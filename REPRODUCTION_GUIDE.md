# Reproduction Guide for DMC Framework Experiments

## Quick Start

### 1. Environment Setup

```bash
# Clone and navigate to the project
cd DMC

# Install dependencies
pip install -r requirements.txt

# Set up API keys
export OPENAI_API_KEY="your_openai_key"
export ANTHROPIC_API_KEY="your_anthropic_key"
export GROQ_API_KEY="your_groq_key"
export GEMINI_API_KEY="your_gemini_key"
```

### 2. Verify Installation

```bash
# Test with a simple problem
python main.py --prompt "What is 15 + 27?" --models openai --task-type gsm8k
```

### 3. Run Sample Experiments

```bash
# Quick test on 3 problems per dataset
bash scripts/run_sample.sh

# Full evaluation (100 problems per dataset)
bash scripts/run_all.sh
```

## Detailed Experiment Reproduction

### Single Dataset Evaluation

```bash
# GSM8K (Mathematical Reasoning)
python main.py --jsonl benchmark/gsm8k.jsonl --models openai anthropic llama --max_problems 100 --output results/gsm8k_result.json

# HumanEval (Code Generation)
python main.py --jsonl benchmark/humaneval.jsonl --models openai anthropic --max_problems 164 --output results/humaneval_result.json

# HotpotQA (Multi-hop Reasoning)
python main.py --jsonl benchmark/hotpotqa.jsonl --models openai anthropic gemini --max_problems 100 --output results/hotpotqa_result.json
```

### Ablation Studies

#### 1. Baseline (Single Agent)
```bash
python main.py --jsonl benchmark/gsm8k.jsonl --baseline-mode --models openai --max_problems 50 --output results/baseline_gsm8k.json
```

#### 2. Without Merge Component
```bash
python main.py --jsonl benchmark/gsm8k.jsonl --disable-merge --models openai anthropic --max_problems 50 --output results/no_merge_gsm8k.json
```

#### 3. Without Annotations
```bash
python main.py --jsonl benchmark/gsm8k.jsonl --disable-annotation --models openai anthropic --max_problems 50 --output results/no_annotation_gsm8k.json
```

#### 4. Without Leader Evaluation
```bash
python main.py --jsonl benchmark/gsm8k.jsonl --disable-leader --models openai anthropic --max_problems 50 --output results/no_leader_gsm8k.json
```

### Evaluation and Metrics

```bash
# Evaluate results
python evaluate.py --results-file results/gsm8k_result.json --dataset gsm8k --output-dir results/evaluation/

# Batch evaluation
bash scripts/evaluate_all.sh
```

## Configuration Options

### Model Selection
```bash
# Use specific models
--models openai anthropic llama gemini

# Use subset of models
--models openai anthropic
```

### Collaboration Settings
```bash
# Adjust collaboration rounds
--max-collaboration-rounds 3

# Set quality thresholds
--quality-threshold 0.8

# Enable/disable features
--context-optimization
--disable-merge
--disable-annotation
--disable-leader
```

### Output Control
```bash
# Custom output location
--output custom/path/results.json

# Limit problem count
--max_problems 50

# Debug mode
--debug
```

## Expected Results Structure

### Output Files
- `results/{dataset}_result.json`: Complete results with metadata
- `results/answers/{dataset}_result_answers.json`: Answer-only format for evaluation
- `results/drafts/{dataset}/`: Detailed collaboration histories
- `results/evaluation/`: Evaluation metrics and analysis

### Result Format
```json
{
  "metadata": {
    "timestamp": "2024-01-01T00:00:00",
    "models_used": ["openai", "anthropic"],
    "task_type": "gsm8k",
    "total_problems": 100
  },
  "results": [
    {
      "prompt": "Question text...",
      "solution": "Final answer",
      "quality_score": 0.85,
      "processing_time": 12.5,
      "collaboration_rounds": 2,
      "efficiency_metrics": {...}
    }
  ]
}
```

## Performance Benchmarks

### Expected Processing Times
- **GSM8K**: ~10-15 seconds per problem
- **HumanEval**: ~15-25 seconds per problem
- **HotpotQA**: ~20-30 seconds per problem
- **MATH**: ~25-35 seconds per problem

### Resource Requirements
- **Memory**: 2-4 GB RAM
- **Storage**: 1-2 GB for full benchmark datasets
- **Network**: Stable internet for API calls

## Troubleshooting

### Common Issues

1. **API Rate Limits**
   ```bash
   # Reduce concurrency
   export MAX_CONCURRENT=5
   ```

2. **Memory Issues**
   ```bash
   # Process smaller batches
   --max_problems 25
   ```

3. **Network Timeouts**
   ```bash
   # Increase timeout in config.py
   "timeout": 60
   ```

### Debug Mode
```bash
# Enable detailed logging
python main.py --debug --jsonl benchmark/gsm8k.jsonl --max_problems 5
```

## Validation

### Quick Validation Test
```bash
# Run minimal test to verify setup
python main.py --prompt "Test question: What is 2+2?" --models openai --task-type gsm8k
```

Expected output should show:
- Successful API connections
- Collaboration workflow execution
- Final answer: "4"

### Full Validation
```bash
# Run sample on all datasets
for dataset in gsm8k math mbpp humaneval hotpotqa strategyqa gpqa mmlu; do
    python main.py --jsonl benchmark/${dataset}.jsonl --max_problems 3 --models openai --output validation/${dataset}_test.json
done
```

## Customization

### Adding New Models
1. Update `config.py` with new provider configuration
2. Implement API client in `utils/api.py`
3. Add model-specific prompts in `prompt.py`

### Adding New Benchmarks
1. Create evaluator in `evaluator/` directory
2. Add dataset-specific configuration in `config.py`
3. Update prompt templates for the new task type

### Modifying Collaboration Strategies
1. Extend `MergeStrategy` enum in `coordination/merge_strategy.py`
2. Implement strategy logic in `LLMDrivenMergeAgent`
3. Update strategy selection criteria

## Citation

If you use this code in your research, please cite our AAAI paper:

```bibtex
@inproceedings{dmc2024,
  title={Dynamic Multi-Agent Collaboration for Complex Reasoning Tasks},
  author={[Authors]},
  booktitle={Proceedings of the AAAI Conference on Artificial Intelligence},
  year={2024}
}
```

## License

This code is provided for research purposes. Please see LICENSE file for details.

---

For additional questions or issues, please refer to the main paper or contact the authors.
