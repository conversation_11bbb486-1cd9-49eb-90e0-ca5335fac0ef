#!/usr/bin/env python3


from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import json
import asyncio
import re

from utils.api import async_generate_completion
from utils.token_utils import Token<PERSON>ou<PERSON>
from config import get_config

class CompressionStrategy(Enum):
    SUMMARIZATION = "summarization"       
    KEY_POINTS = "key_points"           
    HIERARCHICAL = "hierarchical"        
    SELECTIVE_REMOVAL = "selective_removal"  
    SEMANTIC_CLUSTERING = "semantic_clustering"  

class ContentType(Enum):
    DRAFT_CONTENT = "draft_content"
    ANNOTATIONS = "annotations"
    CHANGE_HISTORY = "change_history"
    DISCUSSION = "discussion"
    METADATA = "metadata"

@dataclass
class CompressionResult:
    original_content: str = ""
    compressed_content: str = ""
    original_tokens: int = 0
    compressed_tokens: int = 0
    compression_ratio: float = 0.0
    strategy_used: CompressionStrategy = CompressionStrategy.SUMMARIZATION
    quality_score: float = 0.8
    information_loss: float = 0.2
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())

@dataclass
class ContextBudget:
    max_total_tokens: int = 4000
    draft_content_budget: int = 1500
    annotations_budget: int = 1000
    history_budget: int = 800
    metadata_budget: int = 700
    reserved_tokens: int = 200  

class ContextCompressor:
    
    def __init__(self, compression_agent: str = "openai"):
        self.compression_agent = compression_agent
        self.compression_history: List[CompressionResult] = []
        self.budget = ContextBudget()
        
        self.budget.max_total_tokens = get_config("context_max_tokens", 4000)
        self.budget.draft_content_budget = int(self.budget.max_total_tokens * 0.4)
        self.budget.annotations_budget = int(self.budget.max_total_tokens * 0.3)
        self.budget.history_budget = int(self.budget.max_total_tokens * 0.2)
        self.budget.metadata_budget = int(self.budget.max_total_tokens * 0.1)
        
        self.strategy_preferences = {
            ContentType.DRAFT_CONTENT: CompressionStrategy.SUMMARIZATION,
            ContentType.ANNOTATIONS: CompressionStrategy.KEY_POINTS,
            ContentType.CHANGE_HISTORY: CompressionStrategy.SELECTIVE_REMOVAL,
            ContentType.DISCUSSION: CompressionStrategy.SEMANTIC_CLUSTERING,
            ContentType.METADATA: CompressionStrategy.SELECTIVE_REMOVAL
        }
    
    async def compress_context(self, context_data: Dict[str, Any]) -> Dict[str, Any]:
        
        print("🗜️ Starting context compression...")
        
        usage_analysis = self._analyze_context_usage(context_data)
        print(f"   📊 Current usage: {usage_analysis['total_tokens']} tokens")
        
        if usage_analysis['total_tokens'] <= self.budget.max_total_tokens:
            print("   ✅ Context within budget, no compression needed")
            return context_data
        
        compression_plan = self._create_compression_plan(usage_analysis)
        print(f"   📋 Compression plan: {len(compression_plan)} items to compress")
        
        compressed_context = await self._execute_compression_plan(context_data, compression_plan)
        
        final_usage = self._analyze_context_usage(compressed_context)
        compression_ratio = final_usage['total_tokens'] / usage_analysis['total_tokens']
        
        print(f"   ✅ Compression completed: {usage_analysis['total_tokens']} → {final_usage['total_tokens']} tokens")
        print(f"   📈 Compression ratio: {compression_ratio:.2f}")
        
        return compressed_context
    
    def _analyze_context_usage(self, context_data: Dict[str, Any]) -> Dict[str, Any]:
        usage = {
            'total_tokens': 0,
            'by_type': {},
            'exceeds_budget': False,
            'compression_needed': []
        }
        
        content_types = {
            'draft_content': context_data.get('current_content', ''),
            'annotations': json.dumps(context_data.get('annotations', []), ensure_ascii=False),
            'change_history': json.dumps(context_data.get('change_history', []), ensure_ascii=False),
            'metadata': json.dumps({k: v for k, v in context_data.items() 
                                  if k not in ['current_content', 'annotations', 'change_history']}, 
                                 ensure_ascii=False)
        }
        
        for content_type, content in content_types.items():
            tokens = TokenCounter.count_tokens(content)
            usage['by_type'][content_type] = tokens
            usage['total_tokens'] += tokens
            
            budget_map = {
                'draft_content': self.budget.draft_content_budget,
                'annotations': self.budget.annotations_budget,
                'change_history': self.budget.history_budget,
                'metadata': self.budget.metadata_budget
            }
            
            if tokens > budget_map.get(content_type, 1000):
                usage['compression_needed'].append(content_type)
        
        usage['exceeds_budget'] = usage['total_tokens'] > self.budget.max_total_tokens
        
        return usage
    
    def _create_compression_plan(self, usage_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        plan = []
        
        for content_type in usage_analysis['compression_needed']:
            current_tokens = usage_analysis['by_type'][content_type]
            
            budget_map = {
                'draft_content': self.budget.draft_content_budget,
                'annotations': self.budget.annotations_budget,
                'change_history': self.budget.history_budget,
                'metadata': self.budget.metadata_budget
            }
            
            target_tokens = budget_map.get(content_type, 1000)
            excess_tokens = current_tokens - target_tokens
            
            if excess_tokens > 0:
                plan.append({
                    'content_type': content_type,
                    'current_tokens': current_tokens,
                    'target_tokens': target_tokens,
                    'excess_tokens': excess_tokens,
                    'priority': excess_tokens / current_tokens,  
                    'strategy': self._select_compression_strategy(content_type, current_tokens, target_tokens)
                })
        
        plan.sort(key=lambda x: x['priority'], reverse=True)
        
        return plan
    
    def _select_compression_strategy(self, content_type: str, current_tokens: int, target_tokens: int) -> CompressionStrategy:
        compression_ratio_needed = target_tokens / current_tokens
        
        if compression_ratio_needed < 0.3:
            return CompressionStrategy.KEY_POINTS
        elif compression_ratio_needed < 0.6:
            return CompressionStrategy.SUMMARIZATION
        else:
            return CompressionStrategy.SELECTIVE_REMOVAL
    
    async def _execute_compression_plan(self, context_data: Dict[str, Any], 
                                      compression_plan: List[Dict[str, Any]]) -> Dict[str, Any]:
        
        compressed_context = context_data.copy()
        
        for plan_item in compression_plan:
            content_type = plan_item['content_type']
            strategy = plan_item['strategy']
            target_tokens = plan_item['target_tokens']
            
            print(f"   🗜️ Compressing {content_type} using {strategy.value}...")
            
            if content_type == 'draft_content':
                original_content = compressed_context.get('current_content', '')
            elif content_type == 'annotations':
                original_content = json.dumps(compressed_context.get('annotations', []), ensure_ascii=False)
            elif content_type == 'change_history':
                original_content = json.dumps(compressed_context.get('change_history', []), ensure_ascii=False)
            else:  # metadata
                original_content = json.dumps({k: v for k, v in compressed_context.items() 
                                             if k not in ['current_content', 'annotations', 'change_history']}, 
                                            ensure_ascii=False)
            
            compression_result = await self._compress_content(
                original_content, strategy, target_tokens, content_type
            )
            
            if content_type == 'draft_content':
                compressed_context['current_content'] = compression_result.compressed_content
            elif content_type == 'annotations':
                try:
                    compressed_context['annotations'] = json.loads(compression_result.compressed_content)
                except:
                    pass
            elif content_type == 'change_history':
                try:
                    compressed_context['change_history'] = json.loads(compression_result.compressed_content)
                except:
                    pass
            
            self.compression_history.append(compression_result)
            
            print(f"      ✅ {content_type}: {compression_result.original_tokens} → {compression_result.compressed_tokens} tokens")
        
        return compressed_context
    
    async def _compress_content(self, content: str, strategy: CompressionStrategy, 
                              target_tokens: int, content_type: str) -> CompressionResult:
        
        original_tokens = TokenCounter.count_tokens(content)
        
        if original_tokens <= target_tokens:
            return CompressionResult(
                original_content=content,
                compressed_content=content,
                original_tokens=original_tokens,
                compressed_tokens=original_tokens,
                compression_ratio=1.0,
                strategy_used=strategy
            )
        
        try:
            compression_prompt = self._build_compression_prompt(content, strategy, target_tokens, content_type)
            
            compressed_content = await async_generate_completion(
                agent_id=self.compression_agent,
                prompt=compression_prompt,
                system_prompt="You are an expert at content compression while preserving key information.",
                temperature=0.2,
                max_tokens=target_tokens + 100,  
                skip_cache=True
            )
            
            if not compressed_content:
                compressed_content = self._simple_truncate(content, target_tokens)
            
            compressed_tokens = TokenCounter.count_tokens(compressed_content)
            
            return CompressionResult(
                original_content=content,
                compressed_content=compressed_content,
                original_tokens=original_tokens,
                compressed_tokens=compressed_tokens,
                compression_ratio=compressed_tokens / original_tokens if original_tokens > 0 else 1.0,
                strategy_used=strategy,
                quality_score=0.8,  
                information_loss=1.0 - (compressed_tokens / original_tokens) if original_tokens > 0 else 0.0
            )

        except Exception as e:
            print(f"⚠️ Compression failed: {e}")
            compressed_content = self._simple_truncate(content, target_tokens)
            compressed_tokens = TokenCounter.count_tokens(compressed_content)

            return CompressionResult(
                original_content=content,
                compressed_content=compressed_content,
                original_tokens=original_tokens,
                compressed_tokens=compressed_tokens,
                compression_ratio=compressed_tokens / original_tokens if original_tokens > 0 else 1.0,
                strategy_used=strategy,
                quality_score=0.6,  
                information_loss=1.0 - (compressed_tokens / original_tokens) if original_tokens > 0 else 0.0
            )

    def _build_compression_prompt(self, content: str, strategy: CompressionStrategy,
                                target_tokens: int, content_type: str) -> str:

        base_instruction = f"Compress the following {content_type} to approximately {target_tokens} tokens while preserving the most important information."

        strategy_instructions = {
            CompressionStrategy.SUMMARIZATION: "Create a concise summary that captures the main points and key details.",
            CompressionStrategy.KEY_POINTS: "Extract and list only the most critical key points in bullet format.",
            CompressionStrategy.HIERARCHICAL: "Organize information hierarchically, keeping high-level structure and key details.",
            CompressionStrategy.SELECTIVE_REMOVAL: "Remove redundant, less important, or verbose parts while keeping essential information.",
            CompressionStrategy.SEMANTIC_CLUSTERING: "Group similar information together and represent each cluster concisely."
        }

        content_specific_instructions = {
            'draft_content': "Maintain the logical flow and key arguments. Preserve technical details and conclusions.",
            'annotations': "Keep the most actionable suggestions and critical feedback. Preserve agent attribution.",
            'change_history': "Focus on significant changes and their rationales. Keep version progression clear.",
            'metadata': "Retain essential metadata fields and remove verbose descriptions."
        }

        return f"""{base_instruction}

Strategy: {strategy_instructions[strategy]}
Content-specific guidance: {content_specific_instructions.get(content_type, 'Preserve the most important information.')}

Original content:
{content}

Requirements:
- Target length: ~{target_tokens} tokens
- Maintain information hierarchy
- Preserve key relationships
- Keep essential details
- Remove redundancy

Compressed content:"""

    def _simple_truncate(self, content: str, target_tokens: int) -> str:
        if not content:
            return ""

        # Estimate number of characters to keep
        target_chars = target_tokens * 4  # Rough estimation

        if len(content) <= target_chars:
            return content

        # Try to truncate at paragraph boundaries
        truncated = content[:target_chars]

        # Find the last paragraph separator
        last_paragraph = max(
            truncated.rfind('\n\n'),
            truncated.rfind('\n'),
            truncated.rfind('. '),
            truncated.rfind('。')
        )

        if last_paragraph > target_chars * 0.7:  # If paragraph boundary is not too far
            return truncated[:last_paragraph + 1] + "\n[Content truncated...]"
        else:
            return truncated + "\n[Content truncated...]"

    def get_compression_statistics(self) -> Dict[str, Any]:
        """Get compression statistics"""
        if not self.compression_history:
            return {
                "total_compressions": 0,
                "average_compression_ratio": 1.0,
                "total_tokens_saved": 0,
                "average_quality_score": 0.0
            }

        total_compressions = len(self.compression_history)
        total_original_tokens = sum(result.original_tokens for result in self.compression_history)
        total_compressed_tokens = sum(result.compressed_tokens for result in self.compression_history)
        total_quality = sum(result.quality_score for result in self.compression_history)

        return {
            "total_compressions": total_compressions,
            "average_compression_ratio": total_compressed_tokens / total_original_tokens if total_original_tokens > 0 else 1.0,
            "total_tokens_saved": total_original_tokens - total_compressed_tokens,
            "average_quality_score": total_quality / total_compressions if total_compressions > 0 else 0.0,
            "compression_efficiency": (total_original_tokens - total_compressed_tokens) / total_original_tokens * 100 if total_original_tokens > 0 else 0.0,
            "strategy_usage": self._get_strategy_usage_stats(),
            "recent_compressions": self.compression_history[-5:] if len(self.compression_history) > 5 else self.compression_history
        }

    def _get_strategy_usage_stats(self) -> Dict[str, int]:
        """Get strategy usage statistics"""
        strategy_counts = {}
        for result in self.compression_history:
            strategy = result.strategy_used.value
            strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1
        return strategy_counts

    def update_budget(self, new_budget: Dict[str, int]):
        """Update context budget"""
        if 'max_total_tokens' in new_budget:
            self.budget.max_total_tokens = new_budget['max_total_tokens']
        if 'draft_content_budget' in new_budget:
            self.budget.draft_content_budget = new_budget['draft_content_budget']
        if 'annotations_budget' in new_budget:
            self.budget.annotations_budget = new_budget['annotations_budget']
        if 'history_budget' in new_budget:
            self.budget.history_budget = new_budget['history_budget']
        if 'metadata_budget' in new_budget:
            self.budget.metadata_budget = new_budget['metadata_budget']

    def estimate_compression_need(self, context_data: Dict[str, Any]) -> Dict[str, Any]:
        """Estimate compression requirements"""
        usage_analysis = self._analyze_context_usage(context_data)

        return {
            "compression_needed": usage_analysis['exceeds_budget'],
            "current_usage": usage_analysis['total_tokens'],
            "budget_limit": self.budget.max_total_tokens,
            "excess_tokens": max(0, usage_analysis['total_tokens'] - self.budget.max_total_tokens),
            "compression_targets": usage_analysis['compression_needed'],
            "estimated_savings": self._estimate_compression_savings(usage_analysis)
        }

    def _estimate_compression_savings(self, usage_analysis: Dict[str, Any]) -> Dict[str, int]:
        """Estimate token savings from compression"""
        savings = {}

        for content_type in usage_analysis['compression_needed']:
            current_tokens = usage_analysis['by_type'][content_type]

            budget_map = {
                'draft_content': self.budget.draft_content_budget,
                'annotations': self.budget.annotations_budget,
                'change_history': self.budget.history_budget,
                'metadata': self.budget.metadata_budget
            }

            target_tokens = budget_map.get(content_type, 1000)
            savings[content_type] = max(0, current_tokens - target_tokens)

        return savings
