#!/usr/bin/env python3
"""
协作式共享Draft系统 - 多智能体共同维护的文档
支持批注、版本控制、透明度和LLM驱动的协作
"""

from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import uuid
import json
import difflib
import html
from pathlib import Path
import asyncio
from utils.token_utils import AnnotationLengthController, TokenCounter

class DraftSection(Enum):
    """Draft片段类型"""
    INTRODUCTION = "introduction"
    ANALYSIS = "analysis"
    SOLUTION = "solution"
    CONCLUSION = "conclusion"
    FULL_CONTENT = "full_content"

class DraftStatus(Enum):
    """Draft状态"""
    INITIALIZING = "initializing"
    ACTIVE_COLLABORATION = "active_collaboration"
    CONSENSUS_REACHED = "consensus_reached"
    READY_FOR_MERGE = "ready_for_merge"
    MERGED = "merged"
    FINALIZED = "finalized"

@dataclass
class DraftChange:
    """Draft变更记录"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    agent_id: str = ""
    section: DraftSection = DraftSection.FULL_CONTENT
    change_type: str = ""  # 'add', 'modify', 'delete', 'annotate'
    old_content: str = ""
    new_content: str = ""
    rationale: str = ""
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())
    diff_lines: List[str] = field(default_factory=list)

    def generate_diff(self):
        """生成diff信息"""
        if self.old_content and self.new_content:
            diff = difflib.unified_diff(
                self.old_content.splitlines(keepends=True),
                self.new_content.splitlines(keepends=True),
                fromfile=f"version_{self.id}_old",
                tofile=f"version_{self.id}_new",
                lineterm=""
            )
            self.diff_lines = list(diff)

@dataclass
class DraftAnnotation:
    """Draft批注"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    agent_id: str = ""
    section: DraftSection = DraftSection.FULL_CONTENT
    target_text: str = ""
    annotation_text: str = ""
    annotation_type: str = ""  # 'suggestion', 'correction', 'question', 'enhancement'
    priority: int = 1  # 1-5, 5 is highest priority
    consensus_score: float = 0.0
    responses: List[Dict[str, Any]] = field(default_factory=list)
    status: str = "pending"  # 'pending', 'discussed', 'consensus', 'rejected'
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())

class SharedDraft:
    """
    协作式共享Draft - 多智能体共同维护的文档
    """
    
    def __init__(self, question_id: str, question_content: str, question_type: str = "standard",
                 max_annotation_tokens: int = 100, compression_agent: str = "openai"):
        self.question_id = question_id
        self.question_content = question_content
        self.question_type = question_type

        self.current_content: str = ""
        self.sections: Dict[DraftSection, str] = {
            section: "" for section in DraftSection
        }

        self.status = DraftStatus.INITIALIZING
        self.version = 1
        self.active_agents: List[str] = []
        self.participating_agents: List[str] = []

        self.change_history: List[DraftChange] = []
        self.annotations: List[DraftAnnotation] = []
        self.pending_annotations: List[DraftAnnotation] = []

        self.quality_score: float = 0.0
        self.consensus_level: float = 0.0
        self.total_rounds: int = 0
        self.last_activity: str = datetime.now().isoformat()

        self.transparency_log: List[Dict[str, Any]] = []

        self.annotation_controller = AnnotationLengthController(max_annotation_tokens, compression_agent)
        self.compression_stats: List[Dict[str, Any]] = []
        
    def initialize_draft(self, initial_content: str = "", agent_id: str = "system") -> bool:
        """初始化Draft内容"""
        try:
            if initial_content:
                self.current_content = initial_content
                self.sections[DraftSection.FULL_CONTENT] = initial_content
                self._log_change(agent_id, "initialize", "", initial_content, "Initial draft creation")
            
            self.status = DraftStatus.ACTIVE_COLLABORATION
            self._log_transparency("draft_initialized", {
                "initial_content_length": len(initial_content),
                "agent_id": agent_id
            })
            return True
        except Exception as e:
            self._log_transparency("initialization_failed", {"error": str(e)})
            return False
    
    def add_agent(self, agent_id: str) -> bool:
        """添加协作agent"""
        if agent_id not in self.active_agents:
            self.active_agents.append(agent_id)
        if agent_id not in self.participating_agents:
            self.participating_agents.append(agent_id)
        
        self._log_transparency("agent_joined", {
            "agent_id": agent_id,
            "total_active_agents": len(self.active_agents)
        })
        return True
    
    def remove_agent(self, agent_id: str) -> bool:
        """移除协作agent"""
        if agent_id in self.active_agents:
            self.active_agents.remove(agent_id)
        
        self._log_transparency("agent_left", {
            "agent_id": agent_id,
            "remaining_active_agents": len(self.active_agents)
        })
        return True
    
    async def add_annotation(self, agent_id: str, target_text: str, annotation_text: str,
                           annotation_type: str = "suggestion", priority: int = 1,
                           section: DraftSection = DraftSection.FULL_CONTENT) -> str:
        """添加批注（异步版本，支持长度控制）"""

        processing_result = await self.annotation_controller.process_annotation(
            annotation_text, annotation_type, agent_id
        )

        final_annotation_text = processing_result["final_text"]
        processing_info = processing_result["processing_info"]

        if processing_info["was_processed"]:
            self.compression_stats.append({
                "annotation_id": str(uuid.uuid4()),
                "agent_id": agent_id,
                "annotation_type": annotation_type,
                "original_tokens": processing_info["original_tokens"],
                "final_tokens": processing_info["final_tokens"],
                "compression_ratio": processing_info.get("compression_ratio", 1.0),
                "processing_method": processing_info["processing_method"],
                "timestamp": datetime.now().isoformat()
            })

        annotation = DraftAnnotation(
            agent_id=agent_id,
            section=section,
            target_text=target_text,
            annotation_text=final_annotation_text,
            annotation_type=annotation_type,
            priority=priority
        )

        self.annotations.append(annotation)
        self.pending_annotations.append(annotation)

        self._log_transparency("annotation_added", {
            "annotation_id": annotation.id,
            "agent_id": agent_id,
            "type": annotation_type,
            "priority": priority,
            "target_length": len(target_text),
            "original_annotation_tokens": processing_info["original_tokens"],
            "final_annotation_tokens": processing_info["final_tokens"],
            "was_compressed": processing_info["was_processed"]
        })

        return annotation.id

    def add_annotation_sync(self, agent_id: str, target_text: str, annotation_text: str,
                          annotation_type: str = "suggestion", priority: int = 1,
                          section: DraftSection = DraftSection.FULL_CONTENT) -> str:
        """添加批注（同步版本，用于向后兼容）"""

        original_tokens = TokenCounter.count_tokens(annotation_text)
        max_tokens = self.annotation_controller.max_tokens

        if original_tokens > max_tokens:
            target_chars = max_tokens * 4
            if len(annotation_text) > target_chars:
                annotation_text = annotation_text[:target_chars] + "..."

            final_tokens = TokenCounter.count_tokens(annotation_text)
            self.compression_stats.append({
                "annotation_id": str(uuid.uuid4()),
                "agent_id": agent_id,
                "annotation_type": annotation_type,
                "original_tokens": original_tokens,
                "final_tokens": final_tokens,
                "compression_ratio": final_tokens / original_tokens if original_tokens > 0 else 1.0,
                "processing_method": "sync_truncation",
                "timestamp": datetime.now().isoformat()
            })

        annotation = DraftAnnotation(
            agent_id=agent_id,
            section=section,
            target_text=target_text,
            annotation_text=annotation_text,
            annotation_type=annotation_type,
            priority=priority
        )

        self.annotations.append(annotation)
        self.pending_annotations.append(annotation)

        self._log_transparency("annotation_added", {
            "annotation_id": annotation.id,
            "agent_id": agent_id,
            "type": annotation_type,
            "priority": priority,
            "target_length": len(target_text),
            "annotation_tokens": TokenCounter.count_tokens(annotation_text),
            "was_truncated": original_tokens > max_tokens
        })

        return annotation.id
    
    def respond_to_annotation(self, annotation_id: str, agent_id: str, 
                            response_text: str, agreement_level: float = 0.5) -> bool:
        """回应批注 - 恢复旧版共识机制的回应系统"""
        annotation = self._find_annotation(annotation_id)
        if not annotation:
            return False
        
        existing_response = None
        for resp in annotation.responses:
            if resp["agent_id"] == agent_id:
                existing_response = resp
                break
        
        response = {
            "agent_id": agent_id,
            "response_text": response_text,
            "agreement_level": agreement_level,
            "timestamp": datetime.now().isoformat()
        }
        
        if existing_response:
            annotation.responses.remove(existing_response)
            annotation.responses.append(response)
        else:
            annotation.responses.append(response)
        
        self._update_annotation_consensus(annotation)
        
        self._log_transparency("annotation_response", {
            "annotation_id": annotation_id,
            "responder": agent_id,
            "agreement_level": agreement_level,
            "new_consensus_score": annotation.consensus_score,
            "new_status": annotation.status,
            "is_update": existing_response is not None
        })
        
        return True
    
    def apply_annotation(self, annotation_id: str, agent_id: str) -> bool:
        """应用批注到Draft"""
        annotation = self._find_annotation(annotation_id)
        if not annotation or annotation.status != "consensus":
            return False
        
        if annotation.annotation_type in ["suggestion", "correction", "enhancement"]:
            if annotation.target_text in self.current_content:
                old_content = self.current_content
                new_content = self.current_content.replace(
                    annotation.target_text, 
                    annotation.annotation_text, 
                    1
                )
                
                self.current_content = new_content
                self.sections[DraftSection.FULL_CONTENT] = new_content
                self.version += 1
                
                self._log_change(
                    agent_id, 
                    "apply_annotation", 
                    old_content, 
                    new_content, 
                    f"Applied annotation {annotation_id}"
                )
                
                if annotation in self.pending_annotations:
                    self.pending_annotations.remove(annotation)
                annotation.status = "applied"
                
                self._log_transparency("annotation_applied", {
                    "annotation_id": annotation_id,
                    "applied_by": agent_id,
                    "version": self.version
                })
                
                return True
        
        return False
    
    def update_content(self, agent_id: str, new_content: str, 
                      rationale: str = "", section: DraftSection = DraftSection.FULL_CONTENT) -> bool:
        """更新Draft内容"""
        old_content = self.sections.get(section, "")
        
        if section == DraftSection.FULL_CONTENT:
            old_content = self.current_content
            self.current_content = new_content
        
        self.sections[section] = new_content
        self.version += 1
        self.last_activity = datetime.now().isoformat()
        
        self._log_change(agent_id, "update", old_content, new_content, rationale)
        
        self._log_transparency("content_updated", {
            "agent_id": agent_id,
            "section": section.value,
            "version": self.version,
            "content_length": len(new_content)
        })
        
        return True
    
    def get_annotations_for_consensus(self) -> List[DraftAnnotation]:
        """获取需要达成共识的批注"""
        return [ann for ann in self.pending_annotations if ann.status in ["pending", "discussed"]]
    
    def get_consensus_ready_annotations(self) -> List[DraftAnnotation]:
        """获取已达成共识的批注 - 只有真正达成共识的批注才返回"""
        consensus_annotations = []
        for ann in self.annotations:
            if (ann.status == "consensus" and 
                ann.consensus_score >= 0.8 and 
                len(ann.responses) >= 2):
                consensus_annotations.append(ann)
        
        self._log_transparency("consensus_check", {
            "total_annotations": len(self.annotations),
            "consensus_ready": len(consensus_annotations),
            "consensus_ids": [ann.id for ann in consensus_annotations]
        })
        
        return consensus_annotations
    
    def calculate_consensus_level(self) -> float:
        """计算整体共识水平"""
        if not self.annotations:
            return 1.0
        
        total_consensus = sum(ann.consensus_score for ann in self.annotations)
        max_possible = len(self.annotations) * 1.0
        
        self.consensus_level = total_consensus / max_possible if max_possible > 0 else 1.0
        return self.consensus_level
    
    def is_ready_for_merge(self) -> bool:
        """判断是否准备好进行merge - 必须有共识批注才能merge"""
        consensus_annotations = self.get_consensus_ready_annotations()
        if not consensus_annotations:
            return False
        
        high_priority_pending = [
            ann for ann in self.pending_annotations 
            if ann.priority >= 4 and ann.status == "pending"
        ]
        
        if high_priority_pending:
            return False
        
        consensus = self.calculate_consensus_level()
        if consensus < 0.7:
            return False
        
        if len(self.current_content.strip()) < 10:
            return False
        
        return True
    
    def get_draft_summary(self) -> Dict[str, Any]:
        """获取Draft摘要信息"""
        return {
            "question_id": self.question_id,
            "question_type": self.question_type,
            "status": self.status.value,
            "version": self.version,
            "content_length": len(self.current_content),
            "active_agents": len(self.active_agents),
            "total_annotations": len(self.annotations),
            "pending_annotations": len(self.pending_annotations),
            "consensus_level": self.consensus_level,
            "quality_score": self.quality_score,
            "rounds_completed": self.total_rounds,
            "last_activity": self.last_activity,
            "ready_for_merge": self.is_ready_for_merge()
        }
    
    def get_transparency_info(self) -> Dict[str, Any]:
        """获取透明度信息"""
        return {
            "participating_agents": self.participating_agents,
            "change_history_count": len(self.change_history),
            "annotation_history": [
                {
                    "id": ann.id,
                    "agent": ann.agent_id,
                    "type": ann.annotation_type,
                    "status": ann.status,
                    "consensus_score": ann.consensus_score,
                    "responses_count": len(ann.responses)
                }
                for ann in self.annotations
            ],
            "transparency_log": self.transparency_log[-10:],
            "activity_timeline": [
                {
                    "timestamp": change.timestamp,
                    "agent": change.agent_id,
                    "action": change.change_type
                }
                for change in self.change_history[-5:]
            ]
        }
    
    def export_for_merge(self) -> Dict[str, Any]:
        """导出给Merge Agent的数据"""
        consensus_annotations = self.get_consensus_ready_annotations()
        
        return {
            "draft_content": self.current_content,
            "question_id": self.question_id,
            "question_content": self.question_content,
            "question_type": self.question_type,
            "version": self.version,
            "consensus_annotations": [
                {
                    "id": ann.id,
                    "agent_id": ann.agent_id,
                    "target_text": ann.target_text,
                    "annotation_text": ann.annotation_text,
                    "annotation_type": ann.annotation_type,
                    "priority": ann.priority,
                    "consensus_score": ann.consensus_score
                }
                for ann in consensus_annotations
            ],
            "participating_agents": self.participating_agents,
            "quality_metrics": {
                "consensus_level": self.consensus_level,
                "quality_score": self.quality_score,
                "rounds_completed": self.total_rounds
            },
            "ready_for_merge": self.is_ready_for_merge()
        }
    
    # Private methods
    
    def _find_annotation(self, annotation_id: str) -> Optional[DraftAnnotation]:
        """查找批注"""
        for ann in self.annotations:
            if ann.id == annotation_id:
                return ann
        return None
    
    def _update_annotation_consensus(self, annotation: DraftAnnotation):
        """更新批注的共识分数 - 恢复旧版共识机制"""
        if not annotation.responses:
            annotation.consensus_score = 0.0
            annotation.status = "pending"
            return
        
        total_agreement = sum(resp["agreement_level"] for resp in annotation.responses)
        avg_agreement = total_agreement / len(annotation.responses)
        
        annotation.consensus_score = avg_agreement
        
        if avg_agreement >= 0.8 and len(annotation.responses) >= 2:
            annotation.status = "consensus"
        elif avg_agreement >= 0.6:
            annotation.status = "discussed"
        else:
            annotation.status = "pending"
            
        self._log_transparency("consensus_updated", {
            "annotation_id": annotation.id,
            "consensus_score": avg_agreement,
            "status": annotation.status,
            "responses_count": len(annotation.responses)
        })
    
    def _log_change(self, agent_id: str, change_type: str, old_content: str,
                   new_content: str, rationale: str):
        """记录变更"""
        change = DraftChange(
            agent_id=agent_id,
            change_type=change_type,
            old_content=old_content,
            new_content=new_content,
            rationale=rationale
        )
        change.generate_diff()
        self.change_history.append(change)
    
    def _log_transparency(self, action: str, details: Dict[str, Any]):
        """记录透明度日志"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "action": action,
            "details": details
        }
        self.transparency_log.append(log_entry)
        
        if len(self.transparency_log) > 100:
            self.transparency_log = self.transparency_log[-50:]

    def get_version_diff(self, from_version: int = None, to_version: int = None) -> List[str]:
        """获取版本间的diff"""
        if from_version is None:
            from_version = max(1, self.version - 1)
        if to_version is None:
            to_version = self.version

        relevant_changes = [
            change for change in self.change_history
            if from_version <= self.change_history.index(change) + 1 <= to_version
        ]

        all_diffs = []
        for change in relevant_changes:
            if not change.diff_lines:
                change.generate_diff()
            all_diffs.extend([
                f"=== Change by {change.agent_id} at {change.timestamp} ===",
                f"Rationale: {change.rationale}",
                ""
            ])
            all_diffs.extend(change.diff_lines)
            all_diffs.append("")

        return all_diffs

    def to_markdown(self, include_annotations: bool = True, include_history: bool = False) -> str:
        """将Draft转换为Markdown格式"""
        md_lines = []

        md_lines.extend([
            f"# Draft for Question: {self.question_id}",
            f"**Type:** {self.question_type}",
            f"**Status:** {self.status.value}",
            f"**Version:** {self.version}",
            f"**Last Updated:** {self.last_activity}",
            f"**Participating Agents:** {', '.join(self.participating_agents)}",
            "",
            "## Question",
            self.question_content,
            "",
            "## Current Draft Content",
            self.current_content,
            ""
        ])

        if include_annotations and self.annotations:
            md_lines.extend([
                "## Annotations",
                ""
            ])

            for ann in self.annotations:
                status_emoji = {
                    "pending": "⏳",
                    "discussed": "💬",
                    "consensus": "✅",
                    "applied": "✔️",
                    "rejected": "❌"
                }.get(ann.status, "❓")

                priority_stars = "⭐" * ann.priority

                md_lines.extend([
                    f"### {status_emoji} Annotation by {ann.agent_id} {priority_stars}",
                    f"**Type:** {ann.annotation_type}",
                    f"**Target:** `{ann.target_text[:50]}{'...' if len(ann.target_text) > 50 else ''}`",
                    f"**Suggestion:** {ann.annotation_text}",
                    f"**Consensus Score:** {ann.consensus_score:.2f}",
                    ""
                ])

                if ann.responses:
                    md_lines.append("**Responses:**")
                    for resp in ann.responses:
                        agreement_emoji = "👍" if resp["agreement_level"] > 0.7 else "👎" if resp["agreement_level"] < 0.3 else "🤔"
                        md_lines.append(f"- {agreement_emoji} **{resp['agent_id']}:** {resp['response_text']} (Agreement: {resp['agreement_level']:.2f})")
                    md_lines.append("")

        if include_history and self.change_history:
            md_lines.extend([
                "## Change History",
                ""
            ])

            for i, change in enumerate(self.change_history[-5:], 1):
                md_lines.extend([
                    f"### Change {i} by {change.agent_id}",
                    f"**Time:** {change.timestamp}",
                    f"**Type:** {change.change_type}",
                    f"**Rationale:** {change.rationale}",
                    ""
                ])

                if change.diff_lines:
                    md_lines.extend([
                        "**Diff:**",
                        "```diff"
                    ])
                    md_lines.extend(change.diff_lines)
                    md_lines.extend([
                        "```",
                        ""
                    ])

        md_lines.extend([
            "## Statistics",
            f"- **Content Length:** {len(self.current_content)} characters",
            f"- **Total Annotations:** {len(self.annotations)}",
            f"- **Pending Annotations:** {len(self.pending_annotations)}",
            f"- **Consensus Level:** {self.consensus_level:.2f}",
            f"- **Quality Score:** {self.quality_score:.2f}",
            f"- **Ready for Merge:** {'Yes' if self.is_ready_for_merge() else 'No'}",
            ""
        ])

        return "\n".join(md_lines)

    def to_html(self, include_annotations: bool = True, include_history: bool = False) -> str:
        """将Draft转换为HTML格式，便于可视化展示"""

        css_styles = """
        <style>
        .draft-container { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .status-badge { display: inline-block; padding: 3px 8px; border-radius: 3px; font-size: 12px; font-weight: bold; }
        .status-active { background: #28a745; color: white; }
        .status-ready { background: #17a2b8; color: white; }
        .status-merged { background: #6c757d; color: white; }
        .content-section { background: white; border: 1px solid #ddd; border-radius: 5px; padding: 15px; margin-bottom: 15px; }
        .annotation { border-left: 4px solid #007bff; padding: 10px; margin: 10px 0; background: #f8f9fa; }
        .annotation.suggestion { border-left-color: #28a745; }
        .annotation.correction { border-left-color: #dc3545; }
        .annotation.question { border-left-color: #ffc107; }
        .annotation.enhancement { border-left-color: #17a2b8; }
        .priority-stars { color: #ffc107; }
        .consensus-bar { width: 100%; height: 10px; background: #e9ecef; border-radius: 5px; overflow: hidden; }
        .consensus-fill { height: 100%; background: linear-gradient(90deg, #dc3545 0%, #ffc107 50%, #28a745 100%); }
        .agent-tag { display: inline-block; background: #007bff; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px; margin-right: 5px; }
        .diff-line { font-family: monospace; font-size: 12px; padding: 2px 5px; }
        .diff-add { background: #d4edda; color: #155724; }
        .diff-remove { background: #f8d7da; color: #721c24; }
        .diff-context { background: #f8f9fa; color: #495057; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; }
        .stat-value { font-size: 24px; font-weight: bold; color: #007bff; }
        .stat-label { font-size: 12px; color: #6c757d; text-transform: uppercase; }
        </style>
        """

        html_parts = [
            "<!DOCTYPE html>",
            "<html>",
            "<head>",
            "<meta charset='utf-8'>",
            f"<title>Draft: {self.question_id}</title>",
            css_styles,
            "</head>",
            "<body>",
            "<div class='draft-container'>"
        ]

        status_class = {
            DraftStatus.INITIALIZING: "status-badge",
            DraftStatus.ACTIVE_COLLABORATION: "status-badge status-active",
            DraftStatus.READY_FOR_MERGE: "status-badge status-ready",
            DraftStatus.MERGED: "status-badge status-merged",
            DraftStatus.FINALIZED: "status-badge status-merged"
        }.get(self.status, "status-badge")

        html_parts.extend([
            "<div class='header'>",
            f"<h1>Draft for Question: {html.escape(self.question_id)}</h1>",
            f"<p><strong>Type:</strong> {html.escape(self.question_type)} | ",
            f"<span class='{status_class}'>{self.status.value.upper()}</span> | ",
            f"<strong>Version:</strong> {self.version} | ",
            f"<strong>Last Updated:</strong> {self.last_activity}</p>",
            "<p><strong>Participating Agents:</strong> ",
            " ".join([f"<span class='agent-tag'>{agent}</span>" for agent in self.participating_agents]),
            "</p>",
            "</div>"
        ])

        html_parts.extend([
            "<div class='content-section'>",
            "<h2>Question</h2>",
            f"<p>{html.escape(self.question_content)}</p>",
            "</div>"
        ])

        html_parts.extend([
            "<div class='content-section'>",
            "<h2>Current Draft Content</h2>",
            f"<div style='white-space: pre-wrap; background: #f8f9fa; padding: 15px; border-radius: 3px;'>{html.escape(self.current_content)}</div>",
            "</div>"
        ])

        if include_annotations and self.annotations:
            html_parts.extend([
                "<div class='content-section'>",
                "<h2>Annotations</h2>"
            ])

            for ann in self.annotations:
                status_emoji = {
                    "pending": "⏳",
                    "discussed": "💬",
                    "consensus": "✅",
                    "applied": "✔️",
                    "rejected": "❌"
                }.get(ann.status, "❓")

                priority_stars = "⭐" * ann.priority
                consensus_width = ann.consensus_score * 100

                html_parts.extend([
                    f"<div class='annotation {ann.annotation_type}'>",
                    f"<h4>{status_emoji} Annotation by <span class='agent-tag'>{ann.agent_id}</span> <span class='priority-stars'>{priority_stars}</span></h4>",
                    f"<p><strong>Type:</strong> {ann.annotation_type}</p>",
                    f"<p><strong>Target:</strong> <code>{html.escape(ann.target_text[:100])}{'...' if len(ann.target_text) > 100 else ''}</code></p>",
                    f"<p><strong>Suggestion:</strong> {html.escape(ann.annotation_text)}</p>",
                    f"<p><strong>Consensus Score:</strong> {ann.consensus_score:.2f}</p>",
                    "<div class='consensus-bar'>",
                    f"<div class='consensus-fill' style='width: {consensus_width}%'></div>",
                    "</div>"
                ])

                if ann.responses:
                    html_parts.append("<h5>Responses:</h5><ul>")
                    for resp in ann.responses:
                        agreement_emoji = "👍" if resp["agreement_level"] > 0.7 else "👎" if resp["agreement_level"] < 0.3 else "🤔"
                        html_parts.append(
                            f"<li>{agreement_emoji} <span class='agent-tag'>{resp['agent_id']}</span>: "
                            f"{html.escape(resp['response_text'])} (Agreement: {resp['agreement_level']:.2f})</li>"
                        )
                    html_parts.append("</ul>")

                html_parts.append("</div>")

            html_parts.append("</div>")

        if include_history and self.change_history:
            html_parts.extend([
                "<div class='content-section'>",
                "<h2>Change History</h2>"
            ])

            for i, change in enumerate(self.change_history[-5:], 1):
                html_parts.extend([
                    f"<h4>Change {i} by <span class='agent-tag'>{change.agent_id}</span></h4>",
                    f"<p><strong>Time:</strong> {change.timestamp}</p>",
                    f"<p><strong>Type:</strong> {change.change_type}</p>",
                    f"<p><strong>Rationale:</strong> {html.escape(change.rationale)}</p>"
                ])

                if change.diff_lines:
                    html_parts.append("<h5>Diff:</h5><div style='font-family: monospace; background: #f8f9fa; padding: 10px; border-radius: 3px;'>")
                    for line in change.diff_lines:
                        line_class = "diff-context"
                        if line.startswith('+'):
                            line_class = "diff-add"
                        elif line.startswith('-'):
                            line_class = "diff-remove"
                        html_parts.append(f"<div class='diff-line {line_class}'>{html.escape(line)}</div>")
                    html_parts.append("</div>")

            html_parts.append("</div>")

        html_parts.extend([
            "<div class='content-section'>",
            "<h2>Statistics</h2>",
            "<div class='stats-grid'>",
            f"<div class='stat-card'><div class='stat-value'>{len(self.current_content)}</div><div class='stat-label'>Characters</div></div>",
            f"<div class='stat-card'><div class='stat-value'>{len(self.annotations)}</div><div class='stat-label'>Total Annotations</div></div>",
            f"<div class='stat-card'><div class='stat-value'>{len(self.pending_annotations)}</div><div class='stat-label'>Pending Annotations</div></div>",
            f"<div class='stat-card'><div class='stat-value'>{self.consensus_level:.2f}</div><div class='stat-label'>Consensus Level</div></div>",
            f"<div class='stat-card'><div class='stat-value'>{self.quality_score:.2f}</div><div class='stat-label'>Quality Score</div></div>",
            f"<div class='stat-card'><div class='stat-value'>{'Yes' if self.is_ready_for_merge() else 'No'}</div><div class='stat-label'>Ready for Merge</div></div>",
            "</div>",
            "</div>"
        ])

        html_parts.extend([
            "</div>",
            "</body>",
            "</html>"
        ])

        return "\n".join(html_parts)

    def save_visualization(self, output_dir: str = "results/drafts", format: str = "both") -> Dict[str, str]:
        """保存可视化文件"""
        output_path = Path(output_dir) / self.question_id
        output_path.mkdir(parents=True, exist_ok=True)

        saved_files = {}

        if format in ["markdown", "both"]:
            md_content = self.to_markdown(include_annotations=True, include_history=True)
            md_file = output_path / f"draft_v{self.version}.md"
            with open(md_file, 'w', encoding='utf-8') as f:
                f.write(md_content)
            saved_files['markdown'] = str(md_file)

        if format in ["html", "both"]:
            html_content = self.to_html(include_annotations=True, include_history=True)
            html_file = output_path / f"draft_v{self.version}.html"
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            saved_files['html'] = str(html_file)

        if self.change_history:
            diff_content = "\n".join(self.get_version_diff())
            diff_file = output_path / f"changes_v{self.version}.diff"
            with open(diff_file, 'w', encoding='utf-8') as f:
                f.write(diff_content)
            saved_files['diff'] = str(diff_file)

        return saved_files

    def get_compression_statistics(self) -> Dict[str, Any]:
        """获取注释压缩统计信息"""
        if not self.compression_stats:
            return {
                "total_compressions": 0,
                "average_compression_ratio": 1.0,
                "total_tokens_saved": 0,
                "compression_methods": {}
            }

        total_compressions = len(self.compression_stats)
        total_original_tokens = sum(stat["original_tokens"] for stat in self.compression_stats)
        total_final_tokens = sum(stat["final_tokens"] for stat in self.compression_stats)

        average_compression_ratio = total_final_tokens / total_original_tokens if total_original_tokens > 0 else 1.0
        total_tokens_saved = total_original_tokens - total_final_tokens

        method_stats = {}
        for stat in self.compression_stats:
            method = stat["processing_method"]
            if method not in method_stats:
                method_stats[method] = {
                    "count": 0,
                    "total_original_tokens": 0,
                    "total_final_tokens": 0
                }
            method_stats[method]["count"] += 1
            method_stats[method]["total_original_tokens"] += stat["original_tokens"]
            method_stats[method]["total_final_tokens"] += stat["final_tokens"]

        for method, stats in method_stats.items():
            if stats["total_original_tokens"] > 0:
                stats["compression_ratio"] = stats["total_final_tokens"] / stats["total_original_tokens"]
                stats["tokens_saved"] = stats["total_original_tokens"] - stats["total_final_tokens"]
            else:
                stats["compression_ratio"] = 1.0
                stats["tokens_saved"] = 0

        return {
            "total_compressions": total_compressions,
            "average_compression_ratio": average_compression_ratio,
            "total_tokens_saved": total_tokens_saved,
            "total_original_tokens": total_original_tokens,
            "total_final_tokens": total_final_tokens,
            "compression_methods": method_stats,
            "efficiency_gain": (total_tokens_saved / total_original_tokens * 100) if total_original_tokens > 0 else 0
        }