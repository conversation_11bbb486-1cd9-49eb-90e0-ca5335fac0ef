#!/usr/bin/env python3
"""
Adaptive merge strategy selector
Automatically selects optimal merge strategy based on annotation consistency and conflict severity
"""

from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import json
import asyncio
import numpy as np

from agent.merger.merge_agent import MergeStrategy, ConflictType, LLMDrivenMergeAgent
from coordination.draft import SharedDraft, DraftAnnotation
from utils.api import async_generate_completion
from utils.token_utils import TokenCounter

class AnnotationConsistency(Enum):
    """Annotation consistency levels"""
    HIGH_CONSENSUS = "high_consensus"
    MODERATE_CONSENSUS = "moderate_consensus"
    LOW_CONSENSUS = "low_consensus"
    CONFLICTING = "conflicting"

class ConflictSeverity(Enum):
    """Conflict severity levels"""
    NO_CONFLICT = "no_conflict"
    MINOR_CONFLICT = "minor_conflict"
    MODERATE_CONFLICT = "moderate_conflict"
    MAJOR_CONFLICT = "major_conflict"

@dataclass
class AnnotationAnalysis:
    """Annotation analysis results"""
    total_annotations: int = 0
    consistency_level: AnnotationConsistency = AnnotationConsistency.MODERATE_CONSENSUS
    conflict_severity: ConflictSeverity = ConflictSeverity.NO_CONFLICT
    consensus_score: float = 0.0
    conflict_count: int = 0
    annotation_types: Dict[str, int] = field(default_factory=dict)
    agent_agreement_matrix: Dict[str, Dict[str, float]] = field(default_factory=dict)
    semantic_similarity: float = 0.0
    priority_distribution: Dict[int, int] = field(default_factory=dict)

@dataclass
class StrategyRecommendation:
    """Strategy recommendation results"""
    recommended_strategy: MergeStrategy = MergeStrategy.SEMANTIC_SYNTHESIS
    confidence: float = 0.8
    reasoning: str = ""
    alternative_strategies: List[MergeStrategy] = field(default_factory=list)
    expected_efficiency: float = 0.8
    risk_factors: List[str] = field(default_factory=list)

class AdaptiveMergeStrategySelector:
    """Adaptive merge strategy selector"""
    
    def __init__(self, analysis_agent: str = "openai"):
        self.analysis_agent = analysis_agent
        self.strategy_history: List[Dict[str, Any]] = []
        self.performance_metrics: Dict[MergeStrategy, Dict[str, float]] = {}
        self.agent_authority_history: Dict[str, Dict[str, float]] = {}
        
        self.strategy_weights = {
            "consistency_weight": 0.35,
            "conflict_weight": 0.3,
            "efficiency_weight": 0.15,
            "historical_performance_weight": 0.1,
            "agent_authority_weight": 0.1
        }
        
        self.task_adaptive_weights = {
            "gsm8k": {
                "consistency_weight": 0.4,
                "conflict_weight": 0.25,
                "efficiency_weight": 0.2,
                "historical_performance_weight": 0.1,
                "agent_authority_weight": 0.05
            },
            "math": {
                "consistency_weight": 0.3,
                "conflict_weight": 0.35,
                "efficiency_weight": 0.1,
                "historical_performance_weight": 0.15,
                "agent_authority_weight": 0.1
            },
            "mbpp": {
                "consistency_weight": 0.25,
                "conflict_weight": 0.4,
                "efficiency_weight": 0.2,
                "historical_performance_weight": 0.1,
                "agent_authority_weight": 0.05
            },
            "humaneval": {
                "consistency_weight": 0.25,
                "conflict_weight": 0.4,
                "efficiency_weight": 0.2,
                "historical_performance_weight": 0.1,
                "agent_authority_weight": 0.05
            },
            "hotpotqa": {
                "consistency_weight": 0.35,
                "conflict_weight": 0.3,
                "efficiency_weight": 0.1,
                "historical_performance_weight": 0.15,
                "agent_authority_weight": 0.1
            },
            "strategyqa": {
                "consistency_weight": 0.3,
                "conflict_weight": 0.25,
                "efficiency_weight": 0.15,
                "historical_performance_weight": 0.2,
                "agent_authority_weight": 0.1
            },
            "gpqa": {
                "consistency_weight": 0.25,
                "conflict_weight": 0.35,
                "efficiency_weight": 0.1,
                "historical_performance_weight": 0.15,
                "agent_authority_weight": 0.15
            },
            "mmlu": {
                "consistency_weight": 0.3,
                "conflict_weight": 0.3,
                "efficiency_weight": 0.15,
                "historical_performance_weight": 0.15,
                "agent_authority_weight": 0.1
            }
        }
    
    async def analyze_annotations(self, annotations: List[DraftAnnotation], 
                                draft: SharedDraft) -> AnnotationAnalysis:
        """Analyze annotation consistency and conflict severity"""
        
        if not annotations:
            return AnnotationAnalysis()
        
        analysis = AnnotationAnalysis()
        analysis.total_annotations = len(annotations)
        
        analysis.annotation_types = {}
        analysis.priority_distribution = {}
        
        for ann in annotations:
            ann_type = ann.annotation_type
            analysis.annotation_types[ann_type] = analysis.annotation_types.get(ann_type, 0) + 1
            
            priority = ann.priority
            analysis.priority_distribution[priority] = analysis.priority_distribution.get(priority, 0) + 1
        
        consensus_scores = [ann.consensus_score for ann in annotations if hasattr(ann, 'consensus_score')]
        if consensus_scores:
            analysis.consensus_score = sum(consensus_scores) / len(consensus_scores)
        else:
            analysis.consensus_score = 0.5
        
        if analysis.consensus_score > 0.8:
            analysis.consistency_level = AnnotationConsistency.HIGH_CONSENSUS
        elif analysis.consensus_score > 0.5:
            analysis.consistency_level = AnnotationConsistency.MODERATE_CONSENSUS
        elif analysis.consensus_score > 0.3:
            analysis.consistency_level = AnnotationConsistency.LOW_CONSENSUS
        else:
            analysis.consistency_level = AnnotationConsistency.CONFLICTING
        
        conflicts = await self._detect_semantic_conflicts(annotations, draft)
        analysis.conflict_count = len(conflicts)
        
        if analysis.conflict_count == 0:
            analysis.conflict_severity = ConflictSeverity.NO_CONFLICT
        elif analysis.conflict_count <= 2:
            analysis.conflict_severity = ConflictSeverity.MINOR_CONFLICT
        elif analysis.conflict_count <= 5:
            analysis.conflict_severity = ConflictSeverity.MODERATE_CONFLICT
        else:
            analysis.conflict_severity = ConflictSeverity.MAJOR_CONFLICT
        
        analysis.semantic_similarity = await self._calculate_semantic_similarity(annotations)
        
        analysis.agent_agreement_matrix = self._build_agreement_matrix(annotations)
        
        return analysis
    
    async def recommend_strategy(self, analysis: AnnotationAnalysis, 
                               draft: SharedDraft, task_type: str = None) -> StrategyRecommendation:
        """Recommend optimal merge strategy based on analysis results - high success rate version"""
        
        recommendation = StrategyRecommendation()
        
        if task_type == "gsm8k":
            if analysis.conflict_count > 1 or analysis.consensus_score < 0.7:
                recommendation.recommended_strategy = MergeStrategy.CONFLICT_RESOLUTION
                recommendation.confidence = 0.9
                recommendation.reasoning = "GSM8K requires accurate calculations - using conflict resolution"
            else:
                recommendation.recommended_strategy = MergeStrategy.SEMANTIC_SYNTHESIS
                recommendation.confidence = 0.85
                recommendation.reasoning = "High consensus GSM8K - semantic synthesis for clarity"
                
        elif task_type == "math":
            recommendation.recommended_strategy = MergeStrategy.CONFLICT_RESOLUTION
            recommendation.confidence = 0.95
            recommendation.reasoning = "Complex math requires rigorous conflict resolution"
            
        elif task_type in ["mbpp", "humaneval"]:
            recommendation.recommended_strategy = MergeStrategy.CONFLICT_RESOLUTION
            recommendation.confidence = 0.9
            recommendation.reasoning = "Programming tasks require conflict resolution for code correctness"
            
        elif task_type == "hotpotqa":
            if analysis.consensus_score > 0.75:
                recommendation.recommended_strategy = MergeStrategy.SEMANTIC_SYNTHESIS
                recommendation.confidence = 0.85
                recommendation.reasoning = "Multi-hop reasoning benefits from semantic synthesis"
            else:
                recommendation.recommended_strategy = MergeStrategy.CONFLICT_RESOLUTION
                recommendation.confidence = 0.8
                recommendation.reasoning = "Low consensus in multi-hop reasoning requires conflict resolution"
                
        elif task_type in ["gpqa", "mmlu"]:
            if analysis.conflict_count > 2:
                recommendation.recommended_strategy = MergeStrategy.CONFLICT_RESOLUTION
                recommendation.confidence = 0.9
                recommendation.reasoning = "Scientific reasoning with conflicts requires resolution"
            else:
                recommendation.recommended_strategy = MergeStrategy.PRIORITY_BASED
                recommendation.confidence = 0.8
                recommendation.reasoning = "Scientific reasoning benefits from priority-based merging"
                
        elif task_type == "strategyqa":
            recommendation.recommended_strategy = MergeStrategy.CREATIVE_COMBINATION
            recommendation.confidence = 0.8
            recommendation.reasoning = "Strategic reasoning benefits from creative combination of perspectives"
            
        else:
            if analysis.conflict_count > 3:
                recommendation.recommended_strategy = MergeStrategy.CONFLICT_RESOLUTION
                recommendation.confidence = 0.8
                recommendation.reasoning = f"High conflict count ({analysis.conflict_count}) requires resolution"
            elif analysis.consensus_score > 0.8:
                recommendation.recommended_strategy = MergeStrategy.SEQUENTIAL_INTEGRATION
                recommendation.confidence = 0.9
                recommendation.reasoning = f"High consensus ({analysis.consensus_score:.2f}) enables sequential integration"
            else:
                recommendation.recommended_strategy = MergeStrategy.SEMANTIC_SYNTHESIS
                recommendation.confidence = 0.75
                recommendation.reasoning = "Default semantic synthesis for balanced merge"
        
        recommendation.alternative_strategies = [
            MergeStrategy.SEMANTIC_SYNTHESIS, 
            MergeStrategy.CONFLICT_RESOLUTION,
            MergeStrategy.PRIORITY_BASED
        ]
        recommendation.expected_efficiency = 0.85
        recommendation.risk_factors = self._assess_risk_factors(analysis, task_type)
        
        return recommendation
    
    def _score_by_consistency(self, consistency: AnnotationConsistency) -> Dict[MergeStrategy, float]:
        """Score strategies based on consistency level"""
        scores = {
            MergeStrategy.SEQUENTIAL_INTEGRATION: 0.5,
            MergeStrategy.SEMANTIC_SYNTHESIS: 0.5,
            MergeStrategy.CONFLICT_RESOLUTION: 0.5,
            MergeStrategy.PRIORITY_BASED: 0.5,
            MergeStrategy.CREATIVE_COMBINATION: 0.5
        }
        
        if consistency == AnnotationConsistency.HIGH_CONSENSUS:
            scores[MergeStrategy.SEQUENTIAL_INTEGRATION] = 0.9
            scores[MergeStrategy.SEMANTIC_SYNTHESIS] = 0.8
            scores[MergeStrategy.CREATIVE_COMBINATION] = 0.7
        elif consistency == AnnotationConsistency.MODERATE_CONSENSUS:
            scores[MergeStrategy.SEMANTIC_SYNTHESIS] = 0.9
            scores[MergeStrategy.PRIORITY_BASED] = 0.7
            scores[MergeStrategy.SEQUENTIAL_INTEGRATION] = 0.6
        elif consistency == AnnotationConsistency.LOW_CONSENSUS:
            scores[MergeStrategy.PRIORITY_BASED] = 0.9
            scores[MergeStrategy.CONFLICT_RESOLUTION] = 0.8
            scores[MergeStrategy.SEMANTIC_SYNTHESIS] = 0.6
        else:  # CONFLICTING
            scores[MergeStrategy.CONFLICT_RESOLUTION] = 0.9
            scores[MergeStrategy.PRIORITY_BASED] = 0.7
            scores[MergeStrategy.SEQUENTIAL_INTEGRATION] = 0.3
        
        return scores
    
    def _score_by_conflict_severity(self, severity: ConflictSeverity) -> Dict[MergeStrategy, float]:
        """Score strategies based on conflict severity"""
        scores = {
            MergeStrategy.SEQUENTIAL_INTEGRATION: 0.5,
            MergeStrategy.SEMANTIC_SYNTHESIS: 0.5,
            MergeStrategy.CONFLICT_RESOLUTION: 0.5,
            MergeStrategy.PRIORITY_BASED: 0.5,
            MergeStrategy.CREATIVE_COMBINATION: 0.5
        }
        
        if severity == ConflictSeverity.NO_CONFLICT:
            scores[MergeStrategy.SEQUENTIAL_INTEGRATION] = 0.9
            scores[MergeStrategy.SEMANTIC_SYNTHESIS] = 0.8
            scores[MergeStrategy.CREATIVE_COMBINATION] = 0.8
        elif severity == ConflictSeverity.MINOR_CONFLICT:
            scores[MergeStrategy.SEMANTIC_SYNTHESIS] = 0.9
            scores[MergeStrategy.PRIORITY_BASED] = 0.7
            scores[MergeStrategy.CONFLICT_RESOLUTION] = 0.6
        elif severity == ConflictSeverity.MODERATE_CONFLICT:
            scores[MergeStrategy.CONFLICT_RESOLUTION] = 0.9
            scores[MergeStrategy.PRIORITY_BASED] = 0.8
            scores[MergeStrategy.SEMANTIC_SYNTHESIS] = 0.5
        else:  # MAJOR_CONFLICT
            scores[MergeStrategy.CONFLICT_RESOLUTION] = 0.95
            scores[MergeStrategy.PRIORITY_BASED] = 0.6
            scores[MergeStrategy.SEQUENTIAL_INTEGRATION] = 0.3
        
        return scores
    
    def _score_by_efficiency(self, analysis: AnnotationAnalysis) -> Dict[MergeStrategy, float]:
        """Score strategies based on efficiency considerations"""
        scores = {
            MergeStrategy.SEQUENTIAL_INTEGRATION: 0.8,
            MergeStrategy.SEMANTIC_SYNTHESIS: 0.6,
            MergeStrategy.CONFLICT_RESOLUTION: 0.4,
            MergeStrategy.PRIORITY_BASED: 0.7,
            MergeStrategy.CREATIVE_COMBINATION: 0.3
        }
        
        if analysis.total_annotations > 10:
            scores[MergeStrategy.SEQUENTIAL_INTEGRATION] += 0.1
            scores[MergeStrategy.PRIORITY_BASED] += 0.1
            scores[MergeStrategy.CREATIVE_COMBINATION] -= 0.1
        
        return scores
    
    def _score_by_historical_performance(self) -> Dict[MergeStrategy, float]:
        """Score strategies based on historical performance"""
        scores = {
            MergeStrategy.SEQUENTIAL_INTEGRATION: 0.5,
            MergeStrategy.SEMANTIC_SYNTHESIS: 0.5,
            MergeStrategy.CONFLICT_RESOLUTION: 0.5,
            MergeStrategy.PRIORITY_BASED: 0.5,
            MergeStrategy.CREATIVE_COMBINATION: 0.5
        }
        
        for strategy, metrics in self.performance_metrics.items():
            if metrics.get("success_rate", 0) > 0.8:
                scores[strategy] += 0.2
            elif metrics.get("success_rate", 0) < 0.5:
                scores[strategy] -= 0.2
        
        return scores

    def _score_by_agent_authority(self, analysis: AnnotationAnalysis) -> Dict[MergeStrategy, float]:
        """Score strategies based on agent authority"""
        scores = {
            MergeStrategy.SEQUENTIAL_INTEGRATION: 0.5,
            MergeStrategy.SEMANTIC_SYNTHESIS: 0.5,
            MergeStrategy.CONFLICT_RESOLUTION: 0.5,
            MergeStrategy.PRIORITY_BASED: 0.5,
            MergeStrategy.CREATIVE_COMBINATION: 0.5
        }
        
        agent_agreement = analysis.agent_agreement_matrix
        if agent_agreement:
            authority_variance = self._calculate_authority_variance(agent_agreement)
            
            if authority_variance > 0.3:
                scores[MergeStrategy.PRIORITY_BASED] += 0.2
                scores[MergeStrategy.CONFLICT_RESOLUTION] += 0.15
                scores[MergeStrategy.SEQUENTIAL_INTEGRATION] -= 0.1
            elif authority_variance < 0.1:
                scores[MergeStrategy.SEMANTIC_SYNTHESIS] += 0.15
                scores[MergeStrategy.CREATIVE_COMBINATION] += 0.1
        
        return scores
    
    def _calculate_authority_variance(self, agent_agreement_matrix: Dict[str, Dict[str, float]]) -> float:
        """Calculate agent authority variance"""
        if not agent_agreement_matrix:
            return 0.0
        
        agent_authorities = []
        for agent1 in agent_agreement_matrix:
            avg_agreement = sum(agent_agreement_matrix[agent1].values()) / len(agent_agreement_matrix[agent1])
            agent_authorities.append(avg_agreement)
        
        if len(agent_authorities) < 2:
            return 0.0
        
        mean_authority = sum(agent_authorities) / len(agent_authorities)
        variance = sum((auth - mean_authority) ** 2 for auth in agent_authorities) / len(agent_authorities)
        return variance

    def _get_task_adaptive_weights(self, task_type: str) -> Dict[str, float]:
        """Get adaptive weights based on task type"""
        if task_type and task_type in self.task_adaptive_weights:
            return self.task_adaptive_weights[task_type]
        return self.strategy_weights

    def _score_by_task_type(self, task_type: str) -> Dict[MergeStrategy, float]:
        """Score strategies based on task type"""
        scores = {
            MergeStrategy.SEQUENTIAL_INTEGRATION: 0.5,
            MergeStrategy.SEMANTIC_SYNTHESIS: 0.5,
            MergeStrategy.CONFLICT_RESOLUTION: 0.5,
            MergeStrategy.PRIORITY_BASED: 0.5,
            MergeStrategy.CREATIVE_COMBINATION: 0.5
        }
        
        if task_type == "gsm8k":
            scores[MergeStrategy.SEQUENTIAL_INTEGRATION] = 0.8
            scores[MergeStrategy.SEMANTIC_SYNTHESIS] = 0.7
            scores[MergeStrategy.CONFLICT_RESOLUTION] = 0.6
            scores[MergeStrategy.PRIORITY_BASED] = 0.6
            scores[MergeStrategy.CREATIVE_COMBINATION] = 0.3
        elif task_type == "math":
            scores[MergeStrategy.CONFLICT_RESOLUTION] = 0.9
            scores[MergeStrategy.SEMANTIC_SYNTHESIS] = 0.8
            scores[MergeStrategy.PRIORITY_BASED] = 0.7
            scores[MergeStrategy.SEQUENTIAL_INTEGRATION] = 0.6
            scores[MergeStrategy.CREATIVE_COMBINATION] = 0.4
        elif task_type in ["mbpp", "humaneval"]:
            scores[MergeStrategy.CONFLICT_RESOLUTION] = 0.9
            scores[MergeStrategy.PRIORITY_BASED] = 0.8
            scores[MergeStrategy.SEMANTIC_SYNTHESIS] = 0.7
            scores[MergeStrategy.SEQUENTIAL_INTEGRATION] = 0.6
            scores[MergeStrategy.CREATIVE_COMBINATION] = 0.3
        elif task_type == "hotpotqa":
            scores[MergeStrategy.SEMANTIC_SYNTHESIS] = 0.9
            scores[MergeStrategy.CREATIVE_COMBINATION] = 0.8
            scores[MergeStrategy.CONFLICT_RESOLUTION] = 0.7
            scores[MergeStrategy.PRIORITY_BASED] = 0.6
            scores[MergeStrategy.SEQUENTIAL_INTEGRATION] = 0.5
        elif task_type == "strategyqa":
            scores[MergeStrategy.CREATIVE_COMBINATION] = 0.9
            scores[MergeStrategy.SEMANTIC_SYNTHESIS] = 0.8
            scores[MergeStrategy.PRIORITY_BASED] = 0.7
            scores[MergeStrategy.CONFLICT_RESOLUTION] = 0.6
            scores[MergeStrategy.SEQUENTIAL_INTEGRATION] = 0.5
        elif task_type == "gpqa":
            scores[MergeStrategy.PRIORITY_BASED] = 0.9
            scores[MergeStrategy.CONFLICT_RESOLUTION] = 0.8
            scores[MergeStrategy.SEMANTIC_SYNTHESIS] = 0.7
            scores[MergeStrategy.CREATIVE_COMBINATION] = 0.6
            scores[MergeStrategy.SEQUENTIAL_INTEGRATION] = 0.5
        elif task_type == "mmlu":
            scores[MergeStrategy.SEMANTIC_SYNTHESIS] = 0.8
            scores[MergeStrategy.PRIORITY_BASED] = 0.7
            scores[MergeStrategy.CONFLICT_RESOLUTION] = 0.7
            scores[MergeStrategy.CREATIVE_COMBINATION] = 0.6
            scores[MergeStrategy.SEQUENTIAL_INTEGRATION] = 0.6
        
        return scores

    def _assess_risk_factors(self, analysis: AnnotationAnalysis, task_type: str) -> List[str]:
        """Assess risk factors for strategy execution"""
        risks = []
        
        if task_type in ["gsm8k", "math"] and analysis.conflict_count > 0:
            risks.append("Mathematical conflicts may lead to incorrect calculations")
        
        if task_type in ["mbpp", "humaneval"] and analysis.conflict_count > 1:
            risks.append("Code conflicts may result in syntax errors or incorrect logic")
        
        if task_type == "hotpotqa" and analysis.consensus_score < 0.6:
            risks.append("Low consensus in multi-hop reasoning may miss critical connections")
        
        if analysis.total_annotations > 10:
            risks.append("Large number of annotations may cause information overload")
        
        if analysis.consensus_score < 0.4:
            risks.append("Very low consensus increases merge complexity")
        
        return risks

    async def _detect_semantic_conflicts(self, annotations: List[DraftAnnotation],
                                       draft: SharedDraft) -> List[Dict[str, Any]]:
        """Detect semantic conflicts between annotations"""

        if len(annotations) < 2:
            return []

        conflicts = []

        for i, ann1 in enumerate(annotations):
            for j, ann2 in enumerate(annotations[i+1:], i+1):
                if (ann1.target_text == ann2.target_text and ann1.annotation_text != ann2.annotation_text) or \
                   (ann1.annotation_type == ann2.annotation_type and ann1.priority != ann2.priority):
                    conflict_analysis = await self._llm_detect_conflict(ann1, ann2)
                    if conflict_analysis.get("has_conflict", False):
                        conflicts.append({
                            "type": conflict_analysis.get("conflict_type", "semantic_conflict"),
                            "annotation_1": ann1.id,
                            "annotation_2": ann2.id,
                            "severity": conflict_analysis.get("severity", "moderate"),
                            "description": conflict_analysis.get("conflict_reason", f"Conflicting suggestions for: {ann1.target_text[:50]}..."),
                            "resolution_strategy": conflict_analysis.get("resolution_strategy", "conservative"),
                            "confidence": conflict_analysis.get("confidence", 0.8),
                            "affected_areas": conflict_analysis.get("affected_areas", [])
                        })

        return conflicts

    async def _llm_detect_conflict(self, ann1: DraftAnnotation, ann2: DraftAnnotation) -> Dict[str, Any]:
        """Enhanced conflict detection combining heuristics and lightweight LLM analysis"""
        
        heuristic_conflict = self._heuristic_conflict_detection(ann1, ann2)
        
        if heuristic_conflict or abs(ann1.priority - ann2.priority) > 1:
            try:
                prompt = f"""Quick conflict check between two suggestions:
Suggestion 1: "{ann1.annotation_text[:100]}"
Suggestion 2: "{ann2.annotation_text[:100]}"

Are these suggestions contradictory? Answer: YES/NO
Reason (one sentence):"""

                response = await async_generate_completion(
                    agent_id=self.analysis_agent,
                    prompt=prompt,
                    system_prompt="You are analyzing suggestion conflicts. Be concise.",
                    temperature=0.1,
                    max_tokens=100,
                    skip_cache=True
                )
                
                llm_conflict = "YES" in response.upper()
                reason = response.split("Reason:")[-1].strip() if "Reason:" in response else "LLM conflict analysis"
                
                return {
                    "has_conflict": llm_conflict,
                    "conflict_type": "llm_detected" if llm_conflict else "no_conflict",
                    "severity": "moderate" if llm_conflict else "low",
                    "conflict_reason": reason,
                    "resolution_strategy": "evidence_based" if llm_conflict else "conservative",
                    "confidence": 0.8,
                    "affected_areas": [ann1.target_text[:50]] if llm_conflict else []
                }
                
            except Exception:
                pass
        
        return {
            "has_conflict": heuristic_conflict,
            "conflict_type": "heuristic" if heuristic_conflict else "no_conflict",
            "severity": "moderate" if heuristic_conflict else "low",
            "conflict_reason": "Heuristic conflict detection" if heuristic_conflict else "No conflicts detected",
            "resolution_strategy": "conservative",
            "confidence": 0.7,
            "affected_areas": [ann1.target_text[:50]] if heuristic_conflict else []
        }

    def _heuristic_conflict_detection(self, ann1: DraftAnnotation, ann2: DraftAnnotation) -> bool:
        """Heuristic conflict detection method"""

        if ann1.target_text == ann2.target_text:
            conflict_pairs = [
                ("add", "remove"), ("increase", "decrease"), ("yes", "no"),
                ("correct", "incorrect"), ("good", "bad"), ("should", "shouldn't")
            ]

            text1_lower = ann1.annotation_text.lower()
            text2_lower = ann2.annotation_text.lower()

            for word1, word2 in conflict_pairs:
                if word1 in text1_lower and word2 in text2_lower:
                    return True
                if word2 in text1_lower and word1 in text2_lower:
                    return True

        return False

    async def _calculate_semantic_similarity(self, annotations: List[DraftAnnotation]) -> float:
        """Calculate semantic similarity between annotations"""

        if len(annotations) < 2:
            return 1.0

        all_texts = [ann.annotation_text for ann in annotations]

        word_sets = [set(text.lower().split()) for text in all_texts]

        total_similarity = 0.0
        comparisons = 0

        for i in range(len(word_sets)):
            for j in range(i+1, len(word_sets)):
                intersection = len(word_sets[i] & word_sets[j])
                union = len(word_sets[i] | word_sets[j])
                similarity = intersection / union if union > 0 else 0.0
                total_similarity += similarity
                comparisons += 1

        return total_similarity / comparisons if comparisons > 0 else 0.0

    def _build_agreement_matrix(self, annotations: List[DraftAnnotation]) -> Dict[str, Dict[str, float]]:
        """Build agreement matrix between agents"""

        agents = list(set(ann.agent_id for ann in annotations))
        matrix = {}

        for agent1 in agents:
            matrix[agent1] = {}
            for agent2 in agents:
                if agent1 == agent2:
                    matrix[agent1][agent2] = 1.0
                else:
                    ann1_texts = [ann.annotation_text for ann in annotations if ann.agent_id == agent1]
                    ann2_texts = [ann.annotation_text for ann in annotations if ann.agent_id == agent2]

                    if ann1_texts and ann2_texts:
                        similarity = self._calculate_text_similarity(ann1_texts[0], ann2_texts[0])
                        matrix[agent1][agent2] = similarity
                    else:
                        matrix[agent1][agent2] = 0.5

        return matrix

    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity between two texts"""
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())

        intersection = len(words1 & words2)
        union = len(words1 | words2)

        return intersection / union if union > 0 else 0.0

    def _generate_reasoning(self, analysis: AnnotationAnalysis,
                          strategy_scores: Dict[MergeStrategy, float]) -> str:
        """Generate reasoning explanation for strategy selection"""

        reasoning_parts = []

        consistency_desc = {
            AnnotationConsistency.HIGH_CONSENSUS: "high consensus among annotations",
            AnnotationConsistency.MODERATE_CONSENSUS: "moderate consensus among annotations",
            AnnotationConsistency.LOW_CONSENSUS: "low consensus among annotations",
            AnnotationConsistency.CONFLICTING: "conflicting annotations"
        }
        reasoning_parts.append(f"Detected {consistency_desc[analysis.consistency_level]} (score: {analysis.consensus_score:.2f})")

        conflict_desc = {
            ConflictSeverity.NO_CONFLICT: "no conflicts",
            ConflictSeverity.MINOR_CONFLICT: "minor conflicts",
            ConflictSeverity.MODERATE_CONFLICT: "moderate conflicts",
            ConflictSeverity.MAJOR_CONFLICT: "major conflicts"
        }
        reasoning_parts.append(f"Found {conflict_desc[analysis.conflict_severity]} ({analysis.conflict_count} conflicts)")

        best_strategy = max(strategy_scores.items(), key=lambda x: x[1])[0]
        strategy_reasons = {
            MergeStrategy.SEQUENTIAL_INTEGRATION: "Sequential integration chosen for straightforward, non-conflicting annotations",
            MergeStrategy.SEMANTIC_SYNTHESIS: "Semantic synthesis chosen to intelligently combine related suggestions",
            MergeStrategy.CONFLICT_RESOLUTION: "Conflict resolution chosen to address contradictory annotations",
            MergeStrategy.PRIORITY_BASED: "Priority-based merging chosen to focus on high-importance suggestions",
            MergeStrategy.CREATIVE_COMBINATION: "Creative combination chosen to synthesize diverse perspectives"
        }
        reasoning_parts.append(strategy_reasons[best_strategy])

        return ". ".join(reasoning_parts) + "."

    def _estimate_efficiency(self, strategy: MergeStrategy, analysis: AnnotationAnalysis) -> float:
        """Estimate strategy execution efficiency"""

        base_efficiency = {
            MergeStrategy.SEQUENTIAL_INTEGRATION: 0.9,
            MergeStrategy.SEMANTIC_SYNTHESIS: 0.7,
            MergeStrategy.CONFLICT_RESOLUTION: 0.5,
            MergeStrategy.PRIORITY_BASED: 0.8,
            MergeStrategy.CREATIVE_COMBINATION: 0.4
        }

        efficiency = base_efficiency[strategy]

        if analysis.total_annotations > 10:
            efficiency *= 0.9
        elif analysis.total_annotations < 3:
            efficiency *= 1.1

        if analysis.conflict_count > 5:
            efficiency *= 0.8
        elif analysis.conflict_count == 0:
            efficiency *= 1.1

        return min(0.95, efficiency)

    def _identify_risk_factors(self, analysis: AnnotationAnalysis,
                             strategy: MergeStrategy) -> List[str]:
        """Identify risk factors for strategy execution"""

        risks = []

        if analysis.conflict_count > 5:
            risks.append("High number of conflicts may lead to information loss")

        if analysis.consensus_score < 0.3:
            risks.append("Low consensus may result in inconsistent merge results")

        if analysis.total_annotations > 15:
            risks.append("Large number of annotations may cause processing delays")

        if strategy == MergeStrategy.CREATIVE_COMBINATION and analysis.semantic_similarity < 0.3:
            risks.append("Creative combination with low similarity may produce incoherent results")

        if strategy == MergeStrategy.CONFLICT_RESOLUTION and analysis.conflict_count == 0:
            risks.append("Conflict resolution strategy chosen despite no detected conflicts")

        return risks

    def record_strategy_performance(self, strategy: MergeStrategy, success: bool,
                                  execution_time: float, quality_score: float):
        """Record strategy execution performance"""

        if strategy not in self.performance_metrics:
            self.performance_metrics[strategy] = {
                "total_uses": 0,
                "successes": 0,
                "total_time": 0.0,
                "total_quality": 0.0
            }

        metrics = self.performance_metrics[strategy]
        metrics["total_uses"] += 1
        if success:
            metrics["successes"] += 1
        metrics["total_time"] += execution_time
        metrics["total_quality"] += quality_score

        metrics["success_rate"] = metrics["successes"] / metrics["total_uses"]
        metrics["average_time"] = metrics["total_time"] / metrics["total_uses"]
        metrics["average_quality"] = metrics["total_quality"] / metrics["total_uses"]

        self.strategy_history.append({
            "strategy": strategy.value,
            "success": success,
            "execution_time": execution_time,
            "quality_score": quality_score,
            "timestamp": datetime.now().isoformat()
        })

    def get_strategy_statistics(self) -> Dict[str, Any]:
        """Get strategy usage statistics"""

        return {
            "total_decisions": len(self.strategy_history),
            "performance_by_strategy": {
                strategy.value: metrics for strategy, metrics in self.performance_metrics.items()
            },
            "recent_decisions": self.strategy_history[-10:],
            "strategy_weights": self.strategy_weights
        }

    def get_merge_metadata(self, analysis: AnnotationAnalysis,
                          recommendation: StrategyRecommendation) -> Dict[str, Any]:
        """Get merge metadata for subsequent analysis"""

        return {
            "analysis": {
                "total_annotations": analysis.total_annotations,
                "consistency_level": analysis.consistency_level.value,
                "conflict_severity": analysis.conflict_severity.value,
                "consensus_score": analysis.consensus_score,
                "conflict_count": analysis.conflict_count,
                "semantic_similarity": analysis.semantic_similarity,
                "annotation_types": analysis.annotation_types,
                "priority_distribution": analysis.priority_distribution,
                "agent_agreement_matrix": analysis.agent_agreement_matrix
            },
            "recommendation": {
                "strategy": recommendation.recommended_strategy.value,
                "confidence": recommendation.confidence,
                "reasoning": recommendation.reasoning,
                "alternative_strategies": [s.value for s in recommendation.alternative_strategies],
                "expected_efficiency": recommendation.expected_efficiency,
                "risk_factors": recommendation.risk_factors
            },
            "decision_context": {
                "strategy_weights": self.strategy_weights,
                "historical_performance_available": len(self.performance_metrics) > 0,
                "timestamp": datetime.now().isoformat()
            }
        }
