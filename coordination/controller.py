#!/usr/bin/env python3
"""
Redesigned simplified collaborative controller
Implements correct workflow: Worker collaboration discussion → annotations → Merger fusion → Leader evaluation → feedback loop
Focuses on concise and efficient draft generation with high accuracy
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
import asyncio
import json
import uuid
import os
from pathlib import Path

from utils.api import async_generate_completion
from utils.token_utils import AnnotationLengthController, TokenCounter
from utils.efficiency_tracker import EfficiencyTracker, OperationType
from coordination.merge_strategy import AdaptiveMergeStrategySelector
from coordination.context_compressor import ContextCompressor
from coordination.draft import SharedDraft, DraftAnnotation, DraftSection
from agent.merger.merge_agent import LLMDrivenMergeAgent, MergeStrategy
from agent.worker.worker_annotation_system import WorkerAnnotationSystem, WorkerRole, AnnotationStrategy
from prompt import (
    get_worker_draft_prompt,
    get_worker_collaboration_prompt,
    get_merger_prompt,
    get_leader_evaluation_prompt,
    get_system_prompt,
    get_expected_format,
    create_complete_worker_prompt
)
from config import get_config, get_worker_token_limit, get_annotation_token_limit

class SimplifiedDraft:
    """Simplified Draft class - based on SharedDraft consensus mechanism"""

    def __init__(self, question: str, task_type: str, session_id: str):
        # Core data
        self.question = question
        self.task_type = task_type
        self.session_id = session_id
        
        # Use SharedDraft as underlying implementation
        self.shared_draft = SharedDraft(
            question_id=session_id,
            question_content=question,
            question_type=task_type
        )
        
        # Compatibility attributes
        self.content = ""
        self.annotations = []
        self.version = 1
        self.participants = []
        self.quality_score = 0.0
        self.created_at = datetime.now()

        # Simplified history - only save key information
        self.history = []

    async def add_annotation(self, agent_id: str, annotation: str, annotation_type: str = "suggestion", target_text: str = ""):
        """Add annotation - using SharedDraft consensus mechanism"""

        if not target_text:
            target_text = self.content[:100] if self.content else "full text"
            
        annotation_id = await self.shared_draft.add_annotation(
            agent_id=agent_id,
            target_text=target_text,
            annotation_text=annotation,
            annotation_type=annotation_type,
            priority=3,  # Default medium priority
            section=DraftSection.FULL_CONTENT
        )

        # Sync annotations list for compatibility
        self.annotations.append({
            "id": annotation_id,
            "agent_id": agent_id,
            "text": annotation,
            "type": annotation_type,
            "timestamp": datetime.now().isoformat()
        })

        # Visualize annotation addition
        self._show_annotation_added(agent_id, annotation, annotation_type, len(annotation))
        
        return annotation_id

    def _show_annotation_added(self, agent_id: str, annotation: str, annotation_type: str, original_length: int):
        """Display visualization information for annotation addition"""

        truncated = len(annotation) < original_length
        truncate_info = f" (truncated from {original_length})" if truncated else ""

        print(f"         📝 {agent_id} annotation ({annotation_type}){truncate_info}:")
        preview = annotation[:100] + "..." if len(annotation) > 100 else annotation
        print(f"         💬 \"{preview}\"")
        print()

    def add_annotation_sync(self, agent_id: str, annotation: str, annotation_type: str = "suggestion", target_text: str = ""):
        """Add annotation (sync version) - using SharedDraft sync method"""
        if not target_text:
            target_text = self.content[:100] if self.content else "full text"
            
        annotation_id = self.shared_draft.add_annotation_sync(
            agent_id=agent_id,
            target_text=target_text,
            annotation_text=annotation,
            annotation_type=annotation_type,
            priority=3,  # Default medium priority
            section=DraftSection.FULL_CONTENT
        )

        # Sync annotations list for compatibility
        self.annotations.append({
            "id": annotation_id,
            "agent_id": agent_id,
            "text": annotation,
            "type": annotation_type,
            "timestamp": datetime.now().isoformat()
        })

        # Visualize annotation addition
        self._show_annotation_added(agent_id, annotation, annotation_type, len(annotation))
        
        return annotation_id
        
    def update_content(self, new_content: str, agent_id: str = "merger", phase: str = "update"):
        """Update draft content - using SharedDraft and checking consensus"""

        # Check if there's enough consensus to update content
        if phase == "consensus_merge":
            # This is consensus merge, check if there are consensus annotations
            consensus_annotations = self.shared_draft.get_consensus_ready_annotations()
            if not consensus_annotations:
                print(f"         ⚠️  No consensus annotations available for merge")
                return False
            
            print(f"         ✅ Found {len(consensus_annotations)} consensus annotations, proceeding with merge")
        
        if self.content:
            self.history.append({
                "version": self.version,
                "agent_id": agent_id,
                "phase": phase,
                "timestamp": datetime.now().isoformat(),
                "content_length": len(self.content),
                "content_preview": self.content[:150] + "..." if len(self.content) > 150 else self.content,
                "full_content": self.content
            })

        old_content = self.content
        self.shared_draft.update_content(agent_id, new_content, f"{phase} - {agent_id} updated content")
        
        self.content = new_content
        self.version = self.shared_draft.version
        if agent_id not in self.participants:
            self.participants.append(agent_id)
            self.shared_draft.add_agent(agent_id)

        self._show_draft_update(old_content, new_content, agent_id, phase)
        return True

    def _show_draft_update(self, old_content: str, new_content: str, agent_id: str, phase: str):

        print(f"      📝 Draft Update by {agent_id} ({phase})")
        print(f"         Version: {self.version-1} → {self.version}")

        if not old_content:
            print(f"         📄 Initial content ({len(new_content)} chars):")
            preview = new_content[:200] + "..." if len(new_content) > 200 else new_content
            print(f"         💭 \"{preview}\"")
        else:
            old_len = len(old_content)
            new_len = len(new_content)
            change = new_len - old_len
            change_str = f"+{change}" if change > 0 else str(change)

            print(f"         📊 Content: {old_len} → {new_len} chars ({change_str})")

            if new_content != old_content:
                print(f"         📋 BEFORE: \"{old_content[:100]}{'...' if len(old_content) > 100 else ''}\"")
                print(f"         📋 AFTER:  \"{new_content[:100]}{'...' if len(new_content) > 100 else ''}\"")

                if change < -50:
                    print(f"         🎯 Significant reduction: Likely final answer extraction")
                elif change > 50:
                    print(f"         📈 Significant expansion: Content enhanced/merged")
            else:
                print(f"         ℹ️ Content unchanged")

        print()

            
    def save_final_draft(self, draft_dir: Path):
        try:
            evolution_history = []

            if self.history:
                evolution_history.append({
                    "version": 1,
                    "phase": "initial",
                    "agent_id": "system",
                    "content": "",
                    "content_length": 0,
                    "timestamp": self.created_at.isoformat(),
                    "description": "Initial empty draft"
                })

            for hist in self.history:
                evolution_history.append({
                    "version": hist["version"],
                    "phase": hist["phase"],
                    "agent_id": hist["agent_id"],
                    "content": hist.get("content_preview", ""),
                    "content_length": hist["content_length"],
                    "timestamp": hist["timestamp"],
                    "description": f"{hist['agent_id']} {hist['phase']}"
                })

            evolution_history.append({
                "version": self.version,
                "phase": "final",
                "agent_id": "system",
                "content": self.content,
                "content_length": len(self.content),
                "timestamp": datetime.now().isoformat(),
                "description": "Final approved draft"
            })

            final_data = {
                "session_id": self.session_id,
                "question": self.question,
                "task_type": self.task_type,
                "final_content": self.content,
                "participants": self.participants,
                "annotations": self.annotations,
                "quality_score": self.quality_score,
                "completed_at": datetime.now().isoformat(),
                "evolution_history": evolution_history,
                "summary": {
                    "total_versions": len(evolution_history),
                    "total_annotations": len(self.annotations),
                    "collaboration_phases": list(set(h["phase"] for h in evolution_history))
                }
            }

            draft_dir.mkdir(parents=True, exist_ok=True)
            summary_path = draft_dir / f"{self.session_id}_evolution.json"
            with open(summary_path, 'w', encoding='utf-8') as f:
                json.dump(final_data, f, indent=2, ensure_ascii=False)

            print(f"📁 Draft evolution saved to: {summary_path}")

        except Exception as e:
            print(f"Warning: Failed to save draft evolution: {e}")

    def get_compression_statistics(self) -> Dict[str, Any]:
        return {
            "total_annotations": len(self.annotations),
            "total_versions": len(self.history) + 1,
            "participants": len(self.participants)
        }






            
    def get_summary(self) -> Dict[str, Any]:
        return {
            "session_id": self.session_id,
            "question": self.question,
            "task_type": self.task_type,
            "content": self.content,
            "version": self.version,
            "participants": self.participants,
            "annotations_count": len(self.annotations),
            "quality_score": self.quality_score,
            "consensus_level": self.shared_draft.consensus_level,
            "ready_for_merge": self.shared_draft.is_ready_for_merge()
        }

    def show_current_status(self):
        print(f"      📋 Current Draft Status:")
        print(f"         Version: {self.version}")
        print(f"         Content length: {len(self.content)} chars")
        print(f"         Annotations: {len(self.annotations)}")
        print(f"         Participants: {', '.join(self.participants)}")
        if self.content:
            preview = self.content[:200] + "..." if len(self.content) > 200 else self.content
            print(f"         📄 Content: \"{preview}\"")
        print()
    
    def is_ready_for_consensus_merge(self) -> bool:
        return self.shared_draft.is_ready_for_merge()
    
    def get_consensus_annotations(self) -> List[DraftAnnotation]:
        return self.shared_draft.get_consensus_ready_annotations()
    
    async def respond_to_annotation(self, annotation_id: str, agent_id: str, response_text: str, agreement_level: float) -> bool:
        success = self.shared_draft.respond_to_annotation(annotation_id, agent_id, response_text, agreement_level)
        if success:
            print(f"         👍 {agent_id} responded to annotation with agreement level {agreement_level}")
        return success
    
    def get_pending_annotations(self) -> List[DraftAnnotation]:
        return self.shared_draft.get_annotations_for_consensus()

class SimplifiedCollaborativeController:
    """
    Simplified collaborative controller

    Correct workflow:
    1. Worker collaboration discussion → generate concise draft
    2. Worker add annotations → mark key points
    3. Merger fusion → integrate into complete draft
    4. Leader evaluation → judge correctness
    5. Feedback loop → if incorrect, provide improvement ideas
    """
    
    def __init__(self):
        self.max_rounds = get_config("collaborative_max_rounds", 3)
        self.quality_threshold = get_config("collaborative_quality_threshold", 0.8)
        self.time_limit = get_config("collaborative_max_duration", 60)

        self.strategy_selector = AdaptiveMergeStrategySelector()
        self.merge_agent = LLMDrivenMergeAgent()
        self.merge_statistics = []

        self.context_compressor = ContextCompressor()
        self.compression_statistics = []

        self.efficiency_tracker = EfficiencyTracker()
        
    async def process_task(self, question: str, task_type: str, 
                          available_agents: List[str], problem_id: str = None) -> Dict[str, Any]:
        
        start_time = datetime.now()
        session_id = problem_id or str(uuid.uuid4())
        
        print(f"🚀 Starting Simplified Collaborative Processing")
        print(f"   Session: {session_id}")
        print(f"   Task Type: {task_type}")
        print(f"   Agents: {available_agents}")
        print(f"   Max Rounds: {self.max_rounds}")
        
        try:
            self.efficiency_tracker.start_session(session_id, task_type, problem_id or "")

            draft = SimplifiedDraft(question, task_type, session_id)

            max_rounds = self._determine_rounds_needed(question, task_type)
            print(f"   📊 Estimated rounds needed: {max_rounds}")
            
            evaluation = {'quality_score': 0.5, 'decision': 'needs_minor_revision', 'feedback': 'No evaluation performed'}

            for round_num in range(1, max_rounds + 1):
                self.efficiency_tracker.start_operation(
                    OperationType.COLLABORATION_ROUND,
                    f"round_{round_num}",
                    round_num,
                    TokenCounter.count_tokens(draft.content)
                )
                print(f"\n🔄 Round {round_num}/{max_rounds}")

                if self._check_time_limit(start_time):
                    print("⏱️ Time limit reached, ending collaboration")
                    break

                if round_num > 1:
                    draft = await self._context_compression_phase(draft)

                draft = await self._worker_collaboration_phase(draft, available_agents)
                
                draft = await self._worker_annotation_phase(draft, available_agents)
                
                draft = await self._merger_phase(draft)
                
                draft = await self._post_merge_leader_review_phase(draft)
                
                leader_result = await self._leader_evaluation_and_final_answer_phase(draft)
                evaluation = leader_result['evaluation']
                
                if leader_result.get('final_answer'):
                    draft.update_content(leader_result['final_answer'], "leader", "final_answer_generation")
                
                if 'quality_score' not in evaluation:
                    evaluation['quality_score'] = evaluation.get('overall', evaluation.get('overall_score', 0.5))
                if 'decision' not in evaluation:
                    evaluation['decision'] = 'needs_minor_revision'
                if 'feedback' not in evaluation:
                    evaluation['feedback'] = 'No specific feedback provided'
                
                self.efficiency_tracker.record_context_change(
                    TokenCounter.count_tokens(draft.content)
                )
                self.efficiency_tracker.finish_operation(
                    evaluation['quality_score'],
                    evaluation['decision'] != 'rejected'
                )

                overall_score = evaluation.get('overall', evaluation.get('quality_score', 0.5))
                
                if max_rounds == 1:
                    min_rounds_required = 1
                else:
                    min_rounds_required = max(2, int(max_rounds * 0.6))
                is_early_termination = round_num < min_rounds_required
                
                if evaluation['decision'] == 'approved' and not is_early_termination:
                    print(f"   ✅ Draft approved! Overall Quality: {overall_score:.2f}")
                    break
                elif evaluation['decision'] == 'approved' and is_early_termination:
                    print(f"   🔄 Too early to approve (round {round_num}/{min_rounds_required} min). Continue collaboration...")
                    evaluation['decision'] = 'needs_minor_revision'
                    evaluation['forced_continuation'] = True
                    await self._add_leader_feedback_as_worker_tasks(draft, evaluation)
                elif round_num < max_rounds:
                    print(f"   🔄 Continue to next round based on multi-dimensional feedback")
                    
                    await self._add_leader_feedback_as_worker_tasks(draft, evaluation)
                else:
                    print(f"   ⚠️ Max rounds reached. Using current draft.")
            
            self.efficiency_tracker.finish_session(
                evaluation.get('quality_score', 0.0),
                evaluation.get('decision') != 'rejected'
            )

            total_time = (datetime.now() - start_time).total_seconds()

            draft_dir = Path(f"results/drafts/{draft.task_type}")
            draft.save_final_draft(draft_dir)

            final_answer = evaluation.get('final_answer')
            if not final_answer:
                final_answer = self._extract_final_answer(draft.content, draft.task_type)

            result = self._create_final_result(draft, evaluation, total_time, session_id, final_answer)

            efficiency_summary = self.efficiency_tracker.get_efficiency_summary(task_type)
            result['efficiency_metrics'] = efficiency_summary

            return result
            
        except Exception as e:
            print(f"❌ Collaboration failed: {e}")
            total_time = (datetime.now() - start_time).total_seconds()
            return {
                'success': False,
                'solution': f"Collaboration failed: {str(e)}",
                'total_processing_time': total_time,
                'mode': 'collaborative_error',
                'session_id': session_id
            }
    
    def _determine_rounds_needed(self, question: str, task_type: str) -> int:

        if task_type == 'gsm8k':
            if len(question) > 300:
                return 3
            else:
                return 2
        if task_type == 'strategyqa':
            return 2

        programming_tasks = ['mbpp', 'humaneval']
        if task_type in programming_tasks:
            if task_type == 'mbpp':
                return 1 if len(question) < 300 else 2
            else:  # humaneval
                if len(question) > 400:
                    return 2
                else:
                    return 1

        complex_tasks = ['math', 'hotpotqa', 'gpqa', 'mmlu', 'strategyqa']
        if task_type in complex_tasks:
            if task_type == 'hotpotqa':
                return 4
            elif task_type == 'math':
                if len(question) > 400:
                    return 4
                elif len(question) > 200:
                    return 3
                else:
                    return 2
            elif task_type == 'gpqa':
                return 2
            elif task_type == 'mmlu':
                return 2
            elif task_type == 'strategyqa':
                return 2
            else:
                return 2 if len(question) < 200 else 3

        return 2
    
    def _check_time_limit(self, start_time: datetime) -> bool:
        elapsed = (datetime.now() - start_time).total_seconds()
        return elapsed > self.time_limit
    
    async def _worker_collaboration_phase(self, draft: SimplifiedDraft, 
                                        available_agents: List[str]) -> SimplifiedDraft:
        
        print("   📝 Phase 1: Worker Collaboration")
        
        existing_content = draft.content if draft.content else ""
        
        leader_feedback = ""
        for ann in draft.annotations:
            if ann['agent_id'] == 'leader' and ann['type'] == 'improvement_guidance':
                leader_feedback = ann['text']
                break
        
        tasks = []
        for agent_id in available_agents:
            task = self._generate_worker_draft(
                agent_id, draft.question, draft.task_type, 
                existing_content, leader_feedback
            )
            tasks.append(task)
        
        worker_drafts = await asyncio.gather(*tasks, return_exceptions=True)
        
        best_draft = await self._select_best_draft(worker_drafts, available_agents, draft.task_type)
        draft.update_content(best_draft['content'], best_draft['agent_id'], "worker_collaboration")

        print(f"      ✅ Best draft selected from {best_draft['agent_id']}")
        return draft
    
    async def _generate_worker_draft(self, agent_id: str, question: str, 
                                   task_type: str, existing_content: str = "", 
                                   leader_feedback: str = "") -> Dict[str, Any]:
        
        try:
            if existing_content and leader_feedback:
                prompt = get_worker_collaboration_prompt(
                    'improve',
                    question=question,
                    task_type=task_type,
                    current_draft=existing_content,
                    feedback=leader_feedback,
                    task_prompt=get_worker_draft_prompt(task_type)
                )
            else:
                prompt = create_complete_worker_prompt(task_type, question)
            
            self.efficiency_tracker.start_operation(
                OperationType.WORKER_DRAFT,
                agent_id,
                0,
                TokenCounter.count_tokens(prompt)
            )

            system_prompt = get_system_prompt(task_type)
            task_token_limit = get_worker_token_limit(task_type)
            result = await async_generate_completion(
                agent_id=agent_id,
                prompt=prompt,
                system_prompt=system_prompt,
                max_tokens=task_token_limit,
                temperature=0.3
            )

            self.efficiency_tracker.record_token_usage(
                prompt_text=prompt + system_prompt,
                response_text=result or ""
            )
            
            self.efficiency_tracker.finish_operation(0.8, bool(result))

            return {
                'agent_id': agent_id,
                'content': result or "",
                'success': bool(result)
            }

        except Exception as e:
            print(f"      ❌ {agent_id} draft generation failed: {e}")
            self.efficiency_tracker.finish_operation(0.0, False, str(e))
            return {
                'agent_id': agent_id,
                'content': "",
                'success': False,
                'error': str(e)
            }
    
    async def _select_best_draft(self, worker_drafts: List[Dict[str, Any]], 
                          available_agents: List[str], task_type: str) -> Dict[str, Any]:
        
        valid_drafts = []
        for i, draft in enumerate(worker_drafts):
            if isinstance(draft, Exception):
                continue
            if draft['success'] and draft['content']:
                valid_drafts.append(draft)
        
        if not valid_drafts:
            return {
                'agent_id': available_agents[0],
                'content': "Failed to generate draft",
                'success': False
            }
        
        if len(valid_drafts) > 1:
            best_draft = await self._leader_select_best_draft(valid_drafts, task_type)
        else:
            scored_drafts = []
            for draft in valid_drafts:
                score = self._score_draft_quality(draft['content'], draft['agent_id'], task_type)
                scored_drafts.append((draft, score))
            
            best_draft = max(scored_drafts, key=lambda x: x[1])[0]
        
        print(f"      🎯 Draft Selection: {best_draft['agent_id']} selected")
        return best_draft
    
    async def _leader_select_best_draft(self, valid_drafts: List[Dict[str, Any]], task_type: str) -> Dict[str, Any]:
        
        if len(valid_drafts) == 1:
            return valid_drafts[0]
        
        try:
            drafts_text = ""
            for i, draft in enumerate(valid_drafts, 1):
                drafts_text += f"\n=== DRAFT {i} (by {draft['agent_id']}) ===\n{draft['content']}\n"
            
            review_prompt = self._create_task_specific_review_prompt(task_type, drafts_text)

            leader_agent = get_config("leader_model", "gemini")
            result = await async_generate_completion(
                agent_id=leader_agent,
                prompt=review_prompt,
                system_prompt=self._get_review_system_prompt(task_type),
                max_tokens=800,
                temperature=0.1
            )

            self.efficiency_tracker.record_token_usage(
                prompt_text=review_prompt + self._get_review_system_prompt(task_type),
                response_text=result or ""
            )
            
            print(f"      📋 Leader {task_type.upper()} Review:")
            print(f"         {result[:200]}..." if len(result) > 200 else f"         {result}")
            
            try:
                if "SELECTION:" in result:
                    selection_line = [line for line in result.split('\n') if 'SELECTION:' in line][0]
                    choice_str = selection_line.split('SELECTION:')[1].strip()
                    choice = int(choice_str.split()[0])
                    
                    if 1 <= choice <= len(valid_drafts):
                        print(f"      🎯 Leader selected draft {choice} after {task_type} review")
                        return valid_drafts[choice - 1]
                
                import re
                numbers = re.findall(r'\b([1-9])\b', result)
                if numbers:
                    choice = int(numbers[-1])
                    if 1 <= choice <= len(valid_drafts):
                        print(f"      🎯 Leader selected draft {choice} (fallback parsing)")
                        return valid_drafts[choice - 1]
            except Exception as parse_error:
                print(f"      ⚠️ Selection parsing failed: {parse_error}")
                
        except Exception as e:
            print(f"      ⚠️ Leader {task_type} review failed, using fallback: {e}")
        
        print(f"      🎯 Using fallback: selecting first valid draft")
        return valid_drafts[0]
    
    def _create_task_specific_review_prompt(self, task_type: str, drafts_text: str) -> str:
        
        if task_type == 'math':
            return f"""You are conducting a comprehensive mathematical solution review to select the BEST draft.
Perform rigorous analysis on each mathematical approach.

MATHEMATICAL REVIEW CRITERIA:
1. **Mathematical Correctness**: Are all mathematical steps logically sound?
2. **Proof Validity**: Is the mathematical reasoning rigorous and complete?
3. **Calculation Accuracy**: Are all computations correct?
4. **Logical Flow**: Does each step follow logically from the previous?
5. **Completeness**: Are all necessary steps included?
6. **Insight Quality**: Does the solution demonstrate proper mathematical understanding?
7. **Alternative Methods**: Are multiple valid approaches considered?

AVAILABLE SOLUTIONS:{drafts_text}

PERFORM DETAILED MATHEMATICAL REVIEW:
For each draft, analyze:
- Mathematical rigor and correctness
- Quality of reasoning and proof structure  
- Computational accuracy
- Completeness of solution steps
- Level of mathematical insight demonstrated

After thorough mathematical analysis, select the BEST solution.

FORMAT YOUR RESPONSE:
```
DRAFT 1 REVIEW:
[Mathematical analysis of draft 1]

DRAFT 2 REVIEW:
[Mathematical analysis of draft 2]

SELECTION: [number]
REASONING: [Why this mathematical solution is superior]
```"""

        elif task_type in ['mbpp', 'humaneval']:
            return f"""You are conducting a comprehensive code review to select the BEST programming solution.
Perform detailed analysis on each implementation.

PROGRAMMING REVIEW CRITERIA:
1. **Functional Correctness**: Does the code solve the problem correctly?
2. **Edge Case Handling**: Are all edge cases properly handled?
3. **Code Quality**: Clean, readable, well-structured code?
4. **Error Handling**: Appropriate validation and error handling?
5. **Performance**: Efficient algorithm and implementation?
6. **Best Practices**: Follows programming conventions?
7. **Completeness**: No missing functionality or truncated code?

AVAILABLE IMPLEMENTATIONS:{drafts_text}

PERFORM DETAILED CODE REVIEW:
For each draft, analyze:
- Functional correctness and algorithm quality
- Edge case coverage and error handling
- Code readability and structure
- Performance implications
- Adherence to best practices

After thorough code analysis, select the BEST implementation.

FORMAT YOUR RESPONSE:
```
DRAFT 1 REVIEW:
[Code analysis of draft 1]

DRAFT 2 REVIEW:
[Code analysis of draft 2]

SELECTION: [number]
REASONING: [Why this code implementation is superior]
```"""

        elif task_type in ['gsm8k']:
            return f"""You are conducting a comprehensive arithmetic reasoning review to select the BEST solution.
Focus on step-by-step mathematical accuracy and logical flow.

ARITHMETIC REASONING REVIEW CRITERIA:
1. **Calculation Accuracy**: Are all arithmetic operations correct?
2. **Step-by-Step Logic**: Does each calculation step follow logically?
3. **Problem Understanding**: Is the word problem interpreted correctly?
4. **Solution Completeness**: Are all required steps included?
5. **Final Answer Correctness**: Is the final numerical result accurate?
6. **Clarity**: Is the solution path clear and easy to follow?

AVAILABLE SOLUTIONS:{drafts_text}

PERFORM DETAILED ARITHMETIC REVIEW:
For each draft, analyze:
- Accuracy of all mathematical calculations
- Logical progression of solution steps
- Correct interpretation of the word problem
- Completeness of the solution approach
- Clarity of mathematical reasoning

After thorough arithmetic analysis, select the BEST solution.

FORMAT YOUR RESPONSE:
```
DRAFT 1 REVIEW:
[Arithmetic analysis of draft 1]

DRAFT 2 REVIEW:
[Arithmetic analysis of draft 2]

SELECTION: [number]
REASONING: [Why this arithmetic solution is superior]
```"""

        elif task_type == 'hotpotqa':
            return f"""You are conducting a comprehensive reasoning analysis to select the BEST solution.
Focus on logical reasoning chains and factual accuracy.

REASONING REVIEW CRITERIA:
1. **Logical Reasoning**: Is the reasoning chain sound and complete?
2. **Factual Accuracy**: Are all facts and information correctly extracted?
3. **Evidence Integration**: Are multiple sources properly synthesized?
4. **Inference Quality**: Are conclusions properly drawn from evidence?
5. **Completeness**: Are all necessary reasoning steps included?
6. **Answer Relevance**: Does the final answer directly address the question?

AVAILABLE SOLUTIONS:{drafts_text}

PERFORM DETAILED REASONING REVIEW:
For each draft, analyze:
- Quality and completeness of reasoning chain
- Accuracy of factual information extraction
- Integration of multiple information sources
- Soundness of logical inferences
- Relevance and accuracy of final answer

After thorough reasoning analysis, select the BEST solution.

FORMAT YOUR RESPONSE:
```
DRAFT 1 REVIEW:
[Reasoning analysis of draft 1]

DRAFT 2 REVIEW:
[Reasoning analysis of draft 2]

SELECTION: [number]
REASONING: [Why this reasoning solution is superior]
```"""

        elif task_type == 'gpqa':
            return f"""You are conducting a comprehensive scientific analysis to select the BEST solution.
Focus on scientific accuracy and expert-level reasoning.

SCIENTIFIC REVIEW CRITERIA:
1. **Scientific Accuracy**: Are all scientific concepts correctly applied?
2. **Domain Expertise**: Does the solution demonstrate appropriate scientific knowledge?
3. **Reasoning Quality**: Is the scientific reasoning sound and complete?
4. **Methodology**: Are appropriate scientific methods and principles used?
5. **Evidence Evaluation**: Are scientific facts and data properly interpreted?
6. **Conclusion Validity**: Are scientific conclusions properly supported?

AVAILABLE SOLUTIONS:{drafts_text}

PERFORM DETAILED SCIENTIFIC REVIEW:
For each draft, analyze:
- Accuracy of scientific concepts and principles
- Depth of domain-specific knowledge
- Quality of scientific reasoning
- Appropriateness of methodology
- Validity of scientific conclusions

After thorough scientific analysis, select the BEST solution.

FORMAT YOUR RESPONSE:
```
DRAFT 1 REVIEW:
[Scientific analysis of draft 1]

DRAFT 2 REVIEW:
[Scientific analysis of draft 2]

SELECTION: [number]
REASONING: [Why this scientific solution is superior]
```"""

        else:
            return f"""You are conducting a comprehensive solution review to select the BEST draft.
Perform detailed analysis on each approach.

GENERAL REVIEW CRITERIA:
1. **Correctness**: Does the solution correctly address the problem?
2. **Completeness**: Are all aspects of the problem addressed?
3. **Clarity**: Is the solution clear and well-structured?
4. **Logic**: Is the reasoning sound and well-organized?
5. **Accuracy**: Are all facts and information correct?
6. **Relevance**: Does the answer directly address the question?

AVAILABLE SOLUTIONS:{drafts_text}

PERFORM DETAILED REVIEW:
For each draft, analyze:
- Correctness and accuracy of the solution
- Completeness of the approach
- Clarity and organization
- Quality of reasoning
- Overall effectiveness

After thorough analysis, select the BEST solution.

FORMAT YOUR RESPONSE:
```
DRAFT 1 REVIEW:
[Analysis of draft 1]

DRAFT 2 REVIEW:
[Analysis of draft 2]

SELECTION: [number]
REASONING: [Why this solution is superior]
```"""

    def _get_review_system_prompt(self, task_type: str) -> str:
        
        prompts = {
            'math': "You are a mathematics professor with expertise in mathematical proofs and problem-solving. Conduct rigorous mathematical analysis.",
            'gsm8k': "You are an arithmetic reasoning expert specializing in grade school math problems. Focus on calculation accuracy and logical flow.",
            'mbpp': "You are a senior Python developer conducting code reviews. Focus on code correctness, efficiency, and best practices.",
            'humaneval': "You are a senior software engineer conducting thorough code reviews. Analyze implementations for correctness and quality.",
            'hotpotqa': "You are a reasoning expert specializing in multi-hop logical inference. Focus on reasoning chain quality and factual accuracy.",
            'gpqa': "You are a scientific expert with graduate-level knowledge across physics, chemistry, and biology. Focus on scientific accuracy.",
            'strategyqa': "You are a strategic reasoning expert. Focus on implicit knowledge application and logical analysis.",
            'mmlu': "You are an academic expert across multiple disciplines. Focus on domain knowledge and comprehensive understanding."
        }
        
        return prompts.get(task_type, "You are an expert analyst conducting comprehensive solution reviews. Focus on accuracy, completeness, and quality.")
    
    async def _worker_annotation_phase(self, draft: SimplifiedDraft,
                                     available_agents: List[str]) -> SimplifiedDraft:

        print("   📋 Phase 2: Worker Collaborative Annotations")
        
        if not get_config("enhanced_enable_annotation", True):
            print("      🔬 Annotation system disabled - skipping annotation phase")
            return draft

        for agent_id in available_agents:
            try:
                peer_annotations = [ann for ann in draft.annotations if ann['agent_id'] != agent_id]

                annotation = await self._generate_simple_annotation(agent_id, draft, peer_annotations)
                if annotation and annotation.strip():
                    draft.add_annotation(agent_id, annotation, "suggestion")
                    print(f"      ✅ {agent_id} added annotation")

            except Exception as e:
                print(f"      ❌ {agent_id} annotation failed: {e}")

        print(f"      📊 Worker Annotations: {len(draft.annotations)} suggestions")
        return draft

    async def _generate_simple_annotation(self, agent_id: str, draft: SimplifiedDraft,
                                        peer_annotations: List[Dict]) -> str:
        try:
            peer_summary = ""
            if peer_annotations:
                peer_summary = "\n".join([f"- {ann['agent_id']}: {ann['text'][:100]}" for ann in peer_annotations[-2:]])

            prompt = f"""Review this draft and provide ONE brief improvement suggestion (max 50 words):

DRAFT:
{draft.content}

PEER SUGGESTIONS:
{peer_summary}

Your suggestion (be concise and specific):"""

            annotation_token_limit = get_annotation_token_limit(draft.task_type)
            response = await async_generate_completion(
                agent_id=agent_id,
                prompt=prompt,
                max_tokens=annotation_token_limit,
                temperature=0.3
            )

            self.efficiency_tracker.record_token_usage(
                prompt_text=prompt,
                response_text=response or ""
            )

            return response.strip() if response else ""

        except Exception as e:
            print(f"      ⚠️ {agent_id} annotation generation failed: {e}")
            return ""

    async def _generate_annotation_with_leader_tasks(self, agent_id: str, draft: SimplifiedDraft,
                                                   peer_annotations: List[Dict], leader_tasks: List[Dict]) -> str:
        
        leader_guidance = ""
        if leader_tasks:
            high_priority_tasks = [task for task in leader_tasks if task['type'] in ['high_priority_fix', 'targeted_improvement']]
            general_guidance = [task for task in leader_tasks if task['type'] in ['improvement_guidance', 'dimension_guidance']]
            
            if high_priority_tasks:
                leader_guidance += "\n\nLEADER PRIORITY TASKS FOR YOU:\n"
                for i, task in enumerate(high_priority_tasks, 1):
                    leader_guidance += f"{i}. {task['text']}\n"
            
            if general_guidance:
                leader_guidance += "\nLEADER GENERAL GUIDANCE:\n"
                for task in general_guidance:
                    leader_guidance += f"- {task['text'][:150]}\n"
            
            leader_guidance += "\nPRIORITIZE addressing Leader's specific feedback above."
        
        peer_summary = ""
        if peer_annotations:
            peer_summary = "\n\nPEER SUGGESTIONS YOU CAN SEE:\n"
            for i, ann in enumerate(peer_annotations, 1):
                peer_summary += f"{i}. {ann['agent_id']}: {ann['text'][:100]}\n"
            peer_summary += "\nIf you disagree with peers, provide a counter-annotation. If you agree, you can reference their suggestion."
        
        prompt = f"""Analyze this draft and provide ONE brief, actionable suggestion for improvement.

PROBLEM: {draft.question}
TASK TYPE: {draft.task_type}
CURRENT DRAFT: {draft.content}{leader_guidance}{peer_summary}

Focus priorities (in order):
1. ADDRESS LEADER'S SPECIFIC FEEDBACK FIRST (if any)
2. Accuracy and correctness
3. Missing steps or information  
4. Format compliance
5. Consider peers' suggestions but provide your independent analysis

Provide only ONE specific suggestion (max 50 words):"""
        
        try:
            system_prompt = get_system_prompt(draft.task_type)
            result = await async_generate_completion(
                agent_id=agent_id,
                prompt=prompt,
                system_prompt=system_prompt,
                max_tokens=100,
                temperature=0.2
            )

            self.efficiency_tracker.record_token_usage(
                prompt_text=prompt + system_prompt,
                response_text=result or ""
            )

            return result.strip() if result else ""
            
        except Exception as e:
            print(f"      ❌ {agent_id} annotation generation failed: {e}")
            return ""

    async def _generate_annotation_with_peers(self, agent_id: str, draft: SimplifiedDraft, peer_annotations: List[Dict]) -> str:
        return await self._generate_annotation_with_leader_tasks(agent_id, draft, peer_annotations, [])
    
    async def _generate_consensus_response(self, agent_id: str, draft: SimplifiedDraft, others_annotations: List[Dict]) -> str:
        
        others_summary = "\n\nPEERS' SUGGESTIONS TO RESPOND TO:\n"
        for i, ann in enumerate(others_annotations, 1):
            others_summary += f"[{i}] {ann['agent_id']}: {ann['text']}\n"
        
        prompt = f"""Review your peers' suggestions and provide consensus feedback.

PROBLEM: {draft.question}
CURRENT DRAFT: {draft.content}{others_summary}

Instructions:
- If you AGREE with a peer's suggestion, respond: "AGREE(suggestion_{i}): [brief reason]"
- If you DISAGREE or have improvements, provide: "COUNTER: [your alternative suggestion in max 50 words]"
- Focus on reaching consensus while maintaining quality

Your response:"""
        
        try:
            system_prompt = "You are a collaborative worker seeking consensus with peers while maintaining high standards."
            result = await async_generate_completion(
                agent_id=agent_id,
                prompt=prompt,
                system_prompt=system_prompt,
                max_tokens=120,
                temperature=0.1
            )
            
            return result.strip() if result else ""
            
        except Exception as e:
            print(f"      ❌ {agent_id} consensus response failed: {e}")
            return ""

    def _analyze_consensus_and_get_conflicts(self, draft: SimplifiedDraft, consensus_responses: Dict[str, str], available_agents: List[str]) -> List[str]:
        
        span_stats = {}
        conflict_workers = set()
        
        for i, ann in enumerate(draft.annotations):
            if ann['type'] == 'suggestion':
                span_id = f"suggestion_{i+1}"
                span_stats[span_id] = {
                    'total_responses': 0,
                    'agree_count': 0,
                    'counter_count': 0,
                    'agree_agents': [],
                    'counter_agents': [],
                    'original_agent': ann['agent_id']
                }
        
        for agent_id, response in consensus_responses.items():
            if not response:
                continue
                
            if response.startswith("AGREE"):
                import re
                match = re.search(r'AGREE\(suggestion_(\d+)\)', response)
                if match:
                    span_id = f"suggestion_{match.group(1)}"
                    if span_id in span_stats:
                        span_stats[span_id]['total_responses'] += 1
                        span_stats[span_id]['agree_count'] += 1
                        span_stats[span_id]['agree_agents'].append(agent_id)
                        
            elif response.startswith("COUNTER"):
                for span_id in span_stats:
                    span_stats[span_id]['total_responses'] += 1
                    span_stats[span_id]['counter_count'] += 1
                    span_stats[span_id]['counter_agents'].append(agent_id)
                    conflict_workers.add(agent_id)
        
        total_agents = len(available_agents)
        required_consensus = max(2, int(total_agents * 2 / 3))
        
        print(f"      📈 Consensus Analysis (need {required_consensus}/{total_agents} agreement):")
        
        for span_id, stats in span_stats.items():
            agree_ratio = stats['agree_count'] / total_agents if total_agents > 0 else 0
            print(f"         {span_id}: {stats['agree_count']} agree, {stats['counter_count']} counter (ratio: {agree_ratio:.2f})")
            
            if stats['agree_count'] < required_consensus:
                conflict_workers.update(stats['counter_agents'])
                conflict_workers.add(stats['original_agent'])
        
        return list(conflict_workers)
    
    async def _resolve_conflicts_discussion(self, draft: SimplifiedDraft, conflict_workers: List[str]):
        
        print(f"      🔥 Conflict resolution discussion with: {conflict_workers}")
        
        conflict_summary = self._build_conflict_summary(draft)
        
        for agent_id in conflict_workers:
            try:
                resolution_response = await self._generate_conflict_resolution_response(
                    agent_id, draft, conflict_summary, conflict_workers
                )
                if resolution_response:
                    draft.add_annotation(agent_id, resolution_response, "conflict_resolution")
                    print(f"      🔧 {agent_id} provided conflict resolution")
            except Exception as e:
                print(f"      ❌ {agent_id} conflict resolution failed: {e}")
    
    def _build_conflict_summary(self, draft: SimplifiedDraft) -> str:
        conflicts = []
        suggestion_count = 0
        counter_count = 0
        
        for ann in draft.annotations:
            if ann['type'] == 'suggestion':
                suggestion_count += 1
                conflicts.append(f"Suggestion {suggestion_count}: {ann['text'][:100]}")
            elif ann['type'] == 'consensus_response' and ann['text'].startswith("COUNTER"):
                counter_count += 1
                conflicts.append(f"Counter {counter_count}: {ann['text'][:100]}")
        
        return "\n".join(conflicts)
    
    async def _generate_conflict_resolution_response(self, agent_id: str, draft: SimplifiedDraft, 
                                                   conflict_summary: str, conflict_workers: List[str]) -> str:
        
        prompt = f"""You are in a conflict resolution discussion. Help reach consensus on the disputed suggestions.

PROBLEM: {draft.question}
CURRENT DRAFT: {draft.content}

CONFLICTS TO RESOLVE:
{conflict_summary}

PARTICIPANTS IN THIS DISCUSSION: {', '.join(conflict_workers)}

Your task:
1. Identify the core disagreement
2. Propose a compromise solution that addresses everyone's concerns
3. Be specific and constructive

Provide a compromise suggestion (max 80 words):"""
        
        try:
            system_prompt = "You are a diplomatic problem-solver focused on finding win-win solutions."
            result = await async_generate_completion(
                agent_id=agent_id,
                prompt=prompt,
                system_prompt=system_prompt,
                max_tokens=150,
                temperature=0.1
            )
            
            return result.strip() if result else ""
            
        except Exception as e:
            print(f"      ❌ {agent_id} conflict resolution generation failed: {e}")
            return ""

    async def _add_leader_feedback_as_worker_tasks(self, draft: SimplifiedDraft, evaluation: Dict[str, Any]):
        
        overall_feedback = f"Leader Quality Assessment - Overall: {evaluation.get('overall', 0.6):.2f}"
        dimensions = []
        for dim in ['accuracy', 'completeness', 'clarity']:
            if dim in evaluation:
                dimensions.append(f"{dim}: {evaluation[dim]:.2f}")
        if dimensions:
            overall_feedback += f" | Dimensions: {', '.join(dimensions)}"
        
        if evaluation.get('improvement_priorities'):
            overall_feedback += f"\nPriority Areas: {', '.join(evaluation['improvement_priorities'])}"
        
        draft.add_annotation("leader", overall_feedback, "improvement_guidance")
        print(f"      📌 Added overall improvement guidance")
        
        if evaluation.get('feedback_annotations'):
            print(f"      🎯 Converting {len(evaluation['feedback_annotations'])} feedback items to targeted tasks")
            
            for i, annotation in enumerate(evaluation['feedback_annotations'], 1):
                span_id = annotation.get('span_id', f'issue_{i}')
                issue_type = annotation.get('issue_type', 'general')
                comment = annotation.get('comment', 'No specific comment')
                severity = annotation.get('severity', 'medium')
                
                task_description = f"[{severity.upper()} {issue_type.upper()}] {span_id}: {comment}"
                
                task_type = "high_priority_fix" if severity == "high" else "targeted_improvement"
                
                draft.add_annotation("leader", task_description, task_type)
                
                severity_icon = {'high': '🔴', 'medium': '🟡', 'low': '🟢'}.get(severity, '🟡')
                print(f"         {i}. {severity_icon} Targeted task: {issue_type} in {span_id}")
        
        dimension_feedback = []
        for dim_name, threshold in [('accuracy', 0.8), ('completeness', 0.75), ('clarity', 0.7)]:
            dim_score = evaluation.get(dim_name, 0.6)
            if dim_score < threshold:
                improvement_msg = f"Focus on improving {dim_name} (current: {dim_score:.2f}, target: {threshold:.2f})"
                dimension_feedback.append(improvement_msg)
        
        if dimension_feedback:
            combined_msg = "Dimension-specific improvements needed:\n" + "\n".join(dimension_feedback)
            draft.add_annotation("leader", combined_msg, "dimension_guidance")
            print(f"      📊 Added dimension-specific guidance for {len(dimension_feedback)} areas")

    async def _generate_annotation(self, agent_id: str, draft: SimplifiedDraft) -> str:
        return await self._generate_annotation_with_peers(agent_id, draft, [])
    
    async def _merger_phase(self, draft: SimplifiedDraft) -> SimplifiedDraft:

        print("   🔀 Phase 3: Adaptive Merger Integration")
        
        if not get_config("enhanced_enable_merge_agent", True):
            print("      🔬 Merge agent disabled - skipping merge phase")
            return draft

        if not draft.annotations:
            print("      ℹ️ No annotations to merge")
            return draft

        import time
        merge_start_time = time.time()

        try:
            draft_annotations = []
            for ann in draft.annotations:
                temp_ann = type('DraftAnnotation', (), {
                    'id': f"ann_{len(draft_annotations)}",
                    'agent_id': ann['agent_id'],
                    'annotation_text': ann['text'],
                    'annotation_type': ann['type'],
                    'target_text': draft.content[:100],
                    'priority': 1,
                    'consensus_score': 0.8
                })()
                draft_annotations.append(temp_ann)

            temp_shared_draft = type('SharedDraft', (), {
                'current_content': draft.content,
                'question_content': draft.question,
                'question_type': draft.task_type
            })()

            print("      📊 Analyzing annotations for optimal merge strategy...")
            analysis = await self.strategy_selector.analyze_annotations(draft_annotations, temp_shared_draft)
            recommendation = await self.strategy_selector.recommend_strategy(analysis, temp_shared_draft)

            print(f"      🎯 Selected strategy: {recommendation.recommended_strategy.value}")
            print(f"      📈 Confidence: {recommendation.confidence:.2f}")
            print(f"      💭 Reasoning: {recommendation.reasoning}")

            merge_result = await self._execute_adaptive_merge(
                draft, draft_annotations, recommendation.recommended_strategy
            )

            merge_time = time.time() - merge_start_time
            merge_metadata = self.strategy_selector.get_merge_metadata(analysis, recommendation)
            merge_metadata['execution_time'] = merge_time
            merge_metadata['success'] = merge_result['success']
            merge_metadata['quality_score'] = merge_result.get('quality_score', 0.8)

            self.merge_statistics.append(merge_metadata)

            self.strategy_selector.record_strategy_performance(
                recommendation.recommended_strategy,
                merge_result['success'],
                merge_time,
                merge_result.get('quality_score', 0.8)
            )

            if merge_result['success'] and merge_result.get('merged_content'):
                draft.update_content(merge_result['merged_content'], "adaptive_merger", "adaptive_fusion")
                print(f"      ✅ Adaptive merge completed successfully")
            else:
                print(f"      ⚠️ Merger failed, keeping original")

        except Exception as e:
            print(f"      ❌ Merger phase failed: {e}")

        return draft

    async def _execute_adaptive_merge(self, draft: SimplifiedDraft, annotations: List,
                                    strategy) -> Dict[str, Any]:

        try:
            annotations_text = "\n".join([
                f"- {ann.agent_id}: {ann.annotation_text}"
                for ann in annotations
            ])

            result = await self._semantic_merge(draft, annotations_text)

            return {
                'success': True,
                'merged_content': result,
                'strategy_used': 'semantic_synthesis',
                'quality_score': 0.8
            }

        except Exception as e:
            return {
                'success': False,
                'merged_content': draft.content,
                'error': str(e),
                'quality_score': 0.3
            }

    async def _semantic_merge(self, draft: SimplifiedDraft, annotations_text: str) -> str:
        from prompt import get_merger_prompt, get_worker_draft_prompt

        prompt = get_merger_prompt(
            'merge_annotations',
            question=draft.question,
            task_type=draft.task_type,
            annotated_drafts=f"CURRENT DRAFT:\n{draft.content}\n\nANNOTATIONS:\n{annotations_text}",
            task_prompt=get_worker_draft_prompt(draft.task_type)
        )

        return await self._call_merge_agent(prompt, draft.task_type)

    async def _call_merge_agent(self, prompt: str, task_type: str) -> str:
        from prompt import get_system_prompt

        merger_agent = get_config("merge_agent_model", "gemini")
        system_prompt = get_system_prompt(task_type)
        
        if task_type in ['mbpp', 'humaneval']:
            max_tokens = get_config("merge_agent_max_tokens", 4096)
        else:
            max_tokens = 2048

        result = await async_generate_completion(
            agent_id=merger_agent,
            prompt=prompt,
            system_prompt=system_prompt,
            max_tokens=max_tokens,
            temperature=0.2
        )

        return result if result else ""

        for ann in draft.annotations:
            if ann['text'].startswith('PATCH('):
                try:
                    import re
                    match = re.match(r'PATCH\(([^,]+),([^,]+),(.+)\)', ann['text'])
                    if match:
                        span_id, new_text, why = match.groups()
                        draft.content += f"\n[PATCH {span_id}]: {new_text.strip()}"
                        patch_count += 1
                        print(f"      🔧 Applied patch for {span_id}: {new_text.strip()}")
                except Exception as e:
                    print(f"      ⚠️ Failed to apply patch: {e}")

        return patch_count

    def _identify_complementary_annotations(self, draft: SimplifiedDraft) -> List[Dict]:
        complementary = []

        for ann in draft.annotations:
            if not ann['text'].startswith(('PATCH(', 'PASS')):
                complementary.append(ann)

        return complementary

    async def _creative_merge(self, draft: SimplifiedDraft, annotations: List[Dict]):
        try:
            annotations_text = "\n".join([
                f"- {ann['agent_id']}: {ann['text']}"
                for ann in annotations
            ])

            prompt = f"""Creatively merge these complementary suggestions into the draft:

CURRENT DRAFT:
{draft.content}

COMPLEMENTARY SUGGESTIONS:
{annotations_text}

Intelligently combine the suggestions to improve the draft:"""

            try:
                result = await async_generate_completion(
                    agent_id="openai",  # gpt-4o-mini
                    prompt=prompt,
                    max_tokens=2048,
                    temperature=0.3
                )

                if result and len(result.strip()) > 50:
                    draft.update_content(result, "creative_merger", "creative_fusion")
                    print(f"      ✅ CreativeMerge completed with mini model")
                    return

            except Exception as e:
                print(f"      ⚠️ Mini model failed: {e}")

            try:
                result = await async_generate_completion(
                    agent_id="gemini",
                    prompt=prompt,
                    max_tokens=2048,
                    temperature=0.3
                )

                if result:
                    draft.update_content(result, "creative_merger_gemini", "creative_fusion_backup")
                    print(f"      ✅ CreativeMerge completed with gemini backup")

            except Exception as e:
                print(f"      ❌ Both mini and gemini failed: {e}")

        except Exception as e:
            print(f"      ❌ CreativeMerge failed: {e}")

            temp_shared_draft = type('SharedDraft', (), {
                'current_content': draft.content,
                'question_content': draft.question,
                'question_type': draft.task_type
            })()

            print("      📊 Analyzing annotations for optimal merge strategy...")
            analysis = await self.strategy_selector.analyze_annotations(draft_annotations, temp_shared_draft)
            recommendation = await self.strategy_selector.recommend_strategy(analysis, temp_shared_draft)

            print(f"      🎯 Selected strategy: {recommendation.recommended_strategy.value}")
            print(f"      📈 Confidence: {recommendation.confidence:.2f}")
            print(f"      💭 Reasoning: {recommendation.reasoning}")

            merge_result = await self._execute_adaptive_merge(
                draft, draft_annotations, recommendation.recommended_strategy
            )

            merge_time = time.time() - merge_start_time
            merge_metadata = self.strategy_selector.get_merge_metadata(analysis, recommendation)
            merge_metadata['execution_time'] = merge_time
            merge_metadata['success'] = merge_result['success']
            merge_metadata['quality_score'] = merge_result.get('quality_score', 0.8)

            self.merge_statistics.append(merge_metadata)

            self.strategy_selector.record_strategy_performance(
                recommendation.recommended_strategy,
                merge_result['success'],
                merge_time,
                merge_result.get('quality_score', 0.8)
            )

            if merge_result['success'] and merge_result.get('merged_content'):
                draft.update_content(merge_result['merged_content'], "adaptive_merger", "adaptive_fusion")
                print(f"      ✅ Adaptive merge completed successfully")
            else:
                print(f"      ⚠️ Merger failed, keeping original")
                
        except Exception as e:
            print(f"      ❌ Merger phase failed: {e}")
        
        return draft

    async def _execute_adaptive_merge(self, draft: SimplifiedDraft, annotations: List,
                                    strategy: MergeStrategy) -> Dict[str, Any]:

        try:
            annotations_text = "\n".join([
                f"- {ann.agent_id}: {ann.annotation_text}"
                for ann in annotations
            ])

            if strategy == MergeStrategy.SEQUENTIAL_INTEGRATION:
                result = await self._sequential_merge(draft, annotations_text)
            elif strategy == MergeStrategy.SEMANTIC_SYNTHESIS:
                result = await self._semantic_merge(draft, annotations_text)
            elif strategy == MergeStrategy.CONFLICT_RESOLUTION:
                result = await self._conflict_resolution_merge(draft, annotations_text)
            elif strategy == MergeStrategy.PRIORITY_BASED:
                result = await self._priority_based_merge(draft, annotations, annotations_text)
            elif strategy == MergeStrategy.CREATIVE_COMBINATION:
                result = await self._creative_merge(draft, annotations_text)
            else:
                result = await self._semantic_merge(draft, annotations_text)

            return {
                'success': True,
                'merged_content': result,
                'strategy_used': strategy.value,
                'quality_score': 0.8
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'merged_content': draft.content,
                'strategy_used': strategy.value,
                'quality_score': 0.3
            }

    async def _sequential_merge(self, draft: SimplifiedDraft, annotations_text: str) -> str:
        prompt = f"""Apply these annotations to the draft in sequence, one by one:

Current Draft:
{draft.content}

Annotations to apply:
{annotations_text}

Apply each annotation carefully and return the improved draft:"""

        return await self._call_merge_agent(prompt, draft.task_type)

    async def _semantic_merge(self, draft: SimplifiedDraft, annotations_text: str) -> str:
        prompt = get_merger_prompt(
            'merge_annotations',
            question=draft.question,
            task_type=draft.task_type,
            annotated_drafts=f"CURRENT DRAFT:\n{draft.content}\n\nANNOTATIONS:\n{annotations_text}",
            task_prompt=get_worker_draft_prompt(draft.task_type)
        )

        return await self._call_merge_agent(prompt, draft.task_type)

    async def _conflict_resolution_merge(self, draft: SimplifiedDraft, annotations_text: str) -> str:
        prompt = f"""Resolve conflicts between these annotations and merge them intelligently:

Current Draft:
{draft.content}

Conflicting Annotations:
{annotations_text}

Identify conflicts, resolve them using best judgment, and return the improved draft:"""

        return await self._call_merge_agent(prompt, draft.task_type)

    async def _priority_based_merge(self, draft: SimplifiedDraft, annotations: List, annotations_text: str) -> str:
        sorted_annotations = sorted(annotations, key=lambda x: getattr(x, 'priority', 1), reverse=True)
        priority_text = "\n".join([
            f"- Priority {getattr(ann, 'priority', 1)}: {ann.agent_id}: {ann.annotation_text}"
            for ann in sorted_annotations
        ])

        prompt = f"""Apply these annotations in priority order (highest first):

Current Draft:
{draft.content}

Prioritized Annotations:
{priority_text}

Focus on high-priority suggestions first, then incorporate lower-priority ones if compatible:"""

        return await self._call_merge_agent(prompt, draft.task_type)

    async def _creative_merge(self, draft: SimplifiedDraft, annotations_text: str) -> str:
        prompt = f"""Creatively synthesize these diverse annotations into an improved draft:

Current Draft:
{draft.content}

Diverse Annotations:
{annotations_text}

Use creative thinking to combine different perspectives and create a superior solution:"""

        return await self._call_merge_agent(prompt, draft.task_type)


    async def _context_compression_phase(self, draft: SimplifiedDraft) -> SimplifiedDraft:

        print("   🗜️ Phase 0: Context Compression")
        
        if not get_config("enhanced_enable_context_compression", True):
            print("      🔬 Context compression disabled - skipping compression phase")
            return draft

        try:
            context_data = {
                'current_content': draft.content,
                'annotations': draft.annotations,
                'change_history': draft.history,
                'question': draft.question,
                'task_type': draft.task_type,
                'session_id': draft.session_id,
                'version': draft.version,
                'participants': draft.participants,
                'quality_score': draft.quality_score,
                'created_at': draft.created_at.isoformat()
            }

            compression_estimate = self.context_compressor.estimate_compression_need(context_data)

            if not compression_estimate['compression_needed']:
                print("      ✅ Context within budget, no compression needed")
                return draft

            print(f"      📊 Context usage: {compression_estimate['current_usage']}/{compression_estimate['budget_limit']} tokens")
            print(f"      🎯 Excess tokens: {compression_estimate['excess_tokens']}")

            compressed_context = await self.context_compressor.compress_context(context_data)

            if 'current_content' in compressed_context:
                old_content = draft.content
                draft.content = compressed_context['current_content']

                if old_content != draft.content:
                    draft.history.append({
                        'version': draft.version,
                        'content': old_content,
                        'agent': 'context_compressor',
                        'change_type': 'compression',
                        'timestamp': datetime.now().isoformat()
                    })
                    draft.version += 1

            if 'annotations' in compressed_context and isinstance(compressed_context['annotations'], list):
                draft.annotations = compressed_context['annotations']

            if 'change_history' in compressed_context and isinstance(compressed_context['change_history'], list):
                draft.history = compressed_context['change_history']

            compression_stats = self.context_compressor.get_compression_statistics()
            self.compression_statistics.append({
                'round': len(self.compression_statistics) + 1,
                'timestamp': datetime.now().isoformat(),
                'original_tokens': compression_estimate['current_usage'],
                'compressed_tokens': compression_estimate['current_usage'] - compression_estimate['excess_tokens'],
                'compression_ratio': compression_stats.get('average_compression_ratio', 1.0),
                'tokens_saved': compression_estimate['excess_tokens']
            })

            print(f"      ✅ Context compressed successfully")

        except Exception as e:
            print(f"      ⚠️ Context compression failed: {e}")

        return draft

    async def _post_merge_leader_review_phase(self, draft: SimplifiedDraft) -> SimplifiedDraft:
        print("   📋 Phase 3.5: Post-Merge Leader Review")
        
        try:
            current_content = draft.content
            if not current_content or len(current_content.strip()) < 10:
                print("      ℹ️ Content too short for meaningful review")
                return draft
            
            review_prompt = self._create_post_merge_review_prompt(draft.task_type, current_content)
            
            leader_agent = get_config("leader_model", "gemini")
            review_response = await async_generate_completion(
                agent_id=leader_agent,
                prompt=review_prompt,
                system_prompt="You are an expert reviewer ensuring content quality is maintained after merge processes."
            )
            
            if "IMPROVEMENT_NEEDED" in review_response:
                print("      🔧 Leader identified improvements needed after merge")
                improved_content = self._extract_improved_content(review_response)
                if improved_content and improved_content != current_content:
                    draft.update_content(improved_content, "leader", "post_merge_review")
                    print(f"      ✅ Content improved by Leader post-merge review")
                else:
                    print("      ℹ️ Leader review complete - content maintained")
            else:
                print("      ✅ Leader approved merge result - no changes needed")
            
            review_annotation = {
                'agent_id': 'leader',
                'text': f"Post-merge review: {review_response[:200]}...",
                'type': 'post_merge_review',
                'timestamp': datetime.now().isoformat()
            }
            draft.annotations.append(review_annotation)
            
        except Exception as e:
            print(f"      ⚠️ Post-merge leader review failed: {e}")
        
        return draft

    def _create_post_merge_review_prompt(self, task_type: str, merged_content: str) -> str:
        base_prompt = f"""You are reviewing content that has been merged from multiple drafts and annotations.
        
MERGED CONTENT TO REVIEW:
{merged_content}

TASK TYPE: {task_type}

Your role is to ensure the merged content maintains quality and hasn't been degraded by the merge process.

REVIEW CRITERIA:
1. **Content Integrity**: Is the core solution/answer still correct and complete?
2. **Coherence**: Does the merged content flow logically without contradictions?
3. **Quality Preservation**: Has the merge maintained or improved the original quality?

"""
        
        if task_type in ['humaneval', 'mbpp']:
            base_prompt += """
PROGRAMMING SPECIFIC CHECKS:
- Is the code still syntactically correct?
- Are all necessary imports and functions complete?
- Does the solution handle the original requirements?
- Has any critical code been lost or corrupted in the merge?

RESPONSE FORMAT:
If improvements are needed, respond with:
IMPROVEMENT_NEEDED
[Provide the corrected version]

If the content is acceptable, respond with:
APPROVED - Content quality maintained after merge
"""
        elif task_type == 'math':
            base_prompt += """
MATHEMATICAL CHECKS:
- Are all mathematical steps still logically sound?
- Has the mathematical reasoning been preserved?
- Are calculations still accurate?
- Is the final answer format correct?

RESPONSE FORMAT:
If improvements are needed, respond with:
IMPROVEMENT_NEEDED
[Provide the corrected version]

If the content is acceptable, respond with:
APPROVED - Mathematical reasoning preserved after merge
"""
        elif task_type == 'gsm8k':
            base_prompt += """
ARITHMETIC CHECKS:
- Are calculation steps still correct and clear?
- Is the step-by-step logic preserved?
- Is the final numerical answer accurate?
- Are units and formatting correct?

RESPONSE FORMAT:
If improvements are needed, respond with:
IMPROVEMENT_NEEDED
[Provide the corrected version]

If the content is acceptable, respond with:
APPROVED - Arithmetic solution quality maintained
"""
        else:
            base_prompt += """
GENERAL CHECKS:
- Is the reasoning still sound and complete?
- Are key facts and information preserved?
- Is the answer format appropriate for the task?

RESPONSE FORMAT:
If improvements are needed, respond with:
IMPROVEMENT_NEEDED
[Provide the corrected version]

If the content is acceptable, respond with:
APPROVED - Content quality maintained after merge
"""
        
        return base_prompt

    def _extract_improved_content(self, leader_response: str) -> str:
        try:
            if "IMPROVEMENT_NEEDED" in leader_response:
                lines = leader_response.split('\n')
                content_started = False
                improved_lines = []
                
                for line in lines:
                    if "IMPROVEMENT_NEEDED" in line:
                        content_started = True
                        continue
                    elif content_started:
                        improved_lines.append(line)
                
                improved_content = '\n'.join(improved_lines).strip()
                return improved_content if improved_content else None
            
        except Exception as e:
            print(f"      ⚠️ Failed to extract improved content: {e}")
            
        return None

    async def _leader_evaluation_and_final_answer_phase(self, draft: SimplifiedDraft) -> Dict[str, Any]:
        
        print("   🎯 Phase 4: Leader Evaluation & Final Answer Generation")
        
        try:
            evaluation_prompt = self._create_multidimensional_evaluation_prompt(draft)

            leader_agent = get_config("leader_model", "gemini")
            system_prompt = get_system_prompt(draft.task_type)

            evaluation_result = await async_generate_completion(
                agent_id=leader_agent,
                prompt=evaluation_prompt,
                system_prompt=system_prompt,
                max_tokens=512,
                temperature=0.1
            )

            evaluation = self._parse_leader_evaluation(evaluation_result, draft)

            final_answer = None
            if evaluation['decision'] == 'approved':
                final_answer_prompt = self._create_final_answer_prompt(draft)

                if draft.task_type in ['mbpp', 'humaneval']:
                    max_tokens = 1200 if draft.task_type == "humaneval" else 800 if draft.task_type == "mbpp" else 150
                else:
                    max_tokens = 256
                
                final_answer_result = await async_generate_completion(
                    agent_id=leader_agent,
                    prompt=final_answer_prompt,
                    system_prompt=system_prompt,
                    max_tokens=max_tokens,
                    temperature=0.1
                )

                final_answer = final_answer_result.strip() if final_answer_result else None
                print(f"      ✅ Leader generated final answer")

                evaluation['final_answer'] = final_answer
            
            print(f"      📊 Quality Dimensions:")
            print(f"         📍 Accuracy: {evaluation.get('accuracy', 0.6):.2f}")
            print(f"         📋 Completeness: {evaluation.get('completeness', 0.6):.2f}")
            print(f"         💡 Clarity: {evaluation.get('clarity', 0.6):.2f}")
            print(f"         🎯 Overall: {evaluation.get('overall', 0.6):.2f}")
            print(f"      🎯 Decision: {evaluation['decision']}")
            
            if evaluation.get('feedback_annotations'):
                print(f"      📋 Specific Feedback ({len(evaluation['feedback_annotations'])} items):")
                for i, annotation in enumerate(evaluation['feedback_annotations'], 1):
                    severity_icon = {'high': '🔴', 'medium': '🟡', 'low': '🟢'}.get(annotation.get('severity', 'medium'), '🟡')
                    print(f"         {i}. {severity_icon} {annotation.get('span_id', 'N/A')}: {annotation.get('comment', 'No comment')}")
            
            if evaluation.get('improvement_priorities'):
                print(f"      🔧 Priority Improvements: {', '.join(evaluation['improvement_priorities'][:3])}")
            
            return {
                'evaluation': evaluation,
                'final_answer': final_answer
            }
            
        except Exception as e:
            print(f"      ❌ Leader evaluation and final answer generation failed: {e}")
            return {
                'evaluation': {
                    'overall_score': 0.5,
                    'quality_score': 0.5,
                    'decision': 'needs_major_revision',
                    'feedback': f"Leader processing failed: {str(e)}"
                },
                'final_answer': None
            }
    
    def _create_final_answer_prompt(self, draft: SimplifiedDraft) -> str:
        
        task_instructions = {
            'gsm8k': {
                'format': '#### [number]',
                'requirement': 'Extract ONLY the final numerical answer (no units, no explanations)',
                'example': '#### 42'
            },
            'math': {
                'format': '#### [mathematical_answer]',
                'requirement': 'Extract ONLY the final mathematical answer. Accept fractions (3/4), decimals (0.75), expressions (2π), exact forms (√2), or algebraic expressions',
                'example': '#### 42 or #### 3/4 or #### 2π or #### √2'
            },
            'mbpp': {
                'format': 'Complete Python function code',
                'requirement': 'Output ONLY the complete Python function code, starting with def. Ensure ALL code is complete with proper closing brackets/statements',
                'example': 'def is_even(n):\n    return n % 2 == 0'
            },
            'humaneval': {
                'format': 'Complete Python function code',
                'requirement': 'Output ONLY the complete Python function code, starting with def. Ensure ALL code is complete with proper closing brackets/statements and no truncation', 
                'example': 'def max_element(lst):\n    return max(lst) if lst else None'
            },

            'hotpotqa': {
                'format': '#### [specific_factual_answer]',
                'requirement': 'Extract ONLY the specific factual answer (name, date, place, etc.)',
                'example': '#### Barack Obama'
            },
            'strategyqa': {
                'format': '#### [yes/no]',
                'requirement': 'Output ONLY yes or no',
                'example': '#### yes'
            },
            'gpqa': {
                'format': '#### [A/B/C/D]',
                'requirement': 'Output ONLY the correct choice letter',
                'example': '#### B'
            },
            'mmlu': {
                'format': '#### [A/B/C/D]',
                'requirement': 'Output ONLY the correct choice letter',
                'example': '#### C'
            }
        }
        
        task_info = task_instructions.get(draft.task_type, {
            'format': '#### [answer]',
            'requirement': 'Extract ONLY the final answer',
            'example': '#### answer'
        })
        
        return f"""As the leader agent, you must synthesize the collaborative draft into the final, most concise answer.

PROBLEM: {draft.question}
TASK TYPE: {draft.task_type}

COLLABORATIVE DRAFT:
{draft.content}

RECENT ANNOTATIONS:
{chr(10).join([f"- {ann['agent_id']}: {ann['text']}" for ann in draft.annotations[-3:]])}

CRITICAL OUTPUT REQUIREMENTS FOR {draft.task_type.upper()}:
- {task_info['requirement']}
- Format: {task_info['format']}
- Example: {task_info['example']}

IMPORTANT RULES:
1. Be extremely concise - provide ONLY the final answer
2. Follow the exact format required for {draft.task_type}
3. Remove ALL explanations, reasoning, or extra text
4. Ensure accuracy based on the collaborative work
5. For programming tasks: output COMPLETE, working code with NO TRUNCATION - ensure all functions, loops, and statements are properly closed
6. For math tasks: output only the final numerical result or expression
7. For factual questions: output only the specific fact requested
8. For multiple choice: output only the letter choice
9. For code: verify syntax correctness and completeness before output

Generate the final answer now:"""
    
    def _create_multidimensional_evaluation_prompt(self, draft: SimplifiedDraft) -> str:
        
        task_focus = {
            'gsm8k': 'mathematical reasoning accuracy and calculation correctness',
            'math': 'mathematical rigor, proof validity, and solution completeness',
            'mbpp': 'code functionality, syntax correctness, completeness (no truncation), and edge case handling',
            'humaneval': 'code correctness, syntax completeness (no truncation), proper function structure, and implementation quality',

            'hotpotqa': 'multi-hop reasoning and fact verification',
            'strategyqa': 'logical reasoning and step-by-step analysis',
            'gpqa': 'scientific accuracy and expert-level reasoning',
            'mmlu': 'domain knowledge and comprehensive understanding'
        }
        
        focus_area = task_focus.get(draft.task_type, 'general problem-solving and reasoning')
        
        task_guidance = ""
        if draft.task_type == 'math':
            task_guidance = """
SPECIALIZED EVALUATION FOR ADVANCED MATH COMPETITION PROBLEMS:
- RECOGNIZE: These are challenging mathematical contest problems requiring deep insight
- MATHEMATICAL RIGOR: Verify all logical steps and mathematical reasoning chains
- PROOF VALIDITY: Check that arguments are mathematically sound and complete
- CALCULATION ACCURACY: Verify numerical computations and algebraic manipulations
- CONCEPTUAL UNDERSTANDING: Assess if the approach demonstrates proper mathematical insight
- PARTIAL CREDIT: Award credit for correct methodology even if execution has minor errors
- SCORING GUIDE: 0.6+ for valid approach, 0.7+ for correct reasoning, 0.8+ for complete solutions
- ACCEPT: Multiple valid solution methods and approaches
- FOCUS: Mathematical creativity, proof techniques, and problem-solving strategies
- EXPECT: Solutions may be complex and multi-step requiring careful verification
"""
        elif draft.task_type == 'gsm8k':
            task_guidance = """
STRICT EVALUATION FOR ARITHMETIC WORD PROBLEMS:
- Verify EVERY mathematical step for correctness
- Check calculations thoroughly - even small arithmetic errors reduce accuracy score significantly
- Ensure logical flow: each step must follow from the previous
- Verify final answer matches the question asked
- Be especially critical of: calculation errors, logical jumps, incomplete reasoning, unit mismatches
- Check if all given information is used correctly
- Only approve if mathematical reasoning is completely sound AND calculations are 100% correct
- Score accuracy harshly: 0.3-0.5 for calculation errors, 0.6-0.7 for logical issues, 0.8+ only for near-perfect solutions
"""
        elif draft.task_type in ['mbpp', 'humaneval']:
            task_guidance = """
SPECIALIZED EVALUATION FOR PROGRAMMING TASKS:
- PRIMARY FOCUS: Code functionality and syntax correctness
- Check if the function structure is complete (proper def statement, parameters, return)
- Verify logic correctness for the specific problem requirements
- Ensure code handles the test cases mentioned in the problem
- Look for common issues: missing returns, incorrect variable names, edge case handling
- Score generously for working code: 0.8+ for functional solutions, 0.9+ for efficient code
- Be more lenient on style/comments - focus on correctness and completeness
- IMPORTANT: A working solution should score 0.8+, even if not optimal
"""
        elif draft.task_type == 'gpqa':
            task_guidance = """
SPECIALIZED EVALUATION FOR GPQA (Graduate-Level Scientific Questions):
- FOCUS: Scientific reasoning and knowledge application across physics, chemistry, biology
- These are extremely challenging graduate-level questions requiring deep domain expertise
- Be generous with scoring: 0.6+ for reasonable scientific approach, 0.7+ for correct reasoning path, 0.8+ for accurate answers
- Look for: scientific concepts used correctly, logical reasoning process, appropriate methodology
- Accept partial credit: even if final answer is incorrect, credit good scientific reasoning
- Common challenges: complex calculations, interdisciplinary knowledge, advanced concepts
- IMPORTANT: GPQA is inherently difficult - score more leniently than typical academic problems
- Reward: correct identification of relevant principles, appropriate problem-solving approach, clear scientific thinking
"""
        
        # Pre-compute the dynamic content for the f-string
        scoring_guidelines = self._get_scoring_guidelines(draft.task_type)
        decision_criteria = self._get_decision_criteria(draft.task_type)
        
        return f"""You are a quality assessment expert. Evaluate this draft solution across multiple dimensions and provide structured feedback.

PROBLEM: {draft.question}
TASK TYPE: {draft.task_type}
FOCUS AREA: {focus_area}

CURRENT DRAFT:
{draft.content}

COLLABORATIVE ANNOTATIONS SUMMARY:
{self._format_annotations_summary(draft)}
{task_guidance}
ADDITIONAL CODING GUIDELINES:
For programming tasks (mbpp/humaneval), pay extra attention to:
- Code completeness: Is the function definition complete with no truncation?
- Syntax correctness: Are all brackets, parentheses properly closed?
- Function structure: Does it have a proper def statement and function body?
- Implementation: Does the code actually solve the problem?

If the code is truncated or incomplete, score completeness as 0.3 or lower.

Evaluate the draft on these dimensions (0.0-1.0 scale):

{scoring_guidelines}

For issues found, provide specific feedback_annotations with:
- span_id: which part needs improvement (e.g., "line_1", "calculation_step_2") 
- issue_type: accuracy/completeness/clarity/format
- comment: specific actionable feedback
- severity: high/medium/low

Output in JSON format:
{{
    "accuracy": 0.0-1.0,
    "completeness": 0.0-1.0, 
    "clarity": 0.0-1.0,
    "overall": 0.0-1.0,
    "decision": "approved" | "needs_minor_revision" | "needs_major_revision",
    
    {decision_criteria}
    "feedback_annotations": [
        {{
            "span_id": "specific_location",
            "issue_type": "accuracy|completeness|clarity|format",
            "comment": "specific actionable feedback",
            "severity": "high|medium|low"
        }}
    ],
    "strengths": ["what the draft does well"],
    "improvement_priorities": ["ordered list of key improvements needed"]
}}"""

    def _format_annotations_summary(self, draft: SimplifiedDraft) -> str:
        if not draft.annotations:
            return "No annotations available"
        
        summary_lines = []
        for ann in draft.annotations:
            ann_type = ann['type']
            agent = ann['agent_id']
            text = ann['text'][:80] + "..." if len(ann['text']) > 80 else ann['text']
            summary_lines.append(f"- {ann_type.upper()} ({agent}): {text}")
        
        return "\n".join(summary_lines)

    def _parse_leader_evaluation(self, result: str, draft: SimplifiedDraft) -> Dict[str, Any]:

        evaluation = {
            'accuracy': 0.3,
            'completeness': 0.35,
            'clarity': 0.4,
            'overall': 0.35,
            'decision': 'needs_major_revision',
            'feedback': result or 'No feedback provided',
            'feedback_annotations': [],
            'strengths': [],
            'improvement_priorities': []
        }
        
        try:
            if '```json' in result:
                json_start = result.find('```json') + 7
                json_end = result.find('```', json_start)
                json_str = result[json_start:json_end].strip()
                parsed = json.loads(json_str)
                
                if self._validate_evaluation_scores(parsed, draft.task_type):
                    evaluation.update(parsed)
                else:
                    print(f"      ⚠️ Rejected unrealistic evaluation scores: {parsed}")
                    self._apply_realistic_scoring(evaluation, parsed, draft.task_type)

                if 'feedback_annotations' in parsed and parsed['feedback_annotations']:
                    print(f"      📋 Structured Feedback:")
                    for i, annotation in enumerate(parsed['feedback_annotations'], 1):
                        span_id = annotation.get('span_id', 'N/A')
                        issue_type = annotation.get('issue_type', 'general')
                        comment = annotation.get('comment', 'No comment')
                        severity = annotation.get('severity', 'medium')

                        severity_icon = {'high': '🔴', 'medium': '🟡', 'low': '🟢'}.get(severity, '🟡')
                        print(f"         {i}. {severity_icon} Span {span_id} ({issue_type}): {comment}")

            elif '{' in result:
                start = result.find('{')
                end = result.rfind('}') + 1
                json_str = result[start:end]
                parsed = json.loads(json_str)
                evaluation.update(parsed)
        except Exception as e:
            print(f"      ⚠️ Failed to parse leader evaluation JSON: {e}")
            pass
        
        if result:
            lines = result.lower().split('\n')
            for line in lines:
                if 'overall_score' in line or 'score' in line:
                    import re
                    scores = re.findall(r'[\d.]+', line)
                    if scores:
                        score = min(float(scores[0]), 1.0)
                        evaluation['overall_score'] = score
                        evaluation['quality_score'] = score
                
                if 'approved' in line:
                    evaluation['decision'] = 'approved'
                elif 'rejected' in line:
                    evaluation['decision'] = 'rejected'
        
        if 'quality_score' not in evaluation:
            evaluation['quality_score'] = evaluation.get('overall', evaluation.get('overall_score', 0.35))
        
        return evaluation
    
    def _get_decision_criteria(self, task_type: str) -> str:
        if task_type in ['mbpp', 'humaneval']:
            return """DECISION CRITERIA FOR PROGRAMMING TASKS:
    - "approved": Working code (accuracy ≥ 0.8) with proper structure and syntax
    - "needs_minor_revision": Code mostly works but has minor issues (accuracy 0.6-0.79)
    - "needs_major_revision": Code has major bugs or syntax errors (accuracy < 0.6)"""
        elif task_type == 'math':
            return """DECISION CRITERIA FOR ADVANCED MATHEMATICS:
    - "approved": Strong mathematical solution (overall ≥ 0.75) with sound reasoning
    - "needs_minor_revision": Valid approach with execution issues (overall 0.6-0.74)
    - "needs_major_revision": Flawed approach or major mathematical errors (overall < 0.6)
    NOTE: Math problems are inherently difficult - be generous with mathematical insight"""
        else:
            return """DECISION CRITERIA (be conservative):
    - "approved": Overall ≥ 0.85 AND accuracy ≥ 0.8 AND few/no major issues
    - "needs_minor_revision": Overall 0.65-0.84 OR accuracy 0.6-0.79 
    - "needs_major_revision": Overall < 0.65 OR accuracy < 0.6 OR major flaws"""

    def _get_scoring_guidelines(self, task_type: str) -> str:
        if task_type in ['mbpp', 'humaneval']:
            return """
PROGRAMMING TASK SCORING GUIDELINES:
- BE GENEROUS: Working code should score 0.8+ even if not perfect
- Score 0.9+ for efficient, well-structured solutions
- Focus on functionality over style

1. ACCURACY: Code functionality and syntax correctness
   - 0.8+: Working code that solves the problem correctly
   - 0.6-0.7: Code with minor logic issues but mostly functional
   - 0.3-0.5: Major syntax errors or incorrect logic

2. COMPLETENESS: Function structure and requirements addressed
   - 0.8+: Complete function with proper def, parameters, return
   - 0.6-0.7: Missing minor elements like docstring or examples
   - 0.3-0.5: Incomplete function or missing core components

3. CLARITY: Code readability and structure
   - 0.8+: Well-structured, clear variable names, good flow
   - 0.6-0.7: Generally readable with minor improvements needed
   - 0.3-0.5: Poor structure, unclear implementation

4. OVERALL: Weighted combination focusing on functionality
   - Working solutions should score 0.8+ regardless of style issues
   - Prioritize correctness over documentation or style
"""
        elif task_type == 'math':
            return """
ADVANCED MATHEMATICS COMPETITION SCORING GUIDELINES:
- RECOGNIZE difficulty: These are contest-level problems requiring mathematical insight
- BE BALANCED: Award credit for mathematical reasoning even with execution issues

1. ACCURACY: Mathematical correctness and logical validity  
   - 0.8+: Mathematically sound with correct final answer
   - 0.6-0.7: Valid approach with minor computational errors
   - 0.4-0.5: Correct core insight but significant execution flaws
   - 0.1-0.3: Incorrect approach or major mathematical errors

2. COMPLETENESS: Solution thoroughness and proof rigor
   - 0.8+: Complete solution with all necessary steps justified
   - 0.6-0.7: Mostly complete but missing some justification
   - 0.4-0.5: Partial solution showing key insights
   - 0.1-0.3: Incomplete with major gaps in reasoning

3. CLARITY: Mathematical presentation and logical flow
   - 0.8+: Clear mathematical exposition with logical progression
   - 0.6-0.7: Generally clear with minor presentation issues
   - 0.4-0.5: Understandable but poorly organized
   - 0.1-0.3: Unclear or confusing mathematical presentation

4. OVERALL: Emphasis on mathematical insight and problem-solving
   - Credit creative approaches and alternative solution methods
   - Value mathematical reasoning over mere calculation
   - Consider the contest-level difficulty in scoring
"""
        else:
            return """
CRITICAL SCORING GUIDELINES:
- BE HARSH: Most drafts should score 0.3-0.7 range
- Score 0.8+ ONLY for truly excellent work with minimal flaws
- Score 0.9+ ONLY for near-perfect solutions
- NEVER give all 1.0s unless absolutely perfect
- Always find at least minor issues to improve

1. ACCURACY: Correctness of facts, calculations, logic, and conclusions
   - 0.3-0.5: Major errors, wrong calculations, faulty logic
   - 0.6-0.7: Minor errors, mostly correct but some flaws
   - 0.8+: Highly accurate with minimal issues

2. COMPLETENESS: All necessary steps, components, and requirements addressed
   - 0.3-0.5: Missing major components, incomplete solution
   - 0.6-0.7: Most components present, minor gaps
   - 0.8+: Comprehensive and complete

3. CLARITY: Clear explanation, good structure, easy to understand
   - 0.3-0.5: Unclear, poor structure, confusing
   - 0.6-0.7: Generally clear with some improvements needed
   - 0.8+: Exceptionally clear and well-structured

4. OVERALL: Weighted combination considering task requirements
   - Must be realistic average of above scores
   - Never exceed highest individual score
"""

    def _validate_evaluation_scores(self, parsed: Dict[str, Any], task_type: str) -> bool:
        
        score_fields = ['accuracy', 'completeness', 'clarity', 'overall']
        scores = []
        
        for field in score_fields:
            if field in parsed and isinstance(parsed[field], (int, float)):
                scores.append(parsed[field])
        
        if len(scores) == 0:
            return False
            
        if task_type == 'mbpp':
            return True
        elif task_type == 'gpqa':
            return True
        elif task_type == 'humaneval':
            return True
        elif task_type == 'math':
            if all(score >= 0.97 for score in scores):
                return False
            return True
        else:
            if all(score >= 0.95 for score in scores):
                return False
                
        avg_score = sum(scores) / len(scores)
        has_feedback = parsed.get('feedback_annotations') and len(parsed['feedback_annotations']) > 0
        
        if task_type in ['mbpp', 'humaneval', 'gpqa']:
            suspicious_threshold = 0.95
        elif task_type == 'math':
            suspicious_threshold = 0.93
        else:
            suspicious_threshold = 0.9
            
        if avg_score >= suspicious_threshold and not has_feedback:
            return False
            
        return True
    
    def _apply_realistic_scoring(self, evaluation: Dict[str, Any], parsed: Dict[str, Any], task_type: str):
        
        score_fields = ['accuracy', 'completeness', 'clarity', 'overall']
        
        for field in score_fields:
            if field in parsed and isinstance(parsed[field], (int, float)):
                original_score = parsed[field]
                
                if task_type == 'mbpp':
                    if original_score >= 0.99:
                        adjusted_score = 0.9
                    else:
                        adjusted_score = original_score
                elif task_type == 'gpqa':
                    if original_score >= 0.98:
                        adjusted_score = 0.88
                    else:
                        adjusted_score = original_score
                elif task_type == 'humaneval':
                    if original_score >= 0.99:
                        adjusted_score = 0.9
                    else:
                        adjusted_score = original_score
                elif task_type == 'math':
                    if original_score >= 0.98:
                        adjusted_score = 0.85
                    elif original_score >= 0.94:
                        adjusted_score = 0.82
                    else:
                        adjusted_score = original_score
                else:
                    if original_score >= 0.95:
                        adjusted_score = 0.7
                    elif original_score >= 0.9:
                        adjusted_score = 0.75
                    else:
                        adjusted_score = original_score
                    
                evaluation[field] = adjusted_score
                if adjusted_score != original_score:
                    print(f"         📉 Adjusted {field}: {original_score:.2f} → {adjusted_score:.2f}")
        
        if task_type == 'mbpp':
            pass
        elif task_type == 'gpqa':
            pass
        elif task_type == 'humaneval':
            pass
        else:
            evaluation['decision'] = 'needs_minor_revision'
            print(f"         🔧 Forced decision: needs_minor_revision (suspicious scoring)")
    
    def _create_final_result(self, draft: SimplifiedDraft, evaluation: Dict[str, Any],
                           total_time: float, session_id: str) -> Dict[str, Any]:

        final_answer = self._extract_final_answer(draft.content, draft.task_type)

        quality_score = evaluation.get('overall', evaluation.get('overall_score', evaluation.get('quality_score', 0.5)))

        result = {
            'success': evaluation['decision'] in ['approved', 'needs_minor_revision'],
            'solution': final_answer,
            'total_processing_time': total_time,
            'mode': 'simplified_collaborative',
            'quality_score': quality_score,
            'quality_decision': evaluation['decision'],
            'session_id': session_id
        }

        print(f"💾 Result processed: {final_answer}")

        return result

    def _create_final_result(self, draft: SimplifiedDraft, evaluation: Dict[str, Any],
                                          total_time: float, session_id: str, final_answer: str) -> Dict[str, Any]:

        quality_score = evaluation.get('overall', evaluation.get('overall_score', evaluation.get('quality_score', 0.5)))

        result = {
            'success': evaluation['decision'] in ['approved', 'needs_minor_revision'],
            'solution': final_answer,
            'total_processing_time': total_time,
            'mode': 'simplified_collaborative',
            'quality_score': quality_score,
            'quality_decision': evaluation['decision'],
            'session_id': session_id
        }

        print(f"💾 Result processed: {final_answer}")

        return result

    async def _leader_synthesis(self, draft: SimplifiedDraft) -> str:
        try:
            if draft.task_type in ["gsm8k", "math"] and "####" in draft.content:
                import re
                match = re.search(r'####\s*(\d+)', draft.content)
                if match:
                    return match.group(1)

            from prompt import LeaderAgentPrompts
            from utils.api import async_generate_completion

            synthesis_prompt = LeaderAgentPrompts.get_enhanced_synthesis_prompt(
                draft.question,
                draft.task_type,
                draft.content
            )

            max_tokens = 1200 if draft.task_type == "humaneval" else 800 if draft.task_type == "mbpp" else 150
            synthesized = await async_generate_completion(
                agent_id="openai",
                prompt=synthesis_prompt,
                temperature=0.1,
                max_tokens=max_tokens
            )

            if draft.task_type in ["mbpp", "humaneval"]:
                if "```python" in synthesized:
                    start = synthesized.find("```python") + 9
                    end = synthesized.find("```", start)
                    if end != -1:
                        code = synthesized[start:end].strip()
                        return code if code else "No code extracted"

                lines = synthesized.strip().split('\n')
                code_lines = []
                in_function = False
                indent_level = 0
                
                for line in lines:
                    if line.strip().startswith('def '):
                        in_function = True
                        indent_level = len(line) - len(line.lstrip())
                        code_lines.append(line)
                    elif in_function:
                        current_indent = len(line) - len(line.lstrip())
                        if not line.strip() or current_indent > indent_level:
                            code_lines.append(line)
                        elif current_indent <= indent_level and line.strip():
                            break
                
                if code_lines:
                    code_text = '\n'.join(code_lines).strip()
                    if self._is_code_complete(code_text):
                        return code_text
                    else:
                        print(f"⚠️ Detected incomplete code, will request regeneration")
                        return f"# INCOMPLETE CODE DETECTED\n{code_text}"

                return synthesized.strip()

            if "ANSWER:" in synthesized:
                answer = synthesized.split("ANSWER:")[-1].strip()
                answer = answer.split('\n')[0].strip()
                answer = answer.rstrip('.')
                return answer if answer else "No answer extracted"

            lines = synthesized.strip().split('\n')
            for line in reversed(lines):
                line = line.strip()
                if line and len(line) < 100 and not line.startswith(('Here', 'The', 'This')):
                    return line

            return synthesized.strip()[:50]
            
        except Exception as e:
            print(f"⚠️ Leader synthesis failed: {e}")
            return self._extract_final_answer(draft.content, draft.task_type)
    
    def _is_code_complete(self, code: str) -> bool:
        try:
            open_parens = code.count('(') - code.count(')')
            open_brackets = code.count('[') - code.count(']')
            open_braces = code.count('{') - code.count('}')
            
            if open_parens != 0 or open_brackets != 0 or open_braces != 0:
                return False
            
            if not code.strip().startswith('def '):
                return False
                
            lines = code.strip().split('\n')
            has_body = False
            for line in lines[1:]:
                if line.strip() and not line.strip().startswith('#'):
                    has_body = True
                    break
            
            return has_body
            
        except Exception:
            return False

    async def _leader_synthesis_fallback(self, draft: SimplifiedDraft) -> str:
        """Leader synthesis fallback method"""
        try:
            return self._extract_final_answer(draft.content, draft.task_type)
        except Exception as e:
            print(f"⚠️ Leader synthesis failed: {e}")
            return self._extract_final_answer(draft.content, draft.task_type)

    def _extract_final_answer(self, content: str, task_type: str) -> str:
        
        if task_type in ['mbpp', 'humaneval']:
            return self._extract_programming_answer(content)
        
        elif task_type in ['gsm8k', 'math']:
            return self._extract_math_answer(content)
        
        elif task_type in ['gpqa', 'mmlu']:
            return self._extract_multiple_choice_answer(content)
        
        elif task_type == 'strategyqa':
            return self._extract_yes_no_answer(content)
        
        elif task_type == 'hotpotqa':
            return self._extract_factual_answer(content)
        
        else:
            return self._extract_generic_answer(content)
    
    def _extract_programming_answer(self, content: str) -> str:
        import re
        
        python_blocks = re.findall(r'```python\n(.*?)\n```', content, re.DOTALL)
        if python_blocks:
            code = python_blocks[-1].strip()
            return self._ensure_code_completeness(code, content)
        
        code_blocks = re.findall(r'```\n(.*?)\n```', content, re.DOTALL)
        if code_blocks:
            code = code_blocks[-1].strip()
            return self._ensure_code_completeness(code, content)
            
        lines = content.split('\n')
        
        code_lines = []
        in_function = False
        function_indent = 0
        
        for line in lines:
            if line.strip().startswith('def '):
                in_function = True
                function_indent = len(line) - len(line.lstrip())
                code_lines = [line]
            elif in_function:
                if line.strip() == '':
                    code_lines.append(line)
                elif len(line) - len(line.lstrip()) > function_indent:
                    code_lines.append(line)
                else:
                    if line.strip():
                        break
        
        if code_lines:
            code = '\n'.join(code_lines)
            return self._ensure_code_completeness(code, content)
        
        def_lines = [line for line in lines if 'def ' in line or 'return ' in line]
        if def_lines:
            code = '\n'.join(def_lines)
            return self._ensure_code_completeness(code, content)
        
        return self._ensure_code_completeness(content, content)

    def _ensure_code_completeness(self, code: str, original_content: str) -> str:
        if not code:
            return code
            
        lines = code.split('\n')
        
        if any(line.strip().startswith('def ') for line in lines):
            has_return_or_pass = any('return ' in line or line.strip() == 'pass' 
                                   for line in lines)
            
            last_line = lines[-1].strip()
            
            truncation_patterns = [
                ('==', (' 0', ' n', ' 1')),
                ('!=', (' 0', ' None')),
                ('<', (' n', ' 0')),
                ('>', (' 0', ' n')),
                ('if ', (':', ':\n        pass')),
                ('or ', (':', ':\n        pass')), 
                ('and ', (':', ':\n        pass')),
                ('(', (')', '):')),
                ('=', (' []', ' None', ' 0')),
                ('.', ('isalpha()', 'isdigit()', 'lower()')),
                ('for ', ('in []', 'in range(n)')),
                ('if ', (':', ':\n        pass')),
            ]
            
            for pattern, fixes in truncation_patterns:
                if last_line.endswith(pattern):
                    fix = fixes[0]
                    lines[-1] = lines[-1] + fix
                    break
            
            if not has_return_or_pass:
                for i in range(len(lines) - 1, -1, -1):
                    if lines[i].strip():
                        indent = '    '
                        if lines[i].startswith('    '):
                            indent = '    '
                        lines.append(indent + 'pass')
                        break
        
        code_with_imports = self._add_missing_imports('\n'.join(lines))
        
        code_with_correct_name = self._validate_and_fix_function_name(code_with_imports, original_content)
        
        return code_with_correct_name
    
    def _add_missing_imports(self, code: str) -> str:
        imports_needed = []
        
        if 're.' in code and 'import re' not in code:
            imports_needed.append('import re')
        if 'List[' in code and 'from typing import' not in code:
            imports_needed.append('from typing import List, Union, Optional')
        if 'Union[' in code and 'from typing import' not in code:
            imports_needed.append('from typing import List, Union, Optional')
        if 'math.' in code and 'import math' not in code:
            imports_needed.append('import math')
        if 'random.' in code and 'import random' not in code:
            imports_needed.append('import random')
            
        if imports_needed:
            imports_needed = list(set(imports_needed))
            return '\n'.join(imports_needed) + '\n\n' + code
        
        return code

    def _validate_function_name(self, code: str, expected_patterns: list = None) -> str:
        if not code or not expected_patterns:
            return code
            
        import re
        lines = code.split('\n')
        
        for i, line in enumerate(lines):
            if line.strip().startswith('def '):
                match = re.search(r'def\s+(\w+)', line)
                if match:
                    current_name = match.group(1)
                    
                    for expected in expected_patterns:
                        if expected != current_name:
                            lines[i] = re.sub(r'def\s+\w+', f'def {expected}', line)
                            break
                    break
        
        return '\n'.join(lines)

    def _validate_and_fix_function_name(self, code: str, original_content: str) -> str:
        import re
        
        expected_name = None
        for line in original_content.split('\n'):
            if 'def ' in line and '(' in line:
                match = re.search(r'def\s+(\w+)\s*\(', line)
                if match:
                    expected_name = match.group(1)
                    break
        
        if not expected_name:
            return code
        
        lines = code.split('\n')
        for i, line in enumerate(lines):
            if line.strip().startswith('def '):
                match = re.search(r'def\s+(\w+)\s*\(', line)
                if match:
                    actual_name = match.group(1)
                    if actual_name != expected_name:
                        lines[i] = re.sub(r'def\s+\w+\s*\(', f'def {expected_name}(', line)
                        print(f"      🔧 Fixed function name: {actual_name} → {expected_name}")
                    break
        
        return '\n'.join(lines)
    
    def _extract_math_answer(self, content: str) -> str:
        import re
        
        if '####' in content:
            lines = content.split('\n')
            for line in lines:
                if line.strip().startswith('####'):
                    answer = line.strip()[4:].strip()
                    if answer:
                        return answer
        
        lines = content.split('\n')
        for line in reversed(lines):
            line = line.strip()
            if not line:
                continue
                
            math_patterns = [
                r'\d+/\d+',
                r'\d+\s*\+\s*\d+√\d+',
                r'\d+√\d+',
                r'\d+\s*\*\s*π',
                r'\d+π',
                r'\d+\.\d+',
                r'-?\d+',
            ]
            
            for pattern in math_patterns:
                match = re.search(pattern, line)
                if match:
                    return match.group(0)
            
            if any(symbol in line for symbol in ['√', 'π', '/', '+', '-', '=']) and any(c.isdigit() for c in line):
                return line
        
        numbers = re.findall(r'-?\d+(?:\.\d+)?', content)
        if numbers:
            return numbers[-1]
        
        return content[:50]
    
    def _extract_multiple_choice_answer(self, content: str) -> str:
        import re
        
        if '####' in content:
            lines = content.split('\n')
            for line in lines:
                if line.strip().startswith('####'):
                    answer = line.strip()[4:].strip().upper()
                    if answer in ['A', 'B', 'C', 'D']:
                        return answer
        
        answer_patterns = [
            r"(?:Final answer|Answer|Best answer|Correct answer):\s*([ABCD])",
            r"(?:The answer is|Answer is):\s*([ABCD])",
            r"(?:Therefore|Thus|So),?\s*(?:the answer is)?\s*([ABCD])",
            r"(?:Option|Choice)\s+([ABCD])\s+is\s+correct"
        ]
        
        for pattern in answer_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                return match.group(1).upper()
        
        lines = content.split('\n')
        for line in reversed(lines):
            line = line.strip().upper()
            if line in ['A', 'B', 'C', 'D']:
                return line
            if line.startswith(('- A', '- B', '- C', '- D', '• A', '• B', '• C', '• D')):
                return line.split()[-1]
        
        content_upper = content.upper()
        positions = []
        for choice in ['A', 'B', 'C', 'D']:
            pos = content_upper.rfind(choice)
            if pos != -1:
                positions.append((pos, choice))
        
        if positions:
            last_choice = max(positions, key=lambda x: x[0])[1]
            return last_choice
        
        return content[:50]
    
    def _extract_yes_no_answer(self, content: str) -> str:
        import re
        
        if '####' in content:
            lines = content.split('\n')
            for line in lines:
                if line.strip().startswith('####'):
                    answer = line.strip()[4:].strip().lower()
                    if answer in ['yes', 'no']:
                        return answer
        
        answer_patterns = [
            r"(?:Final answer|Answer|Conclusion):\s*(yes|no)",
            r"(?:The answer is|Answer is):\s*(yes|no)",
            r"(?:Therefore|Thus|So),?\s*(yes|no)"
        ]
        
        for pattern in answer_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                return match.group(1).lower()
        
        content_lower = content.lower()
        lines = content_lower.split('\n')
        
        for line in reversed(lines):
            line = line.strip()
            if line == 'yes' or line == 'no':
                return line
            if line.startswith(('- yes', '- no', '• yes', '• no')):
                return line.split()[-1]
        
        yes_pos = content_lower.rfind('yes')
        no_pos = content_lower.rfind('no')
        
        if yes_pos > no_pos and yes_pos != -1:
            return 'yes'
        elif no_pos > yes_pos and no_pos != -1:
            return 'no'
        
        return content[:100]
    
    def _extract_factual_answer(self, content: str) -> str:
        import re

        answer_patterns = [
            r"(?:Answer|ANSWER):\s*(.+?)(?:\n|$)",
            r"(?:Final Answer|FINAL ANSWER):\s*(.+?)(?:\n|$)",
            r"(?:The answer is|Answer is):\s*(.+?)(?:\n|$)",
            r"####\s*(.+?)(?:\n|$)"
        ]

        for pattern in answer_patterns:
            match = re.search(pattern, content, re.IGNORECASE | re.MULTILINE)
            if match:
                answer = match.group(1).strip()
                if answer and len(answer) < 200:
                    return answer

        meta_patterns = [
            r"^(?:Key improvements?|The draft now|Improved draft|Enhanced draft)",
            r"^(?:\d+\.\s*)?(?:Business type|Geographic reach|Entity \d+)",
            r"^(?:Here's|This|The improved|The enhanced)",
            r"^(?:WINNER_[A-Z]|Agent \w+|Consensus)"
        ]

        lines = content.split('\n')
        valid_lines = []

        for line in lines:
            line = line.strip()
            if not line:
                continue

            is_meta = any(re.match(pattern, line, re.IGNORECASE) for pattern in meta_patterns)
            if is_meta:
                continue

            if len(line) > 150:
                continue

            valid_lines.append(line)

        for line in reversed(valid_lines[-5:]):
            if line.startswith(('-', '•', '*')):
                answer_match = re.search(r"-\s*(?:Answer|ANSWER):\s*(.+)", line, re.IGNORECASE)
                if answer_match:
                    return answer_match.group(1).strip()
                continue

            if len(line) < 100 and not line.endswith(':'):
                if (re.search(r'\b(?:Group|Company|Corporation)\b', line, re.IGNORECASE) or
                    re.search(r'\b[A-Z][a-z]+ [A-Z][a-z]+\b', line) or
                    re.search(r'\b(?:January|February|March|April|May|June|July|August|September|October|November|December)\b', line, re.IGNORECASE) or
                    re.search(r'\b\d{4}\b', line) or
                    re.search(r'\b(?:The|A) [A-Z][a-z]+ [A-Z][a-z]+\b', line)):
                    return line

        if valid_lines:
            return valid_lines[-1]

        return content[:100].strip()
    
    def _extract_generic_answer(self, content: str) -> str:
        if '####' in content:
            lines = content.split('\n')
            for line in lines:
                if line.strip().startswith('####'):
                    answer = line.strip()[4:].strip()
                    return answer if answer else line.strip()
        
        lines = content.split('\n')
        for line in reversed(lines):
            line = line.strip()
            if line:
                return line
        
        return content

    def _create_fast_verify_prompt(self, draft: SimplifiedDraft) -> str:

        if draft.task_type in ["gsm8k", "math"]:
            return f"""Carefully verify this math solution on a scale of 0.0-1.0:

QUESTION: {draft.question}
DRAFT: {draft.content}

CRITICAL VERIFICATION:
1. Check each calculation step for arithmetic errors
2. Verify unit conversions and logic
3. Ensure the final answer makes sense
4. Rate 0.95+ ONLY if ALL calculations are verified correct

Rate the mathematical accuracy (0.0-1.0):
Respond with just the score: 0.XX"""
        else:
            return f"""Quickly evaluate this draft's overall quality on a scale of 0.0-1.0:

QUESTION: {draft.question}
DRAFT: {draft.content}

Rate the overall quality (0.0-1.0) considering accuracy, completeness, and clarity.
Respond with just the score: 0.XX"""

    def _parse_overall_score(self, result: str) -> float:
        try:
            import re
            match = re.search(r'0\.\d+', result)
            if match:
                return float(match.group())

            match = re.search(r'\d+\.\d+', result)
            if match:
                score = float(match.group())
                return min(1.0, max(0.0, score))

            return 0.6
        except:
            return 0.6

    def _get_quality_threshold(self, task_type: str) -> float:
        thresholds = {
            'gsm8k': 0.95,
            'math': 0.80,
            'mbpp': 0.75,
            'humaneval': 0.65,

            'hotpotqa': 0.70,
            'strategyqa': 0.75,
            'gpqa': 0.75,
            'mmlu': 0.85,
        }
        return thresholds.get(task_type, 0.80)

    def _leader_extract_final_answer(self, draft: SimplifiedDraft) -> str:

        if draft.task_type in ["gsm8k", "math"]:
            import re
            match = re.search(r'####\s*(\d+)', draft.content)
            if match:
                return match.group(1)

            numbers = re.findall(r'\b\d+\b', draft.content)
            if numbers:
                return numbers[-1]

        elif draft.task_type in ["mbpp", "humaneval"]:
            return self._extract_programming_answer(draft.content)

        else:
            content = draft.content.lower()
            if "answer:" in content:
                answer_part = draft.content.split("answer:")[-1].strip()
                return answer_part.split('\n')[0].strip()

            lines = draft.content.split('\n')
            for line in reversed(lines):
                line = line.strip()
                if line and len(line) < 100 and not line.startswith(('step', 'calculate', '-')):
                    return line

        return draft.content.strip()[-50:] if draft.content else "No answer found"
    
    def _score_draft_quality(self, content: str, agent_id: str, task_type: str) -> float:
        if not content or not content.strip():
            return 0.0
        
        score = 0.0
        content_lower = content.lower()
        
        base_score = min(len(content) / 500, 1.0) * 0.3
        score += base_score
        
        if task_type in ['gsm8k', 'math']:
            score += self._score_math_draft(content, content_lower)
        elif task_type in ['mbpp', 'humaneval']:
            score += self._score_programming_draft(content, content_lower)
        elif task_type == 'hotpotqa':
            score += self._score_reasoning_draft(content, content_lower, task_type)
        elif task_type in ['gpqa', 'mmlu']:
            score += self._score_knowledge_draft(content, content_lower)
        elif task_type == 'strategyqa':
            score += self._score_yesno_draft(content, content_lower)
        else:
            score += self._score_generic_draft(content, content_lower)
        
        agent_bonus = self._get_agent_task_bonus(agent_id, task_type)
        score += agent_bonus
        
        return min(score, 1.0)
    
    def _score_math_draft(self, content: str, content_lower: str) -> float:
        score = 0.0
        
        if any(marker in content_lower for marker in ['step', '-', '1.', '2.']):
            score += 0.2
        
        if any(op in content for op in ['=', '+', '-', '*', '/', '×', '÷']):
            score += 0.2
        
        if '####' in content:
            score += 0.2
        
        math_terms = ['calculate', 'equation', 'solve', 'result', 'answer']
        if any(term in content_lower for term in math_terms):
            score += 0.1
        
        return score
    
    def _score_programming_draft(self, content: str, content_lower: str) -> float:
        score = 0.0
        
        if 'def ' in content:
            score += 0.25
            if 'def ' in content and '(' in content and ')' in content and ':' in content:
                score += 0.15
        
        if 'return' in content_lower:
            score += 0.2
        
        truncation_indicators = ['==', '!=', 'if ', 'for ', 'or ', 'and ', '.']
        last_line = content.strip().split('\n')[-1].strip() if content.strip() else ''
        has_truncation = any(last_line.endswith(indicator) for indicator in truncation_indicators)
        
        if not has_truncation:
            score += 0.15
        
        logic_keywords = ['if', 'for', 'while', 'try', 'with', 'elif', 'else']
        logic_count = sum(1 for keyword in logic_keywords if keyword in content_lower)
        score += min(logic_count * 0.05, 0.15)
        
        builtin_usage = ['len(', 'range(', 'sum(', 'max(', 'min(', 'str(', 'int(', 'list(']
        builtin_count = sum(1 for builtin in builtin_usage if builtin in content_lower)
        score += min(builtin_count * 0.02, 0.1)
        
        if any(char in content for char in ['_', 'result', 'temp', 'count']):
            score += 0.05
        
        edge_case_indicators = ['not ', 'empty', 'none', 'null', '== 0', 'len(']
        if any(indicator in content_lower for indicator in edge_case_indicators):
            score += 0.1
        
        return score
    
    def _score_reasoning_draft(self, content: str, content_lower: str, task_type: str = None) -> float:
        score = 0.0
        
        reasoning_markers = ['entity', 'connection', 'evidence', 'therefore', 'because']
        if any(marker in content_lower for marker in reasoning_markers):
            score += 0.2
        
        if len(content.split('.')) > 3:
            score += 0.2
        
        if any(word in content_lower for word in ['fact', 'information', 'according', 'based']):
            score += 0.1
        
        if task_type == 'hotpotqa':
            if any(word in content_lower for word in ['bridge', 'connect', 'relationship']):
                score += 0.2
        
        return score
    
    def _score_knowledge_draft(self, content: str, content_lower: str) -> float:
        score = 0.0
        
        if any(opt in content for opt in ['A)', 'B)', 'C)', 'D)', '(A)', '(B)', '(C)', '(D)']):
            score += 0.25
        
        reasoning_words = ['therefore', 'thus', 'hence', 'because', 'since', 'given that', 'based on']
        reasoning_count = sum(1 for word in reasoning_words if word in content_lower)
        score += min(reasoning_count * 0.05, 0.15)
        
        academic_terms = ['theory', 'principle', 'concept', 'according to', 'research', 
                         'evidence', 'study', 'analysis', 'hypothesis', 'method', 'data',
                         'experiment', 'observation', 'conclusion', 'phenomenon']
        term_count = sum(1 for term in academic_terms if term in content_lower)
        score += min(term_count * 0.03, 0.2)
        
        domain_indicators = ['biology', 'chemistry', 'physics', 'mathematics', 'psychology',
                           'history', 'economics', 'literature', 'philosophy', 'geology']
        if any(domain in content_lower for domain in domain_indicators):
            score += 0.1
        
        if any(char.isdigit() for char in content):
            score += 0.05
        
        if '####' in content:
            score += 0.15
        
        return score
    
    def _score_yesno_draft(self, content: str, content_lower: str) -> float:
        score = 0.0
        
        if any(answer in content_lower for answer in ['yes', 'no']):
            score += 0.35
        
        reasoning_words = ['because', 'since', 'reason', 'logic', 'therefore', 'thus', 
                          'given that', 'considering', 'based on', 'evidence']
        reasoning_count = sum(1 for word in reasoning_words if word in content_lower)
        score += min(reasoning_count * 0.05, 0.2)
        
        if any(marker in content for marker in ['1.', '2.', 'first', 'second', 'then', 'next']):
            score += 0.15
        
        if any(word in content_lower for word in ['assume', 'suppose', 'if', 'hypothesis']):
            score += 0.1
        
        if '####' in content:
            score += 0.15
        
        if any(word in content_lower for word in ['however', 'but', 'although', 'despite']):
            score += 0.05
        
        return score
    
    def _score_generic_draft(self, content: str, content_lower: str) -> float:
        score = 0.0
        
        if any(marker in content for marker in ['-', '1.', '2.', '•']):
            score += 0.2
        
        if '####' in content or 'answer:' in content_lower:
            score += 0.2
        
        return score
    
    def _get_agent_task_bonus(self, agent_id: str, task_type: str) -> float:
        bonuses = {
            ('openai', 'math'): 0.05,
            ('anthropic', 'hotpotqa'): 0.05,  
            ('llama', 'mbpp'): 0.05,
            ('gemini', 'gpqa'): 0.05,
            ('gemini', 'mmlu'): 0.05
        }
        
        return bonuses.get((agent_id, task_type), 0.0)