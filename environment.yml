name: MAS
channels:
  - conda-forge
  - defaults
dependencies:
  - bzip2=1.0.8=h99b78c6_7
  - ca-certificates=2025.7.14=hbd8a1cb_0
  - libexpat=2.7.0=h286801f_0
  - libffi=3.4.6=h1da3d7d_1
  - liblzma=5.8.1=h39f12f2_0
  - libsqlite=3.49.1=h3f77e49_2
  - libzlib=1.3.1=h8359307_2
  - ncurses=6.5=h5e97a16_3
  - openssl=3.5.1=h81ee809_0
  - parallel=20250622=hce30654_0
  - perl=5.32.1=7_h4614cfb_perl5
  - pip=25.1=pyh8b19718_0
  - python=3.10.17=h6cefb37_0_cpython
  - readline=8.2=h1d1bf99_2
  - setuptools=79.0.1=pyhff2d567_0
  - tk=8.6.13=h5083fa2_1
  - wheel=0.45.1=pyhd8ed1ab_1
  - pip:
      - aiofiles==24.1.0
      - aiohappyeyeballs==2.6.1
      - aiohttp==3.11.18
      - aiosignal==1.3.2
      - annotated-types==0.7.0
      - anthropic==0.50.0
      - antlr4-python3-runtime==4.13.2
      - anyio==4.9.0
      - async-timeout==4.0.3
      - attrs==25.3.0
      - beautifulsoup4==4.13.4
      - cachetools==5.5.2
      - certifi==2025.4.26
      - charset-normalizer==3.4.1
      - click==8.1.8
      - colorama==0.4.6
      - contourpy==1.3.2
      - cycler==0.12.1
      - dataclasses-json==0.6.7
      - datasets==3.6.0
      - decorator==5.2.1
      - dill==0.3.8
      - distro==1.9.0
      - exceptiongroup==1.2.2
      - fastapi==0.115.12
      - filelock==3.18.0
      - fonttools==4.59.0
      - frozenlist==1.6.0
      - fsspec==2025.3.0
      - google==3.0.0
      - google-ai-generativelanguage==0.6.15
      - google-api-core==2.25.0rc1
      - google-api-python-client==2.169.0
      - google-auth==2.40.1
      - google-auth-httplib2==0.2.0
      - google-genai==1.15.0
      - google-generativeai==0.8.5
      - googleapis-common-protos==1.70.0
      - groq==0.24.0
      - grpcio==1.71.0
      - grpcio-status==1.71.0
      - h11==0.16.0
      - hf-xet==1.1.0
      - httpcore==1.0.9
      - httplib2==0.22.0
      - httpx==0.28.1
      - httpx-sse==0.4.0
      - huggingface-hub==0.31.1
      - idna==3.10
      - iniconfig==2.1.0
      - jinja2==3.1.6
      - jiter==0.9.0
      - jsonlines==4.0.0
      - jsonpatch==1.33
      - jsonpointer==3.0.0
      - kiwisolver==1.4.8
      - langchain==0.3.24
      - langchain-anthropic==0.3.12
      - langchain-community==0.3.23
      - langchain-core==0.3.58
      - langchain-groq==0.3.2
      - langchain-openai==0.3.16
      - langchain-text-splitters==0.3.8
      - langgraph==0.4.2
      - langgraph-checkpoint==2.0.25
      - langgraph-prebuilt==0.1.8
      - langgraph-sdk==0.1.66
      - langsmith==0.3.39
      - markupsafe==3.0.2
      - marshmallow==3.26.1
      - matplotlib==3.10.3
      - mpmath==1.3.0
      - multidict==6.4.3
      - multiprocess==0.70.16
      - mypy-extensions==1.1.0
      - networkx==3.4.2
      - numpy==2.2.5
      - openai==1.78.0
      - orjson==3.10.18
      - ormsgpack==1.9.1
      - packaging==24.2
      - pandas==2.2.3
      - pillow==11.3.0
      - pluggy==1.5.0
      - prettytable==3.16.0
      - propcache==0.3.1
      - proto-plus==1.26.1
      - protobuf==5.29.4
      - psutil==7.0.0
      - py==1.11.0
      - py-cpuinfo==9.0.0
      - pyarrow==20.0.0
      - pyasn1==0.6.1
      - pyasn1-modules==0.4.2
      - pycryptodome==3.9.9
      - pydantic==2.11.4
      - pydantic-core==2.33.2
      - pydantic-settings==2.9.1
      - pyparsing==3.2.3
      - pytest==8.3.5
      - pytest-benchmark==5.1.0
      - python-dateutil==2.9.0.post0
      - python-dotenv==1.1.0
      - pytz==2020.5
      - pyyaml==6.0.2
      - regex==2024.11.6
      - requests==2.32.3
      - requests-toolbelt==1.0.0
      - retry==0.9.2
      - rsa==4.9.1
      - safetensors==0.5.3
      - seaborn==0.13.2
      - six==1.17.0
      - sniffio==1.3.1
      - soupsieve==2.7
      - sqlalchemy==2.0.40
      - starlette==0.46.2
      - sympy==1.14.0
      - tabulate==0.9.0
      - tenacity==9.1.2
      - tiktoken==0.9.0
      - tokenizers==0.21.2
      - tomli==2.2.1
      - torch==2.7.1
      - tqdm==4.67.1
      - transformers==4.53.2
      - tree-sitter==0.24.0
      - typing-extensions==4.13.2
      - typing-inspect==0.9.0
      - typing-inspection==0.4.0
      - tzdata==2025.2
      - uritemplate==4.1.1
      - urllib3==2.4.0
      - uvicorn==0.34.2
      - volcengine==1.0.183
      - wcwidth==0.2.13
      - websockets==15.0.1
      - xxhash==3.5.0
      - yarl==1.20.0
      - zstandard==0.23.0
prefix: /opt/anaconda3/envs/MAS
