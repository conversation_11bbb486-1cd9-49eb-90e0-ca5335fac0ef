# DMC Framework Architecture Documentation

## System Overview

The Dynamic Multi-Agent Collaboration (DMC) framework implements a novel multi-agent system for complex reasoning tasks. The architecture is designed around four key principles:

1. **Adaptive Collaboration**: Dynamic adjustment of collaboration intensity
2. **Consensus-Based Decision Making**: Structured agreement mechanisms
3. **Intelligent Merge Strategies**: Automatic strategy selection
4. **Quality-Driven Processing**: Continuous quality assessment and improvement

## Core Architecture

### 1. Collaboration Controller (`coordination/controller.py`)

The central orchestrator that manages the entire collaboration workflow:

```
SimplifiedCollaborativeController
├── Worker Collaboration Phase
├── Annotation Phase  
├── Merger Phase
├── Leader Evaluation Phase
└── Feedback Loop
```

**Key Features:**
- Adaptive round determination based on task complexity
- Time-bounded collaboration with quality thresholds
- Efficiency tracking and optimization
- Context compression for long conversations

### 2. Shared Draft System (`coordination/draft.py`)

Maintains collaborative documents with version control and consensus tracking:

```
SharedDraft
├── Content Management
├── Annotation System
├── Version Control
├── Consensus Tracking
└── History Preservation
```

**Key Features:**
- Real-time collaborative editing
- Annotation consensus mechanisms
- Complete evolution history tracking
- Agent participation management

### 3. Adaptive Merge Strategy (`coordination/merge_strategy.py`)

Automatically selects optimal merge strategies based on collaboration context:

```
AdaptiveMergeStrategySelector
├── Annotation Analysis
├── Conflict Detection
├── Strategy Scoring
├── Risk Assessment
└── Performance Tracking
```

**Supported Strategies:**
- Semantic Synthesis: Deep understanding-based merging
- Consensus Voting: Democratic decision making
- Authority-Based: Expertise-weighted decisions
- Conflict Resolution: Structured disagreement handling

### 4. Agent Ecosystem

#### Worker Agents (`agent/worker/`)
- Generate initial drafts and improvements
- Provide structured annotations
- Participate in consensus building
- Respond to peer feedback

#### Leader Agent (`agent/leader/`)
- Conduct multi-dimensional quality evaluation
- Provide structured feedback
- Make final approval decisions
- Generate improvement suggestions

#### Merger Agent (`agent/merger/`)
- Integrate multiple drafts using selected strategies
- Resolve conflicts and maintain coherence
- Apply consensus-based improvements
- Ensure output quality

## Data Flow

### 1. Input Processing
```
Question Input → Task Type Detection → Agent Selection → Context Preparation
```

### 2. Collaboration Workflow
```
Worker Draft Generation → Annotation Phase → Consensus Building → Merge Strategy Selection → Content Integration → Leader Evaluation → Quality Assessment → Feedback Loop
```

### 3. Output Generation
```
Final Draft → Answer Extraction → Quality Validation → Result Packaging → Metadata Attachment
```

## Key Innovations

### 1. Adaptive Round Determination
The system automatically determines the optimal number of collaboration rounds based on:
- Task complexity analysis
- Question length and structure
- Historical performance data
- Resource constraints

### 2. Consensus-Based Annotations
Workers provide annotations that require peer consensus:
- Structured annotation format
- Agreement level tracking
- Conflict identification and resolution
- Consensus threshold management

### 3. Intelligent Context Compression
Manages long conversations efficiently:
- Token-aware compression strategies
- Semantic preservation techniques
- Adaptive compression ratios
- Performance optimization

### 4. Multi-Dimensional Quality Assessment
Comprehensive evaluation framework:
- Task-specific evaluation criteria
- Configurable dimension weights
- Structured feedback generation
- Continuous improvement loops

## Configuration System

### Task-Specific Settings (`config.py`)
```python
WORKER_TOKEN_LIMITS = {
    "humaneval": 2048,  # Programming tasks need more tokens
    "math": 1536,       # Math reasoning needs moderate tokens
    "gsm8k": 1024,      # Arithmetic problems are simpler
    # ... other tasks
}

ANNOTATION_TOKEN_LIMITS = {
    "humaneval": 150,   # Programming annotations need more space
    "math": 120,        # Math annotations need reasoning explanation
    # ... other tasks
}
```

### Evaluation Configuration (`config/evaluation_config.yaml`)
```yaml
tasks:
  gsm8k:
    dimensions:
      accuracy: {weight: 0.35, description: "Mathematical correctness"}
      completeness: {weight: 0.25, description: "Solution completeness"}
      clarity: {weight: 0.25, description: "Explanation clarity"}
      efficiency: {weight: 0.15, description: "Solution efficiency"}
```

## Performance Optimizations

### 1. Parallel Processing
- Concurrent agent execution
- Asynchronous API calls
- Batch processing capabilities
- Resource pooling

### 2. Intelligent Caching
- Response caching for repeated queries
- Context caching for efficiency
- Adaptive cache management
- Memory optimization

### 3. Token Management
- Dynamic token allocation
- Context-aware compression
- Efficient prompt engineering
- Resource monitoring

## Extensibility

### Adding New Models
1. Update provider configuration in `config.py`
2. Implement API client in `utils/api.py`
3. Add model-specific optimizations

### Adding New Benchmarks
1. Create evaluator in `evaluator/` directory
2. Add dataset configuration
3. Update prompt templates
4. Configure evaluation criteria

### Extending Collaboration Strategies
1. Define new merge strategies
2. Implement strategy logic
3. Update selection criteria
4. Add performance tracking

## Quality Assurance

### Testing Framework
- Unit tests for core components
- Integration tests for workflows
- Performance benchmarks
- Regression testing

### Monitoring and Logging
- Detailed execution logging
- Performance metrics tracking
- Error handling and recovery
- Resource usage monitoring

## Deployment Considerations

### Resource Requirements
- **CPU**: Multi-core recommended for parallel processing
- **Memory**: 4-8 GB RAM for full benchmark evaluation
- **Storage**: 2-5 GB for datasets and results
- **Network**: Stable internet for API access

### Scalability
- Horizontal scaling through agent distribution
- Vertical scaling through resource optimization
- Load balancing for API requests
- Efficient resource utilization

---

This architecture enables robust, efficient, and scalable multi-agent collaboration for complex reasoning tasks while maintaining high quality and performance standards.
