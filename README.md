# Dynamic Multi-Agent Collaboration (DMC) Framework

**AAAI 2024 Supplementary Material**

## Overview

This repository contains the complete implementation of the Dynamic Multi-Agent Collaboration (DMC) framework. DMC enables adaptive multi-agent collaboration for complex reasoning tasks through consensus-based annotations, intelligent merge strategies, and dynamic quality assessment.

## Key Innovations

- **Adaptive Collaboration**: Dynamically adjusts collaboration intensity based on task complexity
- **Consensus-Based Annotations**: Structured peer review with agreement tracking
- **Intelligent Merge Strategies**: Automatic strategy selection based on annotation analysis
- **Multi-Dimensional Quality Assessment**: Configurable evaluation with task-specific criteria

## System Architecture

### Core Components
- **Collaboration Controller** (`coordination/controller.py`): Orchestrates multi-agent workflow
- **Adaptive Merge Strategy** (`coordination/merge_strategy.py`): Selects optimal merge strategies
- **Shared Draft System** (`coordination/draft.py`): Manages collaborative documents
- **Context Compressor** (`coordination/context_compressor.py`): Intelligent context management

### Agent Types
- **Worker Agents** (`agent/worker/`): Generate drafts, provide annotations, build consensus
- **Leader Agent** (`agent/leader/`): Quality evaluation and structured feedback
- **Merger Agent** (`agent/merger/`): Integrate drafts using various strategies

## Supported Benchmarks

**Mathematical Reasoning**: GSM8K, MATH  
**Code Generation**: HumanEval, MBPP  
**Reading Comprehension**: HotpotQA, StrategyQA  
**Knowledge & Science**: MMLU, GPQA

## Quick Start

### 1. Installation
```bash
pip install -r requirements.txt
```

### 2. API Setup
```bash
export OPENAI_API_KEY="your_key"
export ANTHROPIC_API_KEY="your_key"
export GROQ_API_KEY="your_key"
export GEMINI_API_KEY="your_key"
```

### 3. Test Run
```bash
python main.py --prompt "What is 15 + 27?" --models openai --task-type gsm8k
```

## Experiments

### Single Dataset Evaluation
```bash
# GSM8K with multiple models
python main.py --jsonl benchmark/gsm8k.jsonl --models openai anthropic llama --max_problems 100 --output results/gsm8k_result.json

# HumanEval with code generation
python main.py --jsonl benchmark/humaneval.jsonl --models openai anthropic --max_problems 164 --output results/humaneval_result.json

# HotpotQA with multi-hop reasoning
python main.py --jsonl benchmark/hotpotqa.jsonl --models openai anthropic gemini --max_problems 100 --output results/hotpotqa_result.json
```

### Batch Processing
```bash
# Run all benchmarks
bash scripts/run_all.sh

# Sample evaluation (3 problems per dataset)
bash scripts/run_sample.sh
```

### Ablation Studies
```bash
# Baseline (single agent)
python main.py --baseline-mode --models openai --jsonl benchmark/gsm8k.jsonl --max_problems 50

# Without merge component
python main.py --disable-merge --models openai anthropic --jsonl benchmark/gsm8k.jsonl --max_problems 50

# Without annotations
python main.py --disable-annotation --models openai anthropic --jsonl benchmark/gsm8k.jsonl --max_problems 50

# Without leader evaluation
python main.py --disable-leader --models openai anthropic --jsonl benchmark/gsm8k.jsonl --max_problems 50

# Full DMC framework
python main.py --models openai anthropic llama --context-optimization --jsonl benchmark/gsm8k.jsonl --max_problems 50
```

## Configuration

### Model Settings
```python
DEFAULT_MODELS = {
    "openai": "gpt-4o-mini",
    "anthropic": "claude-3-5-haiku-20241022",
    "llama": "llama-3.3-70b-versatile",
    "gemini": "gemini-2.0-flash"
}
```

### Task-Specific Parameters
```python
# Token limits optimized for each task
WORKER_TOKEN_LIMITS = {
    "humaneval": 2048,  # Programming tasks
    "math": 1536,       # Complex mathematics
    "gsm8k": 1024,      # Basic arithmetic
    "hotpotqa": 1536,   # Multi-hop reasoning
    # ... other tasks
}

# Quality thresholds for each domain
QUALITY_THRESHOLDS = {
    'gsm8k': 0.95,      # High accuracy for math
    'humaneval': 0.65,  # Lower for complex coding
    'hotpotqa': 0.70,   # Moderate for reasoning
    # ... other tasks
}
```

### Collaboration Settings
```python
COLLABORATION_CONFIG = {
    "max_rounds": 3,
    "quality_threshold": 0.8,
    "max_duration": 180,
    "context_max_tokens": 2000
}
```

## Results Format

Results are saved with comprehensive metadata:

```json
{
  "metadata": {
    "timestamp": "2024-01-01T00:00:00",
    "models_used": ["openai", "anthropic"],
    "task_type": "gsm8k",
    "total_problems": 100
  },
  "results": [
    {
      "prompt": "Question text...",
      "solution": "Final answer",
      "quality_score": 0.85,
      "processing_time": 12.5,
      "collaboration_rounds": 2,
      "merge_strategy_used": "semantic_synthesis",
      "consensus_rate": 0.8
    }
  ]
}
```

## File Structure
```
DMC/
├── agent/                  # Agent implementations
│   ├── leader/            # Quality evaluation agent
│   ├── merger/            # Draft integration agent
│   └── worker/            # Collaborative worker agents
├── coordination/          # Core collaboration framework
├── evaluator/            # Benchmark-specific evaluators
├── utils/                # Utility functions
├── benchmark/            # Dataset files
├── scripts/              # Execution scripts
├── config.py            # System configuration
├── main.py              # Main entry point
└── prompt.py            # Prompt templates
```

## Performance Expectations

- **Processing Time**: 10-35 seconds per problem (varies by complexity)
- **Memory Usage**: 2-4 GB RAM for full evaluation
- **Token Efficiency**: 30-50% reduction through context compression
- **Quality Improvement**: 5-15% accuracy gain over single-agent baselines

## Evaluation

```bash
# Evaluate specific results
python evaluate.py --results-file results/gsm8k_result.json --dataset gsm8k --output-dir results/evaluation/

# Batch evaluation of all results
bash scripts/evaluate_all.sh
```

## Troubleshooting

**API Rate Limits**: Set `export MAX_CONCURRENT=5`  
**Memory Issues**: Use `--max_problems 25` for smaller batches  
**Network Timeouts**: Increase timeout in `config.py`  
**Debug Mode**: Add `--debug` flag for detailed logging

## Reproducibility

All experiments use deterministic settings:
- Fixed temperature (0.3) for all models
- Consistent random seeds
- Stable evaluation criteria
- Reproducible prompt formatting

## Citation

```bibtex
@inproceedings{dmc2024,
  title={Dynamic Multi-Agent Collaboration for Complex Reasoning Tasks},
  author={[Authors]},
  booktitle={Proceedings of the AAAI Conference on Artificial Intelligence},
  year={2024}
}
```

---

*This implementation provides complete reproducibility for all experiments reported in our AAAI submission.*
