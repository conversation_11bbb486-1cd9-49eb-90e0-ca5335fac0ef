#!/bin/bash

# Get project root
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

echo "Evaluating benchmark results..."
echo ""

# Evaluate each dataset
for dataset in gsm8k math mbpp humaneval hotpotqa strategyqa gpqa mmlu; do
    answer_file="results/answers/${dataset}_result_answers.json"
    output_dir="results/benchmark_outputs/evaluation_logs/${dataset}"
    
    if [ ! -f "$answer_file" ]; then
        echo "${dataset}: Answer file not found"
        continue
    fi
    
    mkdir -p "$output_dir"
    
    echo -n "${dataset}: "
    source ~/.zshrc && /opt/anaconda3/envs/MAS/bin/python evaluate.py \
        --results-file "$answer_file" \
        --dataset "$dataset" \
        --output-dir "$output_dir" \
        --use-answers 2>/dev/null | grep "Accuracy" | tail -1
done

echo ""
echo "Results saved in: results/benchmark_outputs/evaluation_logs/"
