#!/bin/bash

# Simple cleanup function
cleanup() {
    echo "Stopping..."
    pkill -f "main.py.*--jsonl.*benchmark"
    exit 130
}

trap cleanup INT TERM

export MODELS="${MODELS:-openai anthropic llama}"
export OUTPUT_DIR="/Users/<USER>/Desktop/LLM_MAS/results/benchmark_outputs"
export LEADER_AGENT="${LEADER_AGENT:-gemini}"
export MERGE_AGENT_MODEL="${MERGE_AGENT_MODEL:-gemini}"
export MAX_PROBLEMS="${MAX_PROBLEMS:-100}"
mkdir -p "${OUTPUT_DIR}"

process_dataset() {
    local dataset_name="$1"
    source ~/.zshrc && /opt/anaconda3/envs/MAS/bin/python /Users/<USER>/Desktop/LLM_MAS/main.py \
        --jsonl "/Users/<USER>/Desktop/LLM_MAS/benchmark/${dataset_name}.jsonl" \
        --max_problems "${MAX_PROBLEMS}" \
        --max_rounds 1 \
        --max-collaboration-rounds 3 \
        --output "${OUTPUT_DIR}/${dataset_name}_result.json" \
        --models ${MODELS} \
        --context-optimization \
        --leader "${LEADER_AGENT}" \
        --merge-agent-model "${MERGE_AGENT_MODEL}" \
        > "${OUTPUT_DIR}/${dataset_name}_log.txt" 2>&1
}

export -f process_dataset

case "${1:-all}" in
    --quick)
        export MAX_PROBLEMS=3
        shift
        ;;
esac

DATASETS="${@:-gsm8k math mbpp humaneval hotpotqa strategyqa gpqa mmlu}"

if command -v parallel >/dev/null 2>&1; then
    parallel --will-cite -j 9 process_dataset ::: ${DATASETS}
else
    for dataset in ${DATASETS}; do
        process_dataset "$dataset"
    done
fi