#!/usr/bin/env python3
"""
真实通信复杂度实验
测量DMC、CoT-SC、LLM-Debate的实际token消耗和通信模式
"""

import asyncio
import json
import time
import numpy as np
from typing import Dict, List, Any, Tuple
from pathlib import Path
import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from coordination.controller import SimplifiedCollaborativeController
from utils.api import async_generate_completion
from utils.token_utils import TokenCounter
from config import get_config

class RealCommunicationComplexityExperiment:
    """真实通信复杂度实验类"""
    
    def __init__(self):
        self.token_counter = TokenCounter()
        self.results = []
        
        self.test_problems = [
            {
                "id": "gsm8k_1",
                "question": "A store sells apples for $2 each and oranges for $3 each. If <PERSON> buys 5 apples and 3 oranges, how much does he spend in total?",
                "task_type": "gsm8k",
                "expected_complexity": "low"
            },
            {
                "id": "math_1", 
                "question": "Find the value of x if 3x + 7 = 22.",
                "task_type": "math",
                "expected_complexity": "low"
            },
            {
                "id": "strategy_1",
                "question": "Is it true that all birds can fly?",
                "task_type": "strategyqa", 
                "expected_complexity": "medium"
            },
            {
                "id": "gsm8k_2",
                "question": "Sarah has 24 stickers. She gives 1/3 of them to her brother and 1/4 of the remaining stickers to her sister. How many stickers does Sarah have left?",
                "task_type": "gsm8k",
                "expected_complexity": "medium"
            },
            {
                "id": "math_2",
                "question": "A rectangle has a length of 12 cm and a width of 8 cm. What is its area and perimeter?",
                "task_type": "math", 
                "expected_complexity": "low"
            }
        ]
        
        self.agent_configurations = [
            {"count": 2, "agents": ["openai", "anthropic"]},
            {"count": 3, "agents": ["openai", "anthropic", "gemini"]},
            {"count": 4, "agents": ["openai", "anthropic", "gemini", "llama"]},
        ]
        
    async def run_full_experiment(self):
        """运行完整实验"""
        print("🧪 Starting Real Communication Complexity Experiment")
        print("=" * 60)
        
        for agent_config in self.agent_configurations:
            agent_count = agent_config["count"]
            agents = agent_config["agents"]
            
            print(f"\n📊 Testing with {agent_count} agents: {agents}")
            
            for problem in self.test_problems:
                print(f"\n🔍 Problem: {problem['id']} ({problem['expected_complexity']})")
                
                dmc_result = await self.test_dmc_real(problem, agents)
                
                cotsc_result = await self.test_cotsc_real(problem, agents)
                
                debate_result = await self.test_debate_real(problem, agents)
                
                self.results.append({
                    "problem_id": problem["id"],
                    "task_type": problem["task_type"],
                    "agent_count": agent_count,
                    "agents": agents,
                    "dmc": dmc_result,
                    "cotsc": cotsc_result,
                    "debate": debate_result,
                    "timestamp": time.time()
                })
                
                self._print_comparison(problem["id"], dmc_result, cotsc_result, debate_result)
        
        await self.save_results()
        
        self.analyze_results()
    
    async def test_dmc_real(self, problem: Dict, agents: List[str]) -> Dict[str, Any]:
        """测试真实DMC系统"""
        print(f"   🤝 Testing DMC with {len(agents)} agents...")
        
        start_time = time.time()
        total_tokens = 0
        communication_events = []
        
        try:
            controller = SimplifiedCollaborativeController()
            
            initial_tokens = 0
            
            result = await controller.process_task(
                question=problem["question"],
                task_type=problem["task_type"],
                available_agents=agents,
                problem_id=f"comm_exp_{problem['id']}"
            )
            
            if hasattr(controller, 'efficiency_tracker'):
                tracker_data = controller.efficiency_tracker.get_efficiency_summary()
                total_tokens = tracker_data.get('total_tokens', 0)
                
                if hasattr(controller.efficiency_tracker, 'operations'):
                    for op in controller.efficiency_tracker.operations:
                        communication_events.append({
                            "type": op.operation_type.value if hasattr(op.operation_type, 'value') else str(op.operation_type),
                            "agent": op.agent_id,
                            "tokens": getattr(op, 'token_count', 0),
                            "timestamp": getattr(op, 'start_time', 0)
                        })
            
            if total_tokens == 0:
                input_tokens = self.token_counter.count_tokens(problem["question"])
                output_tokens = self.token_counter.count_tokens(result.get('solution', ''))
                collaboration_tokens = len(agents) * input_tokens * 2
                total_tokens = input_tokens + output_tokens + collaboration_tokens
            
            duration = time.time() - start_time
            
            return {
                "method": "DMC",
                "success": result.get('success', True),
                "solution": result.get('solution', ''),
                "total_tokens": total_tokens,
                "duration": duration,
                "communication_events": communication_events,
                "rounds": result.get('rounds_used', 1),
                "quality_score": result.get('quality_score', 0.0)
            }
            
        except Exception as e:
            print(f"     ❌ DMC failed: {e}")
            return {
                "method": "DMC",
                "success": False,
                "error": str(e),
                "total_tokens": 0,
                "duration": time.time() - start_time,
                "communication_events": []
            }
    
    async def test_cotsc_real(self, problem: Dict, agents: List[str]) -> Dict[str, Any]:
        """测试真实CoT Self-Consistency - 单智能体多次采样"""
        print(f"   🧠 Testing CoT-SC (single agent, multiple samples)...")

        start_time = time.time()
        total_tokens = 0
        communication_events = []

        try:
            primary_agent = agents[0]
            num_samples = 3
            all_responses = []

            for sample_idx in range(num_samples):
                prompt = f"""Solve this problem step by step. Think through each step carefully.

Problem: {problem['question']}

Let me think step by step:"""

                response = await async_generate_completion(
                    agent_id=primary_agent,
                    prompt=prompt,
                    system_prompt="You are an expert problem solver. Think step by step and show your reasoning clearly.",
                    temperature=0.7,
                    max_tokens=1024
                )

                if response:
                    all_responses.append(response)

                    input_tokens = self.token_counter.count_tokens(prompt)
                    output_tokens = self.token_counter.count_tokens(response)
                    total_tokens += input_tokens + output_tokens

                    communication_events.append({
                        "type": "sample_generation",
                        "agent": primary_agent,
                        "sample": sample_idx,
                        "tokens": input_tokens + output_tokens,
                        "timestamp": time.time()
                    })
            
            if len(all_responses) > 1:
                consistency_prompt = f"""Here are {len(all_responses)} different solutions to the same problem:

Problem: {problem['question']}

Solutions:
""" + "\n\n".join([f"Solution {i+1}: {resp}" for i, resp in enumerate(all_responses)]) + """

Determine the most consistent and correct answer. Provide the final answer:"""
                
                final_answer = await async_generate_completion(
                    agent_id=primary_agent,
                    prompt=consistency_prompt,
                    system_prompt="Choose the most consistent and correct solution.",
                    temperature=0.3,
                    max_tokens=512
                )

                input_tokens = self.token_counter.count_tokens(consistency_prompt)
                output_tokens = self.token_counter.count_tokens(final_answer or "")
                total_tokens += input_tokens + output_tokens

                communication_events.append({
                    "type": "consistency_check",
                    "agent": primary_agent,
                    "tokens": input_tokens + output_tokens,
                    "timestamp": time.time()
                })
            else:
                final_answer = all_responses[0] if all_responses else ""
            
            duration = time.time() - start_time
            
            return {
                "method": "CoT-SC",
                "success": len(all_responses) > 0,
                "solution": final_answer,
                "total_tokens": total_tokens,
                "duration": duration,
                "communication_events": communication_events,
                "samples_generated": len(all_responses),
                "agents_used": 1,
                "note": "Single-agent method with multiple samples"
            }
            
        except Exception as e:
            print(f"     ❌ CoT-SC failed: {e}")
            return {
                "method": "CoT-SC",
                "success": False,
                "error": str(e),
                "total_tokens": total_tokens,
                "duration": time.time() - start_time,
                "communication_events": communication_events
            }
    
    async def test_debate_real(self, problem: Dict, agents: List[str]) -> Dict[str, Any]:
        """测试真实LLM-Debate"""
        print(f"   🥊 Testing LLM-Debate with {len(agents)} agents...")
        
        start_time = time.time()
        total_tokens = 0
        communication_events = []
        
        try:
            initial_answers = {}
            
            for agent in agents:
                prompt = f"Solve this problem independently:\n\n{problem['question']}\n\nProvide your complete solution:"
                
                response = await async_generate_completion(
                    agent_id=agent,
                    prompt=prompt,
                    system_prompt="You are an expert. Solve problems independently.",
                    temperature=0.3,
                    max_tokens=1024
                )
                
                if response:
                    initial_answers[agent] = response
                    
                    input_tokens = self.token_counter.count_tokens(prompt)
                    output_tokens = self.token_counter.count_tokens(response)
                    total_tokens += input_tokens + output_tokens
                    
                    communication_events.append({
                        "type": "independent_answer",
                        "agent": agent,
                        "tokens": input_tokens + output_tokens,
                        "timestamp": time.time()
                    })
            
            if len(initial_answers) < 2:
                raise Exception("Insufficient initial answers for debate")
            
            debate_responses = {}
            
            for agent in agents:
                if agent not in initial_answers:
                    continue
                
                others_answers = []
                for other_agent, answer in initial_answers.items():
                    if other_agent != agent:
                        others_answers.append(f"{other_agent.upper()}: {answer}")
                
                others_text = "\n\n".join(others_answers)
                
                debate_prompt = f"""Original problem: {problem['question']}

Your initial answer: {initial_answers[agent]}

Other agents' answers:
{others_text}

Now engage in debate and provide your refined final answer:"""
                
                response = await async_generate_completion(
                    agent_id=agent,
                    prompt=debate_prompt,
                    system_prompt="Engage in constructive debate and refine your answer.",
                    temperature=0.3,
                    max_tokens=1024
                )
                
                if response:
                    debate_responses[agent] = response
                    
                    input_tokens = self.token_counter.count_tokens(debate_prompt)
                    output_tokens = self.token_counter.count_tokens(response)
                    total_tokens += input_tokens + output_tokens
                    
                    communication_events.append({
                        "type": "debate_response",
                        "agent": agent,
                        "tokens": input_tokens + output_tokens,
                        "timestamp": time.time()
                    })
            
            if debate_responses:
                judge_agent = agents[0]
                
                all_debates = "\n\n".join([
                    f"{agent}: {response}" for agent, response in debate_responses.items()
                ])
                
                final_prompt = f"""Problem: {problem['question']}

All debate responses:
{all_debates}

As the judge, synthesize the best solution from all the debates:"""
                
                final_answer = await async_generate_completion(
                    agent_id=judge_agent,
                    prompt=final_prompt,
                    system_prompt="You are the judge. Synthesize the best solution from all debates.",
                    temperature=0.3,
                    max_tokens=512
                )
                
                input_tokens = self.token_counter.count_tokens(final_prompt)
                output_tokens = self.token_counter.count_tokens(final_answer or "")
                total_tokens += input_tokens + output_tokens
                
                communication_events.append({
                    "type": "judge_decision",
                    "agent": judge_agent,
                    "tokens": input_tokens + output_tokens,
                    "timestamp": time.time()
                })
            else:
                final_answer = list(initial_answers.values())[0] if initial_answers else ""
            
            duration = time.time() - start_time
            
            return {
                "method": "LLM-Debate",
                "success": len(debate_responses) > 0,
                "solution": final_answer,
                "total_tokens": total_tokens,
                "duration": duration,
                "communication_events": communication_events,
                "initial_answers": len(initial_answers),
                "debate_responses": len(debate_responses),
                "agents_used": len(agents)
            }
            
        except Exception as e:
            print(f"     ❌ LLM-Debate failed: {e}")
            return {
                "method": "LLM-Debate",
                "success": False,
                "error": str(e),
                "total_tokens": total_tokens,
                "duration": time.time() - start_time,
                "communication_events": communication_events
            }

    def _print_comparison(self, problem_id: str, dmc: Dict, cotsc: Dict, debate: Dict):
        """打印单个问题的对比结果"""
        print(f"     📊 Results for {problem_id}:")
        print(f"       DMC:        {dmc['total_tokens']:4d} tokens, {dmc['duration']:.1f}s, {'✅' if dmc['success'] else '❌'}")
        print(f"       CoT-SC:     {cotsc['total_tokens']:4d} tokens, {cotsc['duration']:.1f}s, {'✅' if cotsc['success'] else '❌'}")
        print(f"       LLM-Debate: {debate['total_tokens']:4d} tokens, {debate['duration']:.1f}s, {'✅' if debate['success'] else '❌'}")

        if dmc['success'] and cotsc['success']:
            cotsc_ratio = cotsc['total_tokens'] / dmc['total_tokens'] if dmc['total_tokens'] > 0 else 0
            print(f"       CoT-SC uses {cotsc_ratio:.1f}x more tokens than DMC")

        if dmc['success'] and debate['success']:
            debate_ratio = debate['total_tokens'] / dmc['total_tokens'] if dmc['total_tokens'] > 0 else 0
            print(f"       LLM-Debate uses {debate_ratio:.1f}x more tokens than DMC")

    async def save_results(self):
        """保存实验结果"""
        output_dir = Path("results/experiments")
        output_dir.mkdir(parents=True, exist_ok=True)

        timestamp = int(time.time())
        output_file = output_dir / f"real_communication_complexity_{timestamp}.json"

        save_data = {
            "experiment_info": {
                "name": "Real Communication Complexity Experiment",
                "timestamp": timestamp,
                "total_tests": len(self.results),
                "agent_configurations": self.agent_configurations,
                "test_problems": self.test_problems
            },
            "results": self.results
        }

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, indent=2, ensure_ascii=False)

        print(f"\n💾 Results saved to: {output_file}")
        return output_file

    def analyze_results(self):
        """分析实验结果"""
        print("\n" + "="*60)
        print("📈 EXPERIMENT ANALYSIS")
        print("="*60)

        by_agent_count = {}
        for result in self.results:
            agent_count = result["agent_count"]
            if agent_count not in by_agent_count:
                by_agent_count[agent_count] = {"dmc": [], "cotsc": [], "debate": []}

            if result["dmc"]["success"]:
                by_agent_count[agent_count]["dmc"].append(result["dmc"]["total_tokens"])
            if result["cotsc"]["success"]:
                by_agent_count[agent_count]["cotsc"].append(result["cotsc"]["total_tokens"])
            if result["debate"]["success"]:
                by_agent_count[agent_count]["debate"].append(result["debate"]["total_tokens"])

        print("\n🔍 Communication Complexity Scaling:")
        print("Agent Count | DMC (avg) | CoT-SC (avg) | LLM-Debate (avg) | DMC Advantage")
        print("-" * 75)

        scaling_data = []
        for agent_count in sorted(by_agent_count.keys()):
            data = by_agent_count[agent_count]

            dmc_avg = np.mean(data["dmc"]) if data["dmc"] else 0
            cotsc_avg = np.mean(data["cotsc"]) if data["cotsc"] else 0
            debate_avg = np.mean(data["debate"]) if data["debate"] else 0

            cotsc_ratio = cotsc_avg / dmc_avg if dmc_avg > 0 else 0
            debate_ratio = debate_avg / dmc_avg if dmc_avg > 0 else 0

            print(f"    {agent_count:2d}      | {dmc_avg:7.0f}   | {cotsc_avg:8.0f}     | {debate_avg:10.0f}       | {cotsc_ratio:.1f}x, {debate_ratio:.1f}x")

            scaling_data.append({
                "agent_count": agent_count,
                "dmc_avg": dmc_avg,
                "cotsc_avg": cotsc_avg,
                "debate_avg": debate_avg,
                "cotsc_ratio": cotsc_ratio,
                "debate_ratio": debate_ratio
            })

        print("\n📊 Scaling Trend Analysis:")
        if len(scaling_data) >= 2:
            for i in range(1, len(scaling_data)):
                prev = scaling_data[i-1]
                curr = scaling_data[i]

                dmc_growth = (curr["dmc_avg"] - prev["dmc_avg"]) / prev["dmc_avg"] * 100 if prev["dmc_avg"] > 0 else 0
                cotsc_growth = (curr["cotsc_avg"] - prev["cotsc_avg"]) / prev["cotsc_avg"] * 100 if prev["cotsc_avg"] > 0 else 0
                debate_growth = (curr["debate_avg"] - prev["debate_avg"]) / prev["debate_avg"] * 100 if prev["debate_avg"] > 0 else 0

                print(f"  {prev['agent_count']} → {curr['agent_count']} agents:")
                print(f"    DMC growth: {dmc_growth:+.1f}%")
                print(f"    CoT-SC growth: {cotsc_growth:+.1f}%")
                print(f"    LLM-Debate growth: {debate_growth:+.1f}%")

        print("\n✅ Success Rate Analysis:")
        total_tests = len(self.results)
        dmc_success = sum(1 for r in self.results if r["dmc"]["success"])
        cotsc_success = sum(1 for r in self.results if r["cotsc"]["success"])
        debate_success = sum(1 for r in self.results if r["debate"]["success"])

        print(f"  DMC:        {dmc_success}/{total_tests} ({dmc_success/total_tests*100:.1f}%)")
        print(f"  CoT-SC:     {cotsc_success}/{total_tests} ({cotsc_success/total_tests*100:.1f}%)")
        print(f"  LLM-Debate: {debate_success}/{total_tests} ({debate_success/total_tests*100:.1f}%)")

        print("\n⚡ Overall Efficiency Comparison:")
        successful_results = [r for r in self.results if r["dmc"]["success"] and r["cotsc"]["success"] and r["debate"]["success"]]

        if successful_results:
            avg_dmc = np.mean([r["dmc"]["total_tokens"] for r in successful_results])
            avg_cotsc = np.mean([r["cotsc"]["total_tokens"] for r in successful_results])
            avg_debate = np.mean([r["debate"]["total_tokens"] for r in successful_results])

            print(f"  DMC average:        {avg_dmc:.0f} tokens")
            print(f"  CoT-SC average:     {avg_cotsc:.0f} tokens ({avg_cotsc/avg_dmc:.1f}x more)")
            print(f"  LLM-Debate average: {avg_debate:.0f} tokens ({avg_debate/avg_dmc:.1f}x more)")

            # Token reduction percentages
            cotsc_reduction = (avg_cotsc - avg_dmc) / avg_cotsc * 100
            debate_reduction = (avg_debate - avg_dmc) / avg_debate * 100

            print(f"\n💰 DMC Token Savings:")
            print(f"  vs CoT-SC:     {cotsc_reduction:.1f}% reduction")
            print(f"  vs LLM-Debate: {debate_reduction:.1f}% reduction")

async def main():
    """主函数"""
    experiment = RealCommunicationComplexityExperiment()
    await experiment.run_full_experiment()

if __name__ == "__main__":
    asyncio.run(main())
