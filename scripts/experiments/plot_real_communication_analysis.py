#!/usr/bin/env python3
"""
真实通信复杂度实验结果可视化
基于实际测量数据生成通信复杂度图表
"""

import json
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from pathlib import Path
import sys
import os
from typing import Dict, List, Any

plt.rcParams['font.family'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
plt.rcParams['font.size'] = 10
plt.rcParams['axes.linewidth'] = 1.2

def load_latest_experiment_data() -> Dict[str, Any]:
    """加载最新的实验数据"""
    results_dir = Path("results/experiments")
    
    if not results_dir.exists():
        raise FileNotFoundError("No experiment results found. Please run the experiment first.")
    
    experiment_files = list(results_dir.glob("real_communication_complexity_*.json"))
    if not experiment_files:
        raise FileNotFoundError("No real communication complexity experiment files found.")
    
    latest_file = max(experiment_files, key=lambda f: f.stat().st_mtime)
    print(f"📊 Loading data from: {latest_file}")
    
    with open(latest_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    return data

def prepare_scaling_data(results: List[Dict]) -> pd.DataFrame:
    """准备scaling分析数据"""
    scaling_data = []
    
    for result in results:
        agent_count = result["agent_count"]
        
        if result["dmc"]["success"]:
            scaling_data.append({
                "method": "DMC",
                "agent_count": agent_count,
                "tokens": result["dmc"]["total_tokens"],
                "duration": result["dmc"]["duration"],
                "problem_id": result["problem_id"]
            })
        
        if result["cotsc"]["success"]:
            scaling_data.append({
                "method": "CoT-SC", 
                "agent_count": agent_count,
                "tokens": result["cotsc"]["total_tokens"],
                "duration": result["cotsc"]["duration"],
                "problem_id": result["problem_id"]
            })
        
        if result["debate"]["success"]:
            scaling_data.append({
                "method": "LLM-Debate",
                "agent_count": agent_count, 
                "tokens": result["debate"]["total_tokens"],
                "duration": result["debate"]["duration"],
                "problem_id": result["problem_id"]
            })
    
    return pd.DataFrame(scaling_data)

def plot_communication_scaling(df: pd.DataFrame, ax):
    """绘制通信复杂度scaling图"""
    
    colors = {'DMC': '#2E8B57', 'CoT-SC': '#4682B4', 'LLM-Debate': '#DC143C'}
    markers = {'DMC': 'o', 'CoT-SC': 's', 'LLM-Debate': '^'}
    
    grouped = df.groupby(['method', 'agent_count'])['tokens'].agg(['mean', 'std']).reset_index()
    
    for method in ['DMC', 'CoT-SC', 'LLM-Debate']:
        method_data = grouped[grouped['method'] == method]
        
        if len(method_data) == 0:
            continue
            
        x = method_data['agent_count'].values
        y = method_data['mean'].values
        yerr = method_data['std'].values
        
        ax.errorbar(x, y, yerr=yerr, color=colors[method], marker=markers[method],
                   markersize=8, linewidth=2, capsize=5, capthick=2,
                   label=method, alpha=0.8)
        
        if len(x) >= 2:
            coeffs = np.polyfit(x, y, 1)
            trend_line = np.poly1d(coeffs)
            x_smooth = np.linspace(x.min(), x.max(), 100)
            ax.plot(x_smooth, trend_line(x_smooth), '--', color=colors[method], alpha=0.6)
            
            y_pred = trend_line(x)
            ss_res = np.sum((y - y_pred) ** 2)
            ss_tot = np.sum((y - np.mean(y)) ** 2)
            r2 = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0
            
            slope = coeffs[0]
            complexity_class = "Linear" if slope < 1000 else "Quadratic"
            print(f"{method}: slope={slope:.1f}, R²={r2:.3f}, {complexity_class}")
    
    ax.set_xlabel('Number of Agents', fontsize=12, fontweight='bold')
    ax.set_ylabel('Total Tokens', fontsize=12, fontweight='bold')
    ax.set_title('Real Communication Complexity Scaling', fontsize=14, fontweight='bold')
    ax.legend(frameon=True, fancybox=True, shadow=True)
    ax.grid(True, alpha=0.3)
    ax.set_xticks(range(2, 7))

def plot_efficiency_comparison(df: pd.DataFrame, ax):
    """绘制效率对比图"""
    
    method_stats = df.groupby('method')['tokens'].agg(['mean', 'std']).reset_index()
    
    methods = method_stats['method'].values
    means = method_stats['mean'].values
    stds = method_stats['std'].values
    
    colors = ['#2E8B57', '#4682B4', '#DC143C']
    
    bars = ax.bar(methods, means, yerr=stds, capsize=5, color=colors, alpha=0.8, edgecolor='white', linewidth=2)
    
    for bar, mean in zip(bars, means):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 50,
                f'{mean:.0f}', ha='center', va='bottom', fontweight='bold')
    
    ax.set_ylabel('Average Total Tokens', fontsize=12, fontweight='bold')
    ax.set_title('Real Token Consumption Comparison', fontsize=14, fontweight='bold')
    ax.grid(True, alpha=0.3, axis='y')
    
    dmc_mean = method_stats[method_stats['method'] == 'DMC']['mean'].iloc[0] if 'DMC' in methods else 0
    
    if dmc_mean > 0:
        for i, (method, mean) in enumerate(zip(methods, means)):
            if method != 'DMC':
                ratio = mean / dmc_mean
                ax.text(i, mean/2, f'{ratio:.1f}x', ha='center', va='center', 
                       fontweight='bold', fontsize=11, color='white')

def create_real_communication_analysis_figure():
    """创建真实通信分析图表"""
    
    try:
        data = load_latest_experiment_data()
        results = data["results"]
        
        print(f"📈 Analyzing {len(results)} experiment results...")
        
        df = prepare_scaling_data(results)
        
        if df.empty:
            print("❌ No successful results to plot")
            return
        
        print(f"📊 Plotting data for {len(df)} successful tests...")
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        plot_communication_scaling(df, ax1)
        
        plot_efficiency_comparison(df, ax2)
        
        plt.tight_layout()
        plt.subplots_adjust(top=0.90, bottom=0.12, left=0.08, right=0.95, wspace=0.25)
        
        output_path = Path("LaTeX/figures/real_communication_analysis_combined.png")
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"💾 Figure saved to: {output_path}")
        
        print_statistics(df)
        
        plt.show()
        
    except Exception as e:
        print(f"❌ Error creating figure: {e}")
        import traceback
        traceback.print_exc()

def print_statistics(df: pd.DataFrame):
    """打印统计信息"""
    print("\n" + "="*50)
    print("📊 REAL DATA STATISTICS")
    print("="*50)
    
    method_stats = df.groupby('method')['tokens'].agg(['count', 'mean', 'std', 'min', 'max'])
    
    print("\n📈 Method Statistics:")
    print("Method      | Count | Mean   | Std    | Min    | Max")
    print("-" * 55)
    
    for method in method_stats.index:
        stats = method_stats.loc[method]
        print(f"{method:11} | {stats['count']:5.0f} | {stats['mean']:6.0f} | {stats['std']:6.0f} | {stats['min']:6.0f} | {stats['max']:6.0f}")
    
    print("\n💰 Efficiency Comparison (vs DMC):")
    dmc_mean = method_stats.loc['DMC', 'mean'] if 'DMC' in method_stats.index else 0
    
    if dmc_mean > 0:
        for method in method_stats.index:
            if method != 'DMC':
                ratio = method_stats.loc[method, 'mean'] / dmc_mean
                reduction = (1 - 1/ratio) * 100
                print(f"  {method}: {ratio:.1f}x more tokens, DMC saves {reduction:.1f}%")
    
    print("\n📊 Scaling Analysis:")
    for method in df['method'].unique():
        method_df = df[df['method'] == method]
        scaling_stats = method_df.groupby('agent_count')['tokens'].mean()
        
        if len(scaling_stats) >= 2:
            growth_rates = []
            for i in range(1, len(scaling_stats)):
                prev_tokens = scaling_stats.iloc[i-1]
                curr_tokens = scaling_stats.iloc[i]
                growth_rate = (curr_tokens - prev_tokens) / prev_tokens * 100
                growth_rates.append(growth_rate)
            
            avg_growth = np.mean(growth_rates)
            print(f"  {method}: Average growth rate per agent: {avg_growth:.1f}%")

if __name__ == "__main__":
    create_real_communication_analysis_figure()
