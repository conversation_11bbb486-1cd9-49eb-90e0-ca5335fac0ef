#!/bin/bash

# 真实通信复杂度实验运行脚本
# 运行实验并生成可视化结果

set -e  # 遇到错误立即退出

echo "🧪 Starting Real Communication Complexity Experiment"
echo "=================================================="

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 not found. Please install Python3."
    exit 1
fi

# 检查必要的Python包
echo "📦 Checking Python dependencies..."
python3 -c "import asyncio, json, numpy, matplotlib, pandas" 2>/dev/null || {
    echo "❌ Missing required Python packages. Please install: numpy matplotlib pandas"
    exit 1
}

# 设置环境变量
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
export OUTPUT_DIR="results/experiments"

# 创建输出目录
mkdir -p "$OUTPUT_DIR"
mkdir -p "LaTeX/figures"

echo "🚀 Running real communication complexity experiment..."
echo "This may take 10-15 minutes depending on API response times..."

# 运行实验
python3 scripts/experiments/real_communication_complexity_experiment.py

# 检查实验是否成功
if [ $? -eq 0 ]; then
    echo "✅ Experiment completed successfully!"
    
    echo "📊 Generating visualization..."
    python3 scripts/experiments/plot_real_communication_analysis.py
    
    if [ $? -eq 0 ]; then
        echo "✅ Visualization generated successfully!"
        echo "📁 Results saved in:"
        echo "   - Experiment data: $OUTPUT_DIR/"
        echo "   - Figure: LaTeX/figures/real_communication_analysis_combined.png"
        
        # 显示结果文件
        echo ""
        echo "📋 Generated files:"
        ls -la "$OUTPUT_DIR"/real_communication_complexity_*.json 2>/dev/null || echo "   No experiment files found"
        ls -la LaTeX/figures/real_communication_analysis_combined.png 2>/dev/null || echo "   No figure file found"
        
    else
        echo "❌ Visualization failed, but experiment data is available"
    fi
    
else
    echo "❌ Experiment failed. Check the error messages above."
    exit 1
fi

echo ""
echo "🎉 Real Communication Complexity Experiment Complete!"
echo "You can now use the real data in your paper instead of simulated data."
