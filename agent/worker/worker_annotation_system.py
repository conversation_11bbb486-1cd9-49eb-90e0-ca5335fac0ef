#!/usr/bin/env python3

from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import uuid
import json
import asyncio

from coordination.draft import SharedDraft, DraftAnnotation, DraftSection
from utils.api import async_generate_completion
from prompt import WorkerAnnotationPrompts
from config import get_config

class AnnotationStrategy(Enum):
    COMPREHENSIVE_REVIEW = "comprehensive"   
    TARGETED_IMPROVEMENT = "targeted"         # 针对性改进
    FACT_CHECKING = "fact_checking"           # 事实检查
    STRUCTURE_OPTIMIZATION = "structure"     # 结构优化
    CLARITY_ENHANCEMENT = "clarity"          # 清晰度提升

class WorkerRole(Enum):
    """Worker角色"""
    GENERAL_REVIEWER = "general"              # 通用审查员
    DOMAIN_EXPERT = "domain_expert"          # 领域专家
    QUALITY_ASSESSOR = "quality"             # 质量评估员
    FACT_CHECKER = "fact_checker"            # 事实检查员
    EDITOR = "editor"                        # 编辑员

@dataclass
class AnnotationContext:
    """批注上下文"""
    question_type: str = ""
    question_content: str = ""
    draft_content: str = ""
    current_version: int = 1
    other_annotations: List[Dict[str, Any]] = field(default_factory=list)
    agent_role: WorkerRole = WorkerRole.GENERAL_REVIEWER
    focus_areas: List[str] = field(default_factory=list)

class WorkerAnnotationSystem:
    """
    Worker智能体批注系统
    使用LLM生成智能批注，支持不同策略和角色
    """
    
    def __init__(self):
        self.active_workers: Dict[str, Dict[str, Any]] = {}
        self.annotation_history: List[Dict[str, Any]] = []
        self.worker_specializations: Dict[str, WorkerRole] = {}
        
        # LLM配置
        self.default_model = get_config("worker_model", "openai")
        self.max_tokens = get_config("worker_max_tokens", 1024)
        self.temperature = get_config("worker_temperature", 0.4)
        
        # 批注质量阈值
        self.quality_threshold = 0.3
        
    async def register_worker(self, agent_id: str, role: WorkerRole = WorkerRole.GENERAL_REVIEWER,
                            specialization: Optional[str] = None) -> bool:
        """注册Worker智能体"""
        try:
            self.active_workers[agent_id] = {
                "role": role,
                "specialization": specialization,
                "annotations_created": 0,
                "last_active": datetime.now().isoformat(),
                "quality_score": 0.8  # 初始质量分数
            }
            
            self.worker_specializations[agent_id] = role
            return True
            
        except Exception as e:
            print(f"Failed to register worker {agent_id}: {e}")
            return False
    
    async def generate_annotations(self, agent_id: str, shared_draft: SharedDraft,
                                 strategy: AnnotationStrategy = AnnotationStrategy.COMPREHENSIVE_REVIEW,
                                 max_annotations: int = 5) -> List[str]:
        """使用LLM生成批注"""
        
        if agent_id not in self.active_workers:
            await self.register_worker(agent_id)
        
        try:
            # 构建批注上下文
            context = self._build_annotation_context(shared_draft, agent_id)
            
            # 生成批注
            annotations = await self._llm_generate_annotations(
                agent_id, context, strategy, max_annotations
            )
            
            # 应用批注到共享Draft
            annotation_ids = []
            for annotation_data in annotations:
                if self._validate_annotation(annotation_data):
                    annotation_id = self._apply_annotation_to_draft(
                        shared_draft, agent_id, annotation_data
                    )
                    if annotation_id:
                        annotation_ids.append(annotation_id)
            
            # 更新worker统计
            self._update_worker_stats(agent_id, len(annotation_ids))
            
            return annotation_ids
            
        except Exception as e:
            print(f"Error generating annotations for {agent_id}: {e}")
            return []
    
    async def respond_to_annotation(self, agent_id: str, shared_draft: SharedDraft,
                                  annotation_id: str, response_type: str = "review") -> bool:
        """Worker对其他Agent的批注进行回应"""
        
        if agent_id not in self.active_workers:
            await self.register_worker(agent_id)
        
        try:
            # 找到目标批注
            target_annotation = None
            for ann in shared_draft.annotations:
                if ann.id == annotation_id:
                    target_annotation = ann
                    break
            
            if not target_annotation:
                return False
            
            # 生成LLM驱动的回应
            response_data = await self._llm_generate_response(
                agent_id, target_annotation, shared_draft, response_type
            )
            
            if response_data:
                # 应用回应到Draft
                success = shared_draft.respond_to_annotation(
                    annotation_id,
                    agent_id,
                    response_data["response_text"],
                    response_data["agreement_level"]
                )
                
                if success:
                    self._log_annotation_activity(agent_id, "response", {
                        "annotation_id": annotation_id,
                        "response_type": response_type,
                        "agreement_level": response_data["agreement_level"]
                    })
                
                return success
            
            return False
            
        except Exception as e:
            print(f"Error responding to annotation {annotation_id}: {e}")
            return False
    
    async def analyze_draft_quality(self, agent_id: str, shared_draft: SharedDraft) -> Dict[str, Any]:
        """Worker分析Draft质量"""
        
        try:
            context = self._build_annotation_context(shared_draft, agent_id)
            
            # 使用LLM进行质量分析
            analysis = await self._llm_analyze_quality(agent_id, context)
            
            return analysis
            
        except Exception as e:
            print(f"Error analyzing draft quality: {e}")
            return {"error": str(e)}
    
    async def suggest_improvements(self, agent_id: str, shared_draft: SharedDraft,
                                 focus_area: str = "general") -> List[Dict[str, Any]]:
        """Worker建议改进方案"""
        
        try:
            context = self._build_annotation_context(shared_draft, agent_id)
            context.focus_areas = [focus_area]
            
            # 使用LLM生成改进建议
            suggestions = await self._llm_generate_improvements(agent_id, context)
            
            return suggestions
            
        except Exception as e:
            print(f"Error generating improvement suggestions: {e}")
            return []
    
    # Private methods
    
    def _build_annotation_context(self, shared_draft: SharedDraft, agent_id: str) -> AnnotationContext:
        """构建批注上下文"""
        
        # 获取其他agent的批注
        other_annotations = []
        for ann in shared_draft.annotations:
            if ann.agent_id != agent_id:
                other_annotations.append({
                    "agent_id": ann.agent_id,
                    "annotation_text": ann.annotation_text,
                    "annotation_type": ann.annotation_type,
                    "target_text": ann.target_text,
                    "priority": ann.priority
                })
        
        # 获取worker角色
        role = self.worker_specializations.get(agent_id, WorkerRole.GENERAL_REVIEWER)
        
        return AnnotationContext(
            question_type=shared_draft.question_type,
            question_content=shared_draft.question_content,
            draft_content=shared_draft.current_content,
            current_version=shared_draft.version,
            other_annotations=other_annotations,
            agent_role=role
        )
    
    async def _llm_generate_annotations(self, agent_id: str, context: AnnotationContext,
                                      strategy: AnnotationStrategy, max_annotations: int) -> List[Dict[str, Any]]:
        """使用LLM生成批注"""
        
        # 构建专业化的prompt
        prompt = WorkerAnnotationPrompts.get_annotation_generation_prompt(
            context, strategy, max_annotations
        )
        
        system_prompt = WorkerAnnotationPrompts.get_worker_system_prompt(context.agent_role)
        
        try:
            response = await async_generate_completion(
                agent_id=agent_id,
                prompt=prompt,
                system_prompt=system_prompt,
                max_tokens=self.max_tokens,
                temperature=self.temperature
            )
            
            # 解析LLM响应
            annotations = self._parse_annotation_response(response)
            return annotations
            
        except Exception as e:
            print(f"LLM annotation generation failed for {agent_id}: {e}")
            return []
    
    async def _llm_generate_response(self, agent_id: str, target_annotation: DraftAnnotation,
                                   shared_draft: SharedDraft, response_type: str) -> Optional[Dict[str, Any]]:
        """使用LLM生成对批注的回应"""
        
        prompt = WorkerAnnotationPrompts.get_annotation_response_prompt(
            target_annotation, shared_draft, response_type
        )
        
        role = self.worker_specializations.get(agent_id, WorkerRole.GENERAL_REVIEWER)
        system_prompt = WorkerAnnotationPrompts.get_worker_system_prompt(role)
        
        try:
            response = await async_generate_completion(
                agent_id=agent_id,
                prompt=prompt,
                system_prompt=system_prompt,
                max_tokens=512,
                temperature=0.3
            )
            
            # 解析回应
            response_data = self._parse_response_data(response)
            return response_data
            
        except Exception as e:
            print(f"LLM response generation failed: {e}")
            return None
    
    async def _llm_analyze_quality(self, agent_id: str, context: AnnotationContext) -> Dict[str, Any]:
        """使用LLM分析质量"""
        
        prompt = WorkerAnnotationPrompts.get_quality_analysis_prompt(context)
        
        role = self.worker_specializations.get(agent_id, WorkerRole.GENERAL_REVIEWER)
        system_prompt = WorkerAnnotationPrompts.get_worker_system_prompt(role)
        
        try:
            response = await async_generate_completion(
                agent_id=agent_id,
                prompt=prompt,
                system_prompt=system_prompt,
                max_tokens=1024,
                temperature=0.2
            )
            
            # 解析质量分析
            analysis = self._parse_quality_analysis(response)
            return analysis
            
        except Exception as e:
            print(f"LLM quality analysis failed: {e}")
            return {"error": str(e)}
    
    async def _llm_generate_improvements(self, agent_id: str, context: AnnotationContext) -> List[Dict[str, Any]]:
        """使用LLM生成改进建议"""
        
        prompt = WorkerAnnotationPrompts.get_improvement_suggestions_prompt(context)
        
        role = self.worker_specializations.get(agent_id, WorkerRole.GENERAL_REVIEWER)
        system_prompt = WorkerAnnotationPrompts.get_worker_system_prompt(role)
        
        try:
            response = await async_generate_completion(
                agent_id=agent_id,
                prompt=prompt,
                system_prompt=system_prompt,
                max_tokens=1024,
                temperature=0.4
            )
            
            # 解析改进建议
            suggestions = self._parse_improvement_suggestions(response)
            return suggestions
            
        except Exception as e:
            print(f"LLM improvement generation failed: {e}")
            return []
    
    def _parse_annotation_response(self, response: str) -> List[Dict[str, Any]]:
        """解析批注响应"""
        annotations = []
        
        try:
            # 尝试解析JSON格式
            if "```json" in response:
                json_str = response.split("```json")[1].split("```")[0].strip()
                data = json.loads(json_str)
                if "annotations" in data:
                    annotations = data["annotations"]
                elif isinstance(data, list):
                    annotations = data
            else:
                # 尝试直接解析
                lines = response.strip().split('\n')
                current_annotation = {}
                
                for line in lines:
                    line = line.strip()
                    if line.startswith("Target:"):
                        current_annotation["target_text"] = line[7:].strip()
                    elif line.startswith("Suggestion:"):
                        current_annotation["annotation_text"] = line[11:].strip()
                    elif line.startswith("Type:"):
                        current_annotation["annotation_type"] = line[5:].strip()
                    elif line.startswith("Priority:"):
                        try:
                            current_annotation["priority"] = int(line[9:].strip())
                        except:
                            current_annotation["priority"] = 1
                    elif line.startswith("---") and current_annotation:
                        annotations.append(current_annotation.copy())
                        current_annotation = {}
                
                # 添加最后一个批注
                if current_annotation:
                    annotations.append(current_annotation)
            
        except Exception as e:
            print(f"Failed to parse annotation response: {e}")
        
        return annotations
    
    def _parse_response_data(self, response: str) -> Optional[Dict[str, Any]]:
        """解析回应数据"""
        try:
            if "```json" in response:
                json_str = response.split("```json")[1].split("```")[0].strip()
                data = json.loads(json_str)
                return data
            else:
                # 简单解析
                lines = response.strip().split('\n')
                response_text = ""
                agreement_level = 0.5
                
                for line in lines:
                    if line.startswith("Response:"):
                        response_text = line[9:].strip()
                    elif line.startswith("Agreement:"):
                        try:
                            agreement_level = float(line[10:].strip())
                        except:
                            agreement_level = 0.5
                
                return {
                    "response_text": response_text or response.strip(),
                    "agreement_level": agreement_level
                }
                
        except Exception as e:
            print(f"Failed to parse response data: {e}")
            return None
    
    def _parse_quality_analysis(self, response: str) -> Dict[str, Any]:
        try:
            if "```json" in response:
                json_str = response.split("```json")[1].split("```")[0].strip()
                return json.loads(json_str)
            else:
                return {
                    "overall_score": 0.7,
                    "strengths": ["Content provided"],
                    "weaknesses": ["Needs improvement"],
                    "suggestions": ["Review and enhance"],
                    "analysis_text": response.strip()
                }
        except Exception as e:
            return {"error": str(e), "analysis_text": response}
    
    def _parse_improvement_suggestions(self, response: str) -> List[Dict[str, Any]]:
        suggestions = []
        
        try:
            if "```json" in response:
                json_str = response.split("```json")[1].split("```")[0].strip()
                data = json.loads(json_str)
                if "suggestions" in data:
                    suggestions = data["suggestions"]
                elif isinstance(data, list):
                    suggestions = data
            else:
                # 简单解析
                lines = response.strip().split('\n')
                for line in lines:
                    if line.strip() and not line.startswith('#'):
                        suggestions.append({
                            "suggestion": line.strip(),
                            "priority": 1,
                            "category": "general"
                        })
                        
        except Exception as e:
            print(f"Failed to parse improvement suggestions: {e}")
        
        return suggestions
    
    def _validate_annotation(self, annotation_data: Dict[str, Any]) -> bool:
        required_fields = ["target_text", "annotation_text"]
        
        for field in required_fields:
            if field not in annotation_data or not annotation_data[field]:
                return False
        
        if len(annotation_data["annotation_text"]) < 5:
            return False
        
        return True
    
    def _apply_annotation_to_draft(self, shared_draft: SharedDraft, agent_id: str,
                                 annotation_data: Dict[str, Any]) -> Optional[str]:
        try:
            annotation_id = shared_draft.add_annotation(
                agent_id=agent_id,
                target_text=annotation_data["target_text"],
                annotation_text=annotation_data["annotation_text"],
                annotation_type=annotation_data.get("annotation_type", "suggestion"),
                priority=annotation_data.get("priority", 1),
                section=DraftSection.FULL_CONTENT
            )
            
            return annotation_id
            
        except Exception as e:
            print(f"Failed to apply annotation to draft: {e}")
            return None
    
    def _update_worker_stats(self, agent_id: str, annotations_count: int):
        if agent_id in self.active_workers:
            self.active_workers[agent_id]["annotations_created"] += annotations_count
            self.active_workers[agent_id]["last_active"] = datetime.now().isoformat()
    
    def _log_annotation_activity(self, agent_id: str, activity_type: str, details: Dict[str, Any]):
        activity = {
            "timestamp": datetime.now().isoformat(),
            "agent_id": agent_id,
            "activity_type": activity_type,
            "details": details
        }
        
        self.annotation_history.append(activity)
        
        if len(self.annotation_history) > 1000:
            self.annotation_history = self.annotation_history[-500:]
    
    def get_worker_stats(self) -> Dict[str, Any]:
        return {
            "total_workers": len(self.active_workers),
            "active_workers": list(self.active_workers.keys()),
            "worker_details": self.active_workers,
            "total_activities": len(self.annotation_history),
            "recent_activities": self.annotation_history[-10:]
        }