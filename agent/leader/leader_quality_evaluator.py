#!/usr/bin/env python3

from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import uuid
import json
import asyncio

from coordination.draft import SharedDraft, DraftAnnotation
from utils.api import async_generate_completion
from prompt import LeaderEvaluationPrompts
from config import get_config

class QualityDecision(Enum):
    APPROVED = "approved"                    
    NEEDS_MINOR_REVISION = "minor_revision"  
    NEEDS_MAJOR_REVISION = "major_revision"  
    REJECTED = "rejected"                    
    REQUIRES_DISCUSSION = "discussion"     

class EvaluationCriteria(Enum):
    ACCURACY = "accuracy"              
    COMPLETENESS = "completeness"      
    CLARITY = "clarity"                  
    COHERENCE = "coherence"              
    RELEVANCE = "relevance"               
    ORIGINALITY = "originality"         

@dataclass
class QualityAssessment:
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    overall_score: float = 0.0             
    decision: QualityDecision = QualityDecision.NEEDS_MINOR_REVISION
    criteria_scores: Dict[str, float] = field(default_factory=dict)
    strengths: List[str] = field(default_factory=list)
    weaknesses: List[str] = field(default_factory=list)
    specific_feedback: List[str] = field(default_factory=list)
    improvement_suggestions: List[str] = field(default_factory=list)
    confidence: float = 0.8                 
    evaluation_reasoning: str = ""          
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())

@dataclass
class QualityThresholds:
    approval_threshold: float = 0.8          
    minor_revision_threshold: float = 0.6    
    major_revision_threshold: float = 0.4    
    rejection_threshold: float = 0.2         

class LeaderQualityEvaluator:
    
    def __init__(self, agent_id: str = "leader"):
        self.agent_id = agent_id
        self.evaluation_history: List[QualityAssessment] = []
        
        self.model_id = get_config("leader_model", "gemini")
        self.max_tokens = get_config("leader_max_tokens", 2048)
        self.temperature = get_config("leader_temperature", 0.2)
        
        self.task_thresholds = {
            'gsm8k': QualityThresholds(0.85, 0.7, 0.55, 0.35),    
            'math': QualityThresholds(0.65, 0.5, 0.35, 0.25),      
            'mbpp': QualityThresholds(0.7, 0.55, 0.4, 0.25),      
            'humaneval': QualityThresholds(0.7, 0.55, 0.4, 0.25),  

            'hotpotqa': QualityThresholds(0.8, 0.65, 0.48, 0.28), 
            'strategyqa': QualityThresholds(0.82, 0.68, 0.5, 0.3), 
            'gpqa': QualityThresholds(0.65, 0.5, 0.35, 0.2),     
            'mmlu': QualityThresholds(0.8, 0.65, 0.48, 0.28),     
            'standard': QualityThresholds(0.75, 0.6, 0.4, 0.2)
        }
    
    async def evaluate_draft_quality(self, shared_draft: SharedDraft,
                                   merged_content: Optional[str] = None) -> QualityAssessment:
        
        content_to_evaluate = merged_content or shared_draft.current_content
        
        try:
            llm_assessment = await self._llm_comprehensive_evaluation(
                shared_draft, content_to_evaluate
            )
            
            criteria_scores = await self._evaluate_quality_criteria(
                shared_draft, content_to_evaluate
            )
            
            overall_score = self._calculate_overall_score(criteria_scores, llm_assessment)
            
            decision = self._make_quality_decision(
                overall_score, shared_draft.question_type, llm_assessment
            )
            
            improvement_suggestions = await self._generate_improvement_suggestions(
                shared_draft, content_to_evaluate, criteria_scores
            )
            
            assessment = QualityAssessment(
                overall_score=overall_score,
                decision=decision,
                criteria_scores=criteria_scores,
                strengths=llm_assessment.get("strengths", []),
                weaknesses=llm_assessment.get("weaknesses", []),
                specific_feedback=llm_assessment.get("specific_feedback", []),
                improvement_suggestions=improvement_suggestions,
                confidence=llm_assessment.get("confidence", 0.8),
                evaluation_reasoning=llm_assessment.get("reasoning", "")
            )
            
            self.evaluation_history.append(assessment)
            
            return assessment
            
        except Exception as e:
            print(f"Error evaluating draft quality: {e}")
            return QualityAssessment(
                overall_score=0.5,
                decision=QualityDecision.NEEDS_MINOR_REVISION,
                evaluation_reasoning=f"Evaluation failed: {str(e)}"
            )
    
    async def evaluate_collaboration_process(self, shared_draft: SharedDraft) -> Dict[str, Any]:
        
        try:
            collaboration_metrics = {
                "participation_rate": len(shared_draft.active_agents) / max(len(shared_draft.participating_agents), 1),
                "annotation_quality": self._assess_annotation_quality(shared_draft),
                "consensus_effectiveness": shared_draft.consensus_level,
                "iteration_efficiency": self._assess_iteration_efficiency(shared_draft),
                "transparency_score": self._assess_transparency(shared_draft)
            }
            
            process_analysis = await self._llm_analyze_collaboration_process(shared_draft)
            
            collaboration_score = sum(collaboration_metrics.values()) / len(collaboration_metrics)
            
            return {
                "collaboration_score": collaboration_score,
                "metrics": collaboration_metrics,
                "process_analysis": process_analysis,
                "recommendations": await self._generate_process_improvements(shared_draft)
            }
            
        except Exception as e:
            print(f"Error evaluating collaboration process: {e}")
            return {"error": str(e)}
    
    async def make_final_decision(self, shared_draft: SharedDraft, 
                                merged_content: str) -> Dict[str, Any]:
        
        try:
            content_assessment = await self.evaluate_draft_quality(shared_draft, merged_content)
            
            process_assessment = await self.evaluate_collaboration_process(shared_draft)
            
            final_decision = await self._llm_make_final_decision(
                shared_draft, merged_content, content_assessment, process_assessment
            )
            
            return {
                "decision": final_decision["decision"],
                "final_score": final_decision["final_score"],
                "content_assessment": content_assessment.__dict__,
                "process_assessment": process_assessment,
                "reasoning": final_decision["reasoning"],
                "next_steps": final_decision.get("next_steps", []),
                "approval_granted": final_decision["decision"] == "approved"
            }
            
        except Exception as e:
            print(f"Error making final decision: {e}")
            return {
                "decision": "error",
                "error": str(e),
                "approval_granted": False
            }
    
    # Private methods
    
    async def _llm_comprehensive_evaluation(self, shared_draft: SharedDraft, 
                                          content: str) -> Dict[str, Any]:
        
        prompt = LeaderEvaluationPrompts.get_comprehensive_evaluation_prompt(
            shared_draft.question_content,
            shared_draft.question_type,
            content,
            shared_draft.get_draft_summary()
        )
        
        system_prompt = LeaderEvaluationPrompts.get_leader_evaluation_system_prompt()
        
        try:
            response = await async_generate_completion(
                agent_id=self.model_id,
                prompt=prompt,
                system_prompt=system_prompt,
                max_tokens=self.max_tokens,
                temperature=self.temperature
            )
            
            assessment = self._parse_llm_evaluation(response)
            return assessment
            
        except Exception as e:
            print(f"LLM comprehensive evaluation failed: {e}")
            return {
                "strengths": ["Content provided"],
                "weaknesses": ["Evaluation failed"],
                "confidence": 0.5,
                "reasoning": f"Evaluation error: {str(e)}"
            }
    
    async def _evaluate_quality_criteria(self, shared_draft: SharedDraft, 
                                       content: str) -> Dict[str, float]:
        
        criteria_scores = {}
        
        for criteria in EvaluationCriteria:
            try:
                score = await self._llm_evaluate_criteria(
                    shared_draft, content, criteria
                )
                criteria_scores[criteria.value] = score
            except Exception as e:
                print(f"Failed to evaluate {criteria.value}: {e}")
                criteria_scores[criteria.value] = 0.5
        
        return criteria_scores
    
    async def _llm_evaluate_criteria(self, shared_draft: SharedDraft, 
                                   content: str, criteria: EvaluationCriteria) -> float:
        
        prompt = LeaderEvaluationPrompts.get_criteria_evaluation_prompt(
            criteria, shared_draft.question_content, 
            shared_draft.question_type, content
        )
        
        system_prompt = LeaderEvaluationPrompts.get_criteria_evaluation_system_prompt()
        
        try:
            response = await async_generate_completion(
                agent_id=self.model_id,
                prompt=prompt,
                system_prompt=system_prompt,
                max_tokens=512,
                temperature=0.1
            )
            
            score = self._extract_score_from_response(response)
            return max(0.0, min(1.0, score))
            
        except Exception as e:
            print(f"Criteria evaluation failed for {criteria.value}: {e}")
            return 0.5
    
    def _calculate_overall_score(self, criteria_scores: Dict[str, float], 
                               llm_assessment: Dict[str, Any]) -> float:

        criteria_weights = {
            "accuracy": 0.25,
            "completeness": 0.2,
            "clarity": 0.15,
            "coherence": 0.15,
            "relevance": 0.15,
            "originality": 0.1
        }
        
        weighted_criteria_score = 0.0
        total_weight = 0.0
        
        for criteria, score in criteria_scores.items():
            weight = criteria_weights.get(criteria, 0.1)
            weighted_criteria_score += score * weight
            total_weight += weight
        
        if total_weight > 0:
            weighted_criteria_score /= total_weight
        
        llm_score = llm_assessment.get("overall_score", 0.5)
        
        overall_score = weighted_criteria_score * 0.7 + llm_score * 0.3
        
        return max(0.0, min(1.0, overall_score))
    
    def _make_quality_decision(self, overall_score: float, question_type: str,
                             llm_assessment: Dict[str, Any]) -> QualityDecision:
        
        thresholds = self.task_thresholds.get(question_type, self.task_thresholds['standard'])
        
        if overall_score >= thresholds.approval_threshold:
            base_decision = QualityDecision.APPROVED
        elif overall_score >= thresholds.minor_revision_threshold:
            base_decision = QualityDecision.NEEDS_MINOR_REVISION
        elif overall_score >= thresholds.major_revision_threshold:
            base_decision = QualityDecision.NEEDS_MAJOR_REVISION
        else:
            base_decision = QualityDecision.REJECTED
        
        llm_suggestion = llm_assessment.get("suggested_decision", "")
        if llm_suggestion:
            try:
                llm_decision = QualityDecision(llm_suggestion)
                decision_severity = {
                    QualityDecision.APPROVED: 4,
                    QualityDecision.NEEDS_MINOR_REVISION: 3,
                    QualityDecision.NEEDS_MAJOR_REVISION: 2,
                    QualityDecision.REJECTED: 1,
                    QualityDecision.REQUIRES_DISCUSSION: 0
                }
                
                if decision_severity[llm_decision] < decision_severity[base_decision]:
                    return llm_decision
            except ValueError:
                pass
        
        return base_decision
    
    async def _generate_improvement_suggestions(self, shared_draft: SharedDraft,
                                              content: str, criteria_scores: Dict[str, float]) -> List[str]:
        
        weak_criteria = [
            criteria for criteria, score in criteria_scores.items() 
            if score < 0.6
        ]
        
        if not weak_criteria:
            return ["Continue with current approach"]
        
        try:
            prompt = LeaderEvaluationPrompts.get_improvement_suggestions_prompt(
                shared_draft.question_content,
                shared_draft.question_type,
                content,
                weak_criteria
            )
            
            system_prompt = LeaderEvaluationPrompts.get_improvement_system_prompt()
            
            response = await async_generate_completion(
                agent_id=self.model_id,
                prompt=prompt,
                system_prompt=system_prompt,
                max_tokens=1024,
                temperature=0.3
            )
            
            suggestions = self._parse_improvement_suggestions(response)
            return suggestions
            
        except Exception as e:
            print(f"Failed to generate improvement suggestions: {e}")
            return [f"Review and improve {', '.join(weak_criteria)}"]
    
    async def _llm_analyze_collaboration_process(self, shared_draft: SharedDraft) -> Dict[str, Any]:
        
        prompt = LeaderEvaluationPrompts.get_collaboration_analysis_prompt(shared_draft)
        system_prompt = LeaderEvaluationPrompts.get_collaboration_analysis_system_prompt()
        
        try:
            response = await async_generate_completion(
                agent_id=self.model_id,
                prompt=prompt,
                system_prompt=system_prompt,
                max_tokens=1024,
                temperature=0.2
            )
            
            analysis = self._parse_collaboration_analysis(response)
            return analysis
            
        except Exception as e:
            print(f"Collaboration analysis failed: {e}")
            return {"analysis": "Analysis failed", "score": 0.5}
    
    async def _llm_make_final_decision(self, shared_draft: SharedDraft, content: str,
                                     content_assessment: QualityAssessment,
                                     process_assessment: Dict[str, Any]) -> Dict[str, Any]:
        
        prompt = LeaderEvaluationPrompts.get_final_decision_prompt(
            shared_draft, content, content_assessment, process_assessment
        )
        
        system_prompt = LeaderEvaluationPrompts.get_final_decision_system_prompt()
        
        try:
            response = await async_generate_completion(
                agent_id=self.model_id,
                prompt=prompt,
                system_prompt=system_prompt,
                max_tokens=1024,
                temperature=0.1
            )
            
            decision = self._parse_final_decision(response)
            return decision
            
        except Exception as e:
            print(f"Final decision making failed: {e}")
            return {
                "decision": "needs_minor_revision",
                "final_score": content_assessment.overall_score,
                "reasoning": f"Decision error: {str(e)}"
            }
    
    # Utility methods
    
    def _assess_annotation_quality(self, shared_draft: SharedDraft) -> float:
        if not shared_draft.annotations:
            return 1.0
        
        total_consensus = sum(ann.consensus_score for ann in shared_draft.annotations)
        avg_consensus = total_consensus / len(shared_draft.annotations)
        
        unique_agents = len(set(ann.agent_id for ann in shared_draft.annotations))
        diversity_score = min(1.0, unique_agents / 3)  
        
        return (avg_consensus * 0.7 + diversity_score * 0.3)
    
    def _assess_iteration_efficiency(self, shared_draft: SharedDraft) -> float:
        if shared_draft.total_rounds == 0:
            return 1.0
        
        optimal_rounds = 4
        efficiency = 1.0 - abs(shared_draft.total_rounds - optimal_rounds) / optimal_rounds
        return max(0.0, min(1.0, efficiency))
    
    def _assess_transparency(self, shared_draft: SharedDraft) -> float:
        transparency_info = shared_draft.get_transparency_info()
        
        activity_score = min(1.0, len(transparency_info["activity_timeline"]) / 5)
        history_score = min(1.0, transparency_info["change_history_count"] / 10)
        
        return (activity_score + history_score) / 2
    
    def _parse_llm_evaluation(self, response: str) -> Dict[str, Any]:
        try:
            if "```json" in response:
                json_str = response.split("```json")[1].split("```")[0].strip()
                return json.loads(json_str)
            else:
                return {
                    "strengths": ["Response provided"],
                    "weaknesses": ["Needs review"],
                    "confidence": 0.7,
                    "reasoning": response.strip()
                }
        except Exception as e:
            return {
                "strengths": [],
                "weaknesses": ["Parsing failed"],
                "confidence": 0.5,
                "reasoning": f"Parse error: {str(e)}"
            }
    
    def _extract_score_from_response(self, response: str) -> float:
        import re
        
        score_patterns = [
            r'score[:\s]*([0-9]*\.?[0-9]+)',
            r'rating[:\s]*([0-9]*\.?[0-9]+)',
            r'([0-9]*\.?[0-9]+)/10',
            r'([0-9]*\.?[0-9]+)\s*out\s*of\s*10'
        ]
        
        for pattern in score_patterns:
            matches = re.findall(pattern, response.lower())
            if matches:
                try:
                    score = float(matches[0])
                    if score > 1.0:
                        score = score / 10.0
                    return score
                except ValueError:
                    continue
        
        return 0.5
    
    def _parse_improvement_suggestions(self, response: str) -> List[str]:
        try:
            if "```json" in response:
                json_str = response.split("```json")[1].split("```")[0].strip()
                data = json.loads(json_str)
                return data.get("suggestions", [])
            else:
                lines = [line.strip() for line in response.split('\n') if line.strip()]
                suggestions = []
                for line in lines:
                    if line.startswith(('-', '•', '*', '1.', '2.', '3.')):
                        suggestions.append(line.lstrip('-•*123456789. '))
                    elif len(line) > 10: 
                        suggestions.append(line)
                return suggestions[:5]  
        except Exception as e:
            return [f"Review response parsing failed: {str(e)}"]
    
    def _parse_collaboration_analysis(self, response: str) -> Dict[str, Any]:
        try:
            if "```json" in response:
                json_str = response.split("```json")[1].split("```")[0].strip()
                return json.loads(json_str)
            else:
                return {
                    "analysis": response.strip(),
                    "score": 0.7,
                    "highlights": ["Collaboration completed"]
                }
        except Exception as e:
            return {"analysis": f"Analysis failed: {str(e)}", "score": 0.5}
    
    def _parse_final_decision(self, response: str) -> Dict[str, Any]:
        try:
            if "```json" in response:
                json_str = response.split("```json")[1].split("```")[0].strip()
                return json.loads(json_str)
            else:
                return {
                    "decision": "approved",
                    "final_score": 0.8,
                    "reasoning": response.strip()
                }
        except Exception as e:
            return {
                "decision": "error",
                "final_score": 0.0,
                "reasoning": f"Decision parsing failed: {str(e)}"
            }
    
    async def _generate_process_improvements(self, shared_draft: SharedDraft) -> List[str]:

        recommendations = []
        
        if len(shared_draft.active_agents) < 2:
            recommendations.append("Increase agent participation for better collaboration")
        
        if shared_draft.consensus_level < 0.7:
            recommendations.append("Focus on building stronger consensus")
        
        if len(shared_draft.annotations) < 3:
            recommendations.append("Encourage more detailed annotations and feedback")
        
        return recommendations or ["Continue current collaboration approach"]
    
    def get_evaluation_stats(self) -> Dict[str, Any]:
        if not self.evaluation_history:
            return {"total_evaluations": 0}
        
        total_evaluations = len(self.evaluation_history)
        avg_score = sum(e.overall_score for e in self.evaluation_history) / total_evaluations
        
        decision_counts = {}
        for assessment in self.evaluation_history:
            decision = assessment.decision.value
            decision_counts[decision] = decision_counts.get(decision, 0) + 1
        
        return {
            "total_evaluations": total_evaluations,
            "average_score": avg_score,
            "decision_distribution": decision_counts,
            "recent_evaluations": [
                {
                    "score": e.overall_score,
                    "decision": e.decision.value,
                    "timestamp": e.timestamp
                }
                for e in self.evaluation_history[-5:]
            ]
        }