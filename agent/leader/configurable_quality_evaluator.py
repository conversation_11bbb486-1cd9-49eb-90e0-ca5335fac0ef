#!/usr/bin/env python3

from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import json
import yaml
from pathlib import Path

from agent.leader.leader_quality_evaluator import (
    LeaderQualityEvaluator, QualityAssessment, QualityDecision, 
    EvaluationCriteria, QualityThresholds
)
from coordination.draft import SharedDraft
from utils.api import async_generate_completion
from config import get_config

@dataclass
class EvaluationDimension:
    name: str
    weight: float = 0.1
    description: str = ""
    prompt_template: str = ""
    scoring_rubric: Dict[str, str] = field(default_factory=dict)
    enabled: bool = True

@dataclass
class TaskEvaluationConfig:
    task_type: str
    dimensions: Dict[str, EvaluationDimension] = field(default_factory=dict)
    thresholds: QualityThresholds = field(default_factory=lambda: QualityThresholds())
    custom_prompts: Dict[str, str] = field(default_factory=dict)
    evaluation_focus: List[str] = field(default_factory=list)
    special_requirements: Dict[str, Any] = field(default_factory=dict)

class ConfigurableQualityEvaluator(LeaderQualityEvaluator):
    
    def __init__(self, agent_id: str = "leader", config_path: Optional[str] = None):
        super().__init__(agent_id)
        
        self.config_path = config_path or "config/evaluation_config.yaml"
        self.task_configs: Dict[str, TaskEvaluationConfig] = {}
        self.default_config = self._create_default_config()
        
        self._load_evaluation_configs()
        
        self.dimension_performance: Dict[str, List[float]] = {}
        self.task_performance: Dict[str, List[float]] = {}
    
    def _create_default_config(self) -> TaskEvaluationConfig:
        """创建默认评估配置"""
        
        default_dimensions = {
            "accuracy": EvaluationDimension(
                name="accuracy",
                weight=0.25,
                description="Correctness and factual accuracy of the solution",
                prompt_template="Evaluate the accuracy of this solution on a scale of 0-1. Consider factual correctness, logical validity, and absence of errors.",
                scoring_rubric={
                    "0.9-1.0": "Completely accurate with no errors",
                    "0.7-0.8": "Mostly accurate with minor errors",
                    "0.5-0.6": "Partially accurate with some errors",
                    "0.3-0.4": "Limited accuracy with significant errors",
                    "0.0-0.2": "Largely inaccurate or incorrect"
                }
            ),
            "completeness": EvaluationDimension(
                name="completeness",
                weight=0.20,
                description="How thoroughly the solution addresses all aspects of the problem",
                prompt_template="Evaluate the completeness of this solution on a scale of 0-1. Consider whether all parts of the question are addressed.",
                scoring_rubric={
                    "0.9-1.0": "Fully addresses all aspects",
                    "0.7-0.8": "Addresses most aspects with minor gaps",
                    "0.5-0.6": "Addresses some aspects but missing key parts",
                    "0.3-0.4": "Addresses few aspects with major gaps",
                    "0.0-0.2": "Incomplete or fails to address the question"
                }
            ),
            "clarity": EvaluationDimension(
                name="clarity",
                weight=0.15,
                description="How clear and understandable the solution is",
                prompt_template="Evaluate the clarity of this solution on a scale of 0-1. Consider readability, organization, and explanation quality.",
                scoring_rubric={
                    "0.9-1.0": "Very clear and well-explained",
                    "0.7-0.8": "Generally clear with minor unclear parts",
                    "0.5-0.6": "Somewhat clear but could be improved",
                    "0.3-0.4": "Unclear in several areas",
                    "0.0-0.2": "Very unclear or confusing"
                }
            ),
            "coherence": EvaluationDimension(
                name="coherence",
                weight=0.15,
                description="Logical flow and consistency of the solution",
                prompt_template="Evaluate the coherence of this solution on a scale of 0-1. Consider logical flow, consistency, and structure.",
                scoring_rubric={
                    "0.9-1.0": "Highly coherent and well-structured",
                    "0.7-0.8": "Generally coherent with minor issues",
                    "0.5-0.6": "Somewhat coherent but could be better organized",
                    "0.3-0.4": "Limited coherence with structural issues",
                    "0.0-0.2": "Incoherent or poorly structured"
                }
            ),
            "relevance": EvaluationDimension(
                name="relevance",
                weight=0.15,
                description="How relevant the solution is to the specific question",
                prompt_template="Evaluate the relevance of this solution on a scale of 0-1. Consider how well it addresses the specific question asked.",
                scoring_rubric={
                    "0.9-1.0": "Highly relevant and on-topic",
                    "0.7-0.8": "Generally relevant with minor off-topic elements",
                    "0.5-0.6": "Somewhat relevant but includes irrelevant parts",
                    "0.3-0.4": "Limited relevance to the question",
                    "0.0-0.2": "Largely irrelevant or off-topic"
                }
            ),
            "efficiency": EvaluationDimension(
                name="efficiency",
                weight=0.10,
                description="Efficiency and elegance of the solution approach",
                prompt_template="Evaluate the efficiency of this solution on a scale of 0-1. Consider whether it uses an optimal approach.",
                scoring_rubric={
                    "0.9-1.0": "Highly efficient and elegant approach",
                    "0.7-0.8": "Generally efficient with minor inefficiencies",
                    "0.5-0.6": "Somewhat efficient but could be improved",
                    "0.3-0.4": "Limited efficiency with significant issues",
                    "0.0-0.2": "Inefficient or overly complex approach"
                }
            )
        }
        
        return TaskEvaluationConfig(
            task_type="default",
            dimensions=default_dimensions,
            thresholds=QualityThresholds(0.8, 0.6, 0.4, 0.2),
            evaluation_focus=["accuracy", "completeness", "clarity"]
        )
    
    def _load_evaluation_configs(self):
        
        config_file = Path(self.config_path)
        
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
                
                for task_type, task_config in config_data.get('tasks', {}).items():
                    self.task_configs[task_type] = self._parse_task_config(task_type, task_config)
                
                print(f"✅ Loaded evaluation configs for {len(self.task_configs)} task types")
                
            except Exception as e:
                print(f"⚠️ Failed to load evaluation config: {e}")
                self._create_default_task_configs()
        else:
            print(f"⚠️ Config file not found: {config_file}, using defaults")
            self._create_default_task_configs()
    
    def _parse_task_config(self, task_type: str, config_data: Dict[str, Any]) -> TaskEvaluationConfig:
        
        dimensions = {}
        for dim_name, dim_config in config_data.get('dimensions', {}).items():
            dimensions[dim_name] = EvaluationDimension(
                name=dim_name,
                weight=dim_config.get('weight', 0.1),
                description=dim_config.get('description', ''),
                prompt_template=dim_config.get('prompt_template', ''),
                scoring_rubric=dim_config.get('scoring_rubric', {}),
                enabled=dim_config.get('enabled', True)
            )
        
        if not dimensions:
            dimensions = self.default_config.dimensions.copy()
        
        thresholds_config = config_data.get('thresholds', {})
        thresholds = QualityThresholds(
            approval_threshold=thresholds_config.get('approval', 0.8),
            minor_revision_threshold=thresholds_config.get('minor_revision', 0.6),
            major_revision_threshold=thresholds_config.get('major_revision', 0.4),
            rejection_threshold=thresholds_config.get('rejection', 0.2)
        )
        
        return TaskEvaluationConfig(
            task_type=task_type,
            dimensions=dimensions,
            thresholds=thresholds,
            custom_prompts=config_data.get('custom_prompts', {}),
            evaluation_focus=config_data.get('evaluation_focus', []),
            special_requirements=config_data.get('special_requirements', {})
        )
    
    def _create_default_task_configs(self):

        math_config = TaskEvaluationConfig(
            task_type="math",
            dimensions=self.default_config.dimensions.copy(),
            thresholds=QualityThresholds(0.9, 0.75, 0.5, 0.3),
            evaluation_focus=["accuracy", "completeness", "clarity"],
            special_requirements={"requires_step_by_step": True, "check_calculations": True}
        )
        math_config.dimensions["accuracy"].weight = 0.35
        math_config.dimensions["completeness"].weight = 0.25
        
        code_config = TaskEvaluationConfig(
            task_type="code",
            dimensions=self.default_config.dimensions.copy(),
            thresholds=QualityThresholds(0.85, 0.7, 0.5, 0.3),
            evaluation_focus=["accuracy", "efficiency", "clarity"],
            special_requirements={"check_syntax": True, "test_functionality": True}
        )
        code_config.dimensions["efficiency"].weight = 0.25 
        code_config.dimensions["accuracy"].weight = 0.30
        
        reading_config = TaskEvaluationConfig(
            task_type="reading",
            dimensions=self.default_config.dimensions.copy(),
            thresholds=QualityThresholds(0.8, 0.65, 0.45, 0.25),
            evaluation_focus=["relevance", "completeness", "accuracy"],
            special_requirements={"check_text_comprehension": True}
        )
        reading_config.dimensions["relevance"].weight = 0.30  
        
        self.task_configs.update({
            "gsm8k": math_config,
            "math": math_config,
            "mbpp": code_config,
            "humaneval": code_config,
            "hotpotqa": reading_config,
            "strategyqa": reading_config,
            "gpqa": self.default_config,
            "mmlu": self.default_config
        })
    
    def get_task_config(self, task_type: str) -> TaskEvaluationConfig:
        return self.task_configs.get(task_type, self.default_config)
    
    async def evaluate_draft_quality(self, shared_draft: SharedDraft,
                                   merged_content: Optional[str] = None) -> QualityAssessment:
        
        task_config = self.get_task_config(shared_draft.question_type)
        content_to_evaluate = merged_content or shared_draft.current_content
        
        try:
            criteria_scores = await self._evaluate_configured_dimensions(
                shared_draft, content_to_evaluate, task_config
            )
            
            llm_assessment = await self._llm_comprehensive_evaluation(
                shared_draft, content_to_evaluate
            )
            
            overall_score = self._calculate_weighted_score(criteria_scores, task_config)
            
            decision = self._make_configured_decision(overall_score, task_config, llm_assessment)
            
            improvement_suggestions = await self._generate_configured_improvements(
                shared_draft, content_to_evaluate, criteria_scores, task_config
            )
            
            assessment = QualityAssessment(
                overall_score=overall_score,
                decision=decision,
                criteria_scores=criteria_scores,
                strengths=llm_assessment.get("strengths", []),
                weaknesses=llm_assessment.get("weaknesses", []),
                specific_feedback=llm_assessment.get("specific_feedback", []),
                improvement_suggestions=improvement_suggestions,
                confidence=llm_assessment.get("confidence", 0.8),
                evaluation_reasoning=self._generate_evaluation_reasoning(criteria_scores, task_config)
            )
            
            self._record_evaluation_performance(shared_draft.question_type, assessment)
            
            self.evaluation_history.append(assessment)
            
            return assessment
            
        except Exception as e:
            print(f"Error in configurable evaluation: {e}")
            return await super().evaluate_draft_quality(shared_draft, merged_content)

    async def _evaluate_configured_dimensions(self, shared_draft: SharedDraft,
                                            content: str, task_config: TaskEvaluationConfig) -> Dict[str, float]:

        dimension_scores = {}

        for dim_name, dimension in task_config.dimensions.items():
            if not dimension.enabled:
                continue

            try:
                score = await self._evaluate_dimension(
                    shared_draft, content, dimension
                )
                dimension_scores[dim_name] = score

                if dim_name not in self.dimension_performance:
                    self.dimension_performance[dim_name] = []
                self.dimension_performance[dim_name].append(score)

            except Exception as e:
                print(f"Failed to evaluate dimension {dim_name}: {e}")
                dimension_scores[dim_name] = 0.5

        return dimension_scores

    async def _evaluate_dimension(self, shared_draft: SharedDraft,
                                content: str, dimension: EvaluationDimension) -> float:

        if dimension.prompt_template:
            prompt = f"""Question: {shared_draft.question_content}
Task Type: {shared_draft.question_type}
Solution to evaluate: {content}

{dimension.prompt_template}

Scoring rubric:
{self._format_scoring_rubric(dimension.scoring_rubric)}

Provide a score between 0.0 and 1.0:"""
        else:
            prompt = f"""Evaluate the {dimension.name} of this solution on a scale of 0.0 to 1.0.

Question: {shared_draft.question_content}
Solution: {content}

Consider: {dimension.description}

Score (0.0-1.0):"""

        try:
            response = await async_generate_completion(
                agent_id=self.model_id,
                prompt=prompt,
                system_prompt=f"You are evaluating the {dimension.name} of a solution. Provide only a numerical score between 0.0 and 1.0.",
                max_tokens=50,
                temperature=0.1
            )

            score = self._extract_score_from_response(response)
            return max(0.0, min(1.0, score))

        except Exception as e:
            print(f"Dimension evaluation failed for {dimension.name}: {e}")
            return 0.5

    def _format_scoring_rubric(self, rubric: Dict[str, str]) -> str:
        if not rubric:
            return "Use your best judgment to assign a score."

        formatted = []
        for score_range, description in rubric.items():
            formatted.append(f"- {score_range}: {description}")

        return "\n".join(formatted)

    def _calculate_weighted_score(self, dimension_scores: Dict[str, float],
                                task_config: TaskEvaluationConfig) -> float:

        total_weighted_score = 0.0
        total_weight = 0.0

        for dim_name, score in dimension_scores.items():
            dimension = task_config.dimensions.get(dim_name)
            if dimension and dimension.enabled:
                weight = dimension.weight
                total_weighted_score += score * weight
                total_weight += weight

        if total_weight > 0:
            return total_weighted_score / total_weight
        else:
            return sum(dimension_scores.values()) / len(dimension_scores) if dimension_scores else 0.5

    def _make_configured_decision(self, overall_score: float, task_config: TaskEvaluationConfig,
                                llm_assessment: Dict[str, Any]) -> QualityDecision:

        thresholds = task_config.thresholds

        if overall_score >= thresholds.approval_threshold:
            base_decision = QualityDecision.APPROVED
        elif overall_score >= thresholds.minor_revision_threshold:
            base_decision = QualityDecision.NEEDS_MINOR_REVISION
        elif overall_score >= thresholds.major_revision_threshold:
            base_decision = QualityDecision.NEEDS_MAJOR_REVISION
        else:
            base_decision = QualityDecision.REJECTED

        if task_config.special_requirements:
            base_decision = self._apply_special_requirements(
                base_decision, overall_score, task_config, llm_assessment
            )

        return base_decision

    def _apply_special_requirements(self, base_decision: QualityDecision,
                                  overall_score: float, task_config: TaskEvaluationConfig,
                                  llm_assessment: Dict[str, Any]) -> QualityDecision:

        requirements = task_config.special_requirements

        if requirements.get("requires_step_by_step") and overall_score > 0.6:
            if "step" not in llm_assessment.get("content_analysis", "").lower():
                if base_decision == QualityDecision.APPROVED:
                    return QualityDecision.NEEDS_MINOR_REVISION

        if requirements.get("check_syntax") and overall_score > 0.5:
            if "error" in llm_assessment.get("content_analysis", "").lower():
                return QualityDecision.NEEDS_MAJOR_REVISION

        return base_decision

    async def _generate_configured_improvements(self, shared_draft: SharedDraft,
                                              content: str, dimension_scores: Dict[str, float],
                                              task_config: TaskEvaluationConfig) -> List[str]:

        improvements = []

        sorted_dimensions = sorted(dimension_scores.items(), key=lambda x: x[1])

        for dim_name, score in sorted_dimensions[:3]:  
            if score < 0.7: 
                dimension = task_config.dimensions.get(dim_name)
                if dimension:
                    improvement = await self._generate_dimension_improvement(
                        shared_draft, content, dimension, score
                    )
                    if improvement:
                        improvements.append(improvement)

        
        task_specific_improvements = self._get_task_specific_improvements(
            task_config, dimension_scores
        )
        improvements.extend(task_specific_improvements)

        return improvements

    async def _generate_dimension_improvement(self, shared_draft: SharedDraft,
                                            content: str, dimension: EvaluationDimension,
                                            score: float) -> str:

        prompt = f"""The solution scored {score:.2f} on {dimension.name} ({dimension.description}).

Question: {shared_draft.question_content}
Current solution: {content}

Provide a specific, actionable improvement suggestion to enhance the {dimension.name}:"""

        try:
            response = await async_generate_completion(
                agent_id=self.model_id,
                prompt=prompt,
                system_prompt=f"Provide specific, actionable advice to improve the {dimension.name} of the solution.",
                max_tokens=200,
                temperature=0.3
            )

            return response.strip() if response else f"Improve {dimension.name}: {dimension.description}"

        except Exception as e:
            return f"Improve {dimension.name}: {dimension.description}"

    def _get_task_specific_improvements(self, task_config: TaskEvaluationConfig,
                                      dimension_scores: Dict[str, float]) -> List[str]:

        improvements = []
        requirements = task_config.special_requirements

        if requirements.get("requires_step_by_step") and dimension_scores.get("clarity", 1.0) < 0.7:
            improvements.append("Provide a clear step-by-step solution with explanations for each step.")

        if requirements.get("check_calculations") and dimension_scores.get("accuracy", 1.0) < 0.8:
            improvements.append("Double-check all calculations and mathematical operations.")

        if requirements.get("check_syntax") and dimension_scores.get("accuracy", 1.0) < 0.7:
            improvements.append("Review code syntax and ensure it follows proper programming conventions.")

        if requirements.get("test_functionality") and dimension_scores.get("completeness", 1.0) < 0.7:
            improvements.append("Test the solution with various inputs to ensure it handles all cases.")

        return improvements

    def _generate_evaluation_reasoning(self, dimension_scores: Dict[str, float],
                                     task_config: TaskEvaluationConfig) -> str:

        reasoning_parts = []

        avg_score = sum(dimension_scores.values()) / len(dimension_scores) if dimension_scores else 0.5
        reasoning_parts.append(f"Overall performance: {avg_score:.2f}")

        sorted_dims = sorted(dimension_scores.items(), key=lambda x: x[1], reverse=True)

        if sorted_dims:
            best_dim, best_score = sorted_dims[0]
            worst_dim, worst_score = sorted_dims[-1]

            reasoning_parts.append(f"Strongest dimension: {best_dim} ({best_score:.2f})")
            reasoning_parts.append(f"Weakest dimension: {worst_dim} ({worst_score:.2f})")

        focus_areas = task_config.evaluation_focus
        if focus_areas:
            focus_scores = [dimension_scores.get(area, 0.5) for area in focus_areas]
            focus_avg = sum(focus_scores) / len(focus_scores)
            reasoning_parts.append(f"Focus areas performance: {focus_avg:.2f}")

        return ". ".join(reasoning_parts) + "."

    def _record_evaluation_performance(self, task_type: str, assessment: QualityAssessment):

        if task_type not in self.task_performance:
            self.task_performance[task_type] = []

        self.task_performance[task_type].append(assessment.overall_score)

    def update_task_config(self, task_type: str, config_updates: Dict[str, Any]):

        if task_type not in self.task_configs:
            self.task_configs[task_type] = self.default_config

        config = self.task_configs[task_type]

        if 'dimension_weights' in config_updates:
            for dim_name, weight in config_updates['dimension_weights'].items():
                if dim_name in config.dimensions:
                    config.dimensions[dim_name].weight = weight

        if 'thresholds' in config_updates:
            thresholds = config_updates['thresholds']
            config.thresholds.approval_threshold = thresholds.get('approval', config.thresholds.approval_threshold)
            config.thresholds.minor_revision_threshold = thresholds.get('minor_revision', config.thresholds.minor_revision_threshold)
            config.thresholds.major_revision_threshold = thresholds.get('major_revision', config.thresholds.major_revision_threshold)
            config.thresholds.rejection_threshold = thresholds.get('rejection', config.thresholds.rejection_threshold)

        if 'evaluation_focus' in config_updates:
            config.evaluation_focus = config_updates['evaluation_focus']

        if 'special_requirements' in config_updates:
            config.special_requirements.update(config_updates['special_requirements'])

    def save_config(self, output_path: Optional[str] = None):

        output_file = Path(output_path or self.config_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)

        config_data = {
            'tasks': {}
        }

        for task_type, config in self.task_configs.items():
            task_data = {
                'dimensions': {},
                'thresholds': {
                    'approval': config.thresholds.approval_threshold,
                    'minor_revision': config.thresholds.minor_revision_threshold,
                    'major_revision': config.thresholds.major_revision_threshold,
                    'rejection': config.thresholds.rejection_threshold
                },
                'evaluation_focus': config.evaluation_focus,
                'special_requirements': config.special_requirements,
                'custom_prompts': config.custom_prompts
            }

            for dim_name, dimension in config.dimensions.items():
                task_data['dimensions'][dim_name] = {
                    'weight': dimension.weight,
                    'description': dimension.description,
                    'prompt_template': dimension.prompt_template,
                    'scoring_rubric': dimension.scoring_rubric,
                    'enabled': dimension.enabled
                }

            config_data['tasks'][task_type] = task_data

        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
            print(f"✅ Configuration saved to {output_file}")
        except Exception as e:
            print(f"❌ Failed to save configuration: {e}")

    def get_evaluation_statistics(self) -> Dict[str, Any]:

        stats = super().get_evaluation_stats()

        dimension_stats = {}
        for dim_name, scores in self.dimension_performance.items():
            if scores:
                dimension_stats[dim_name] = {
                    'average_score': sum(scores) / len(scores),
                    'min_score': min(scores),
                    'max_score': max(scores),
                    'total_evaluations': len(scores),
                    'recent_trend': scores[-5:] if len(scores) >= 5 else scores
                }

        task_stats = {}
        for task_type, scores in self.task_performance.items():
            if scores:
                task_stats[task_type] = {
                    'average_score': sum(scores) / len(scores),
                    'min_score': min(scores),
                    'max_score': max(scores),
                    'total_evaluations': len(scores),
                    'recent_performance': scores[-5:] if len(scores) >= 5 else scores
                }

        config_stats = {
            'total_task_configs': len(self.task_configs),
            'configured_tasks': list(self.task_configs.keys()),
            'total_dimensions': sum(len(config.dimensions) for config in self.task_configs.values()),
            'average_dimensions_per_task': sum(len(config.dimensions) for config in self.task_configs.values()) / len(self.task_configs) if self.task_configs else 0
        }

        stats.update({
            'dimension_performance': dimension_stats,
            'task_performance': task_stats,
            'configuration_stats': config_stats,
            'evaluation_system': 'configurable'
        })

        return stats

    def get_task_config_summary(self, task_type: str) -> Dict[str, Any]:

        config = self.get_task_config(task_type)

        return {
            'task_type': task_type,
            'total_dimensions': len(config.dimensions),
            'enabled_dimensions': sum(1 for dim in config.dimensions.values() if dim.enabled),
            'dimension_weights': {name: dim.weight for name, dim in config.dimensions.items() if dim.enabled},
            'thresholds': {
                'approval': config.thresholds.approval_threshold,
                'minor_revision': config.thresholds.minor_revision_threshold,
                'major_revision': config.thresholds.major_revision_threshold,
                'rejection': config.thresholds.rejection_threshold
            },
            'evaluation_focus': config.evaluation_focus,
            'special_requirements': config.special_requirements,
            'has_custom_prompts': len(config.custom_prompts) > 0
        }

    def optimize_weights_from_performance(self, task_type: str, target_score: float = 0.8):

        if task_type not in self.task_performance or len(self.task_performance[task_type]) < 5:
            print(f"⚠️ Insufficient performance data for {task_type}")
            return

        config = self.get_task_config(task_type)
        current_performance = self.task_performance[task_type]
        avg_performance = sum(current_performance) / len(current_performance)

        if avg_performance >= target_score:
            print(f"✅ {task_type} already meeting target performance ({avg_performance:.2f} >= {target_score})")
            return

        dimension_avg_scores = {}
        for dim_name in config.dimensions.keys():
            if dim_name in self.dimension_performance:
                scores = self.dimension_performance[dim_name]
                dimension_avg_scores[dim_name] = sum(scores) / len(scores) if scores else 0.5

        if dimension_avg_scores:
            worst_dim = min(dimension_avg_scores.items(), key=lambda x: x[1])
            best_dim = max(dimension_avg_scores.items(), key=lambda x: x[1])

            weight_transfer = 0.05
            if worst_dim[0] in config.dimensions and best_dim[0] in config.dimensions:
                config.dimensions[worst_dim[0]].weight += weight_transfer
                config.dimensions[best_dim[0]].weight = max(0.05, config.dimensions[best_dim[0]].weight - weight_transfer)

                print(f"🔧 Optimized weights for {task_type}:")
                print(f"   Increased {worst_dim[0]} weight by {weight_transfer}")
                print(f"   Decreased {best_dim[0]} weight by {weight_transfer}")

    def create_evaluation_report(self, task_type: Optional[str] = None) -> str:

        report_lines = ["# Quality Evaluation Report", ""]

        if task_type:
            config_summary = self.get_task_config_summary(task_type)
            report_lines.extend([
                f"## Task: {task_type}",
                f"- Dimensions: {config_summary['enabled_dimensions']}/{config_summary['total_dimensions']} enabled",
                f"- Approval threshold: {config_summary['thresholds']['approval']}",
                f"- Focus areas: {', '.join(config_summary['evaluation_focus'])}",
                ""
            ])

            if task_type in self.task_performance:
                performance = self.task_performance[task_type]
                avg_score = sum(performance) / len(performance)
                report_lines.extend([
                    f"### Performance",
                    f"- Average score: {avg_score:.3f}",
                    f"- Total evaluations: {len(performance)}",
                    f"- Score range: {min(performance):.3f} - {max(performance):.3f}",
                    ""
                ])
        else:
            stats = self.get_evaluation_statistics()
            report_lines.extend([
                f"## Overall Statistics",
                f"- Total evaluations: {stats['total_evaluations']}",
                f"- Average score: {stats['average_score']:.3f}",
                f"- Configured tasks: {len(stats['configuration_stats']['configured_tasks'])}",
                ""
            ])

            if stats['task_performance']:
                report_lines.extend(["### Task Performance Comparison", ""])
                for task, perf in stats['task_performance'].items():
                    report_lines.append(f"- {task}: {perf['average_score']:.3f} (n={perf['total_evaluations']})")
                report_lines.append("")

        return "\n".join(report_lines)
