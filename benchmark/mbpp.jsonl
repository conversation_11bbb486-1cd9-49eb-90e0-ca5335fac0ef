{"source_file": "<PERSON>'s Copy of Benchmark Questions Verification V2.ipynb", "task_id": 116, "prompt": "Write a function to convert a given tuple of positive integers into a single integer.\n\ndef tuple_to_int(nums):", "code": "def tuple_to_int(nums):\n    result = int(''.join(map(str,nums)))\n    return result", "test_imports": [], "test_list": ["assert tuple_to_int((1,2,3))==123", "assert tuple_to_int((4,5,6))==456", "assert tuple_to_int((5,6,7))==567"], "entry_point": "tuple_to_int", "test": "def check():\n    assert tuple_to_int((1,2,3))==123\n    assert tuple_to_int((4,5,6))==456\n    assert tuple_to_int((5,6,7))==567\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 257, "prompt": "Write a function that takes in two numbers and returns a tuple with the second number and then the first number.\n\ndef swap_numbers(a,b):", "code": "def swap_numbers(a,b):\n temp = a\n a = b\n b = temp\n return (a,b)", "test_imports": [], "test_list": ["assert swap_numbers(10,20)==(20,10)", "assert swap_numbers(15,17)==(17,15)", "assert swap_numbers(100,200)==(200,100)"], "entry_point": "swap_numbers", "test": "def check():\n    assert swap_numbers(10,20)==(20,10)\n    assert swap_numbers(15,17)==(17,15)\n    assert swap_numbers(100,200)==(200,100)\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 435, "prompt": "Write a python function to find the last digit of a given number.\n\ndef last_Digit(n) :", "code": "def last_Digit(n) :\n    return (n % 10) ", "test_imports": [], "test_list": ["assert last_Digit(123) == 3", "assert last_Digit(25) == 5", "assert last_Digit(30) == 0"], "entry_point": "last_Digit", "test": "def check():\n    assert last_Digit(123) == 3\n    assert last_Digit(25) == 5\n    assert last_Digit(30) == 0\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 74, "prompt": "Write a function to check whether it follows the sequence given in the patterns array.\n\ndef is_samepatterns(colors, patterns):    ", "code": "def is_samepatterns(colors, patterns):    \n    if len(colors) != len(patterns):\n        return False    \n    sdict = {}\n    pset = set()\n    sset = set()    \n    for i in range(len(patterns)):\n        pset.add(patterns[i])\n        sset.add(colors[i])\n        if patterns[i] not in sdict.keys():\n            sdict[patterns[i]] = []\n\n        keys = sdict[patterns[i]]\n        keys.append(colors[i])\n        sdict[patterns[i]] = keys\n\n    if len(pset) != len(sset):\n        return False   \n\n    for values in sdict.values():\n\n        for i in range(len(values) - 1):\n            if values[i] != values[i+1]:\n                return False\n\n    return True", "test_imports": [], "test_list": ["assert is_samepatterns([\"red\",\"green\",\"green\"], [\"a\", \"b\", \"b\"])==True", "assert is_samepatterns([\"red\",\"green\",\"greenn\"], [\"a\",\"b\",\"b\"])==False", "assert is_samepatterns([\"red\",\"green\",\"greenn\"], [\"a\",\"b\"])==False"], "entry_point": "is_samepatterns", "test": "def check():\n    assert is_samepatterns([\"red\",\"green\",\"green\"], [\"a\", \"b\", \"b\"])==True\n    assert is_samepatterns([\"red\",\"green\",\"greenn\"], [\"a\",\"b\",\"b\"])==False\n    assert is_samepatterns([\"red\",\"green\",\"greenn\"], [\"a\",\"b\"])==False\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 138, "prompt": "Write a python function to check whether the given number can be represented as sum of non-zero powers of 2 or not.\n\ndef is_Sum_Of_Powers_Of_Two(n): ", "code": "def is_Sum_Of_Powers_Of_Two(n): \n    if (n % 2 == 1): \n        return False\n    else: \n        return True", "test_imports": [], "test_list": ["assert is_Sum_Of_Powers_Of_Two(10) == True", "assert is_Sum_Of_Powers_Of_Two(7) == False", "assert is_Sum_Of_Powers_Of_Two(14) == True"], "entry_point": "is_Sum_Of_Powers_Of_Two", "test": "def check():\n    assert is_Sum_Of_Powers_Of_Two(10) == True\n    assert is_Sum_Of_Powers_Of_Two(7) == False\n    assert is_Sum_Of_Powers_Of_Two(14) == True\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 734, "prompt": "Write a python function to find sum of products of all possible sublists of a given list. https://www.geeksforgeeks.org/sum-of-products-of-all-possible-subarrays/\n\ndef sum_Of_Subarray_Prod(arr):", "code": "def sum_Of_Subarray_Prod(arr):\n    ans = 0\n    res = 0\n    i = len(arr) - 1\n    while (i >= 0):\n        incr = arr[i]*(1 + res)\n        ans += incr\n        res = incr\n        i -= 1\n    return (ans)", "test_imports": [], "test_list": ["assert sum_Of_Subarray_Prod([1,2,3]) == 20", "assert sum_Of_Subarray_Prod([1,2]) == 5", "assert sum_Of_Subarray_Prod([1,2,3,4]) == 84"], "entry_point": "sum_Of_Subarray_Prod", "test": "def check():\n    assert sum_Of_Subarray_Prod([1,2,3]) == 20\n    assert sum_Of_Subarray_Prod([1,2]) == 5\n    assert sum_Of_Subarray_Prod([1,2,3,4]) == 84\n"}
{"source_file": "Ellen's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 299, "prompt": "Write a function to calculate the maximum aggregate from the list of tuples.\nfrom collections import defaultdict\ndef max_aggregate(stdata):", "code": "from collections import defaultdict\ndef max_aggregate(stdata):\n    temp = defaultdict(int)\n    for name, marks in stdata:\n        temp[name] += marks\n    return max(temp.items(), key=lambda x: x[1])", "test_imports": [], "test_list": ["assert max_aggregate([('Juan Whelan',90),('Sabah Colley',88),('Peter Nichols',7),('Juan Whelan',122),('Sabah Colley',84)])==('Juan Whelan', 212)", "assert max_aggregate([('Juan Whelan',50),('Sabah Colley',48),('Peter Nichols',37),('Juan Whelan',22),('Sabah Colley',14)])==('Juan Whelan', 72)", "assert max_aggregate([('Juan Whelan',10),('Sabah Colley',20),('Peter Nichols',30),('Juan Whelan',40),('Sabah Colley',50)])==('Sabah Colley', 70)"], "entry_point": "max_aggregate", "test": "def check():\n    assert max_aggregate([('Juan Whelan',90),('Sabah Colley',88),('Peter Nichols',7),('Juan Whelan',122),('Sabah Colley',84)])==('Juan Whelan', 212)\n    assert max_aggregate([('Juan Whelan',50),('Sabah Colley',48),('Peter Nichols',37),('Juan Whelan',22),('Sabah Colley',14)])==('Juan Whelan', 72)\n    assert max_aggregate([('Juan Whelan',10),('Sabah Colley',20),('Peter Nichols',30),('Juan Whelan',40),('Sabah Colley',50)])==('Sabah Colley', 70)\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 430, "prompt": "Write a function to find the directrix of a parabola.\n\ndef parabola_directrix(a, b, c): ", "code": "def parabola_directrix(a, b, c): \n  directrix=((int)(c - ((b * b) + 1) * 4 * a ))\n  return directrix", "test_imports": [], "test_list": ["assert parabola_directrix(5,3,2)==-198", "assert parabola_directrix(9,8,4)==-2336", "assert parabola_directrix(2,4,6)==-130"], "entry_point": "parabola_directrix", "test": "def check():\n    assert parabola_directrix(5,3,2)==-198\n    assert parabola_directrix(9,8,4)==-2336\n    assert parabola_directrix(2,4,6)==-130\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 796, "prompt": "Write function to find the sum of all items in the given dictionary.\n\ndef return_sum(dict):", "code": "def return_sum(dict):\n  sum = 0\n  for i in dict.values():\n    sum = sum + i\n  return sum", "test_imports": [], "test_list": ["assert return_sum({'a': 100, 'b':200, 'c':300}) == 600", "assert return_sum({'a': 25, 'b':18, 'c':45}) == 88", "assert return_sum({'a': 36, 'b':39, 'c':49}) == 124"], "entry_point": "return_sum", "test": "def check():\n    assert return_sum({'a': 100, 'b':200, 'c':300}) == 600\n    assert return_sum({'a': 25, 'b':18, 'c':45}) == 88\n    assert return_sum({'a': 36, 'b':39, 'c':49}) == 124\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 592, "prompt": "Write a python function to find the sum of the product of consecutive binomial co-efficients.\n\ndef sum_Of_product(n): ", "code": "def binomial_Coeff(n,k): \n    C = [0] * (k + 1); \n    C[0] = 1; # nC0 is 1 \n    for i in range(1,n + 1):  \n        for j in range(min(i, k),0,-1): \n            C[j] = C[j] + C[j - 1]; \n    return C[k]; \ndef sum_Of_product(n): \n    return binomial_Coeff(2 * n,n - 1); ", "test_imports": [], "test_list": ["assert sum_Of_product(3) == 15", "assert sum_Of_product(4) == 56", "assert sum_Of_product(1) == 1"], "entry_point": "sum_Of_product", "test": "def check():\n    assert sum_Of_product(3) == 15\n    assert sum_Of_product(4) == 56\n    assert sum_Of_product(1) == 1\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 635, "prompt": "Write a function to sort the given list.\nimport heapq as hq\ndef heap_sort(iterable):", "code": "import heapq as hq\ndef heap_sort(iterable):\n    h = []\n    for value in iterable:\n        hq.heappush(h, value)\n    return [hq.heappop(h) for i in range(len(h))]", "test_imports": [], "test_list": ["assert heap_sort([1, 3, 5, 7, 9, 2, 4, 6, 8, 0])==[0, 1, 2, 3, 4, 5, 6, 7, 8, 9]", "assert heap_sort([25, 35, 22, 85, 14, 65, 75, 25, 58])==[14, 22, 25, 25, 35, 58, 65, 75, 85]", "assert heap_sort( [7, 1, 9, 5])==[1,5,7,9]"], "entry_point": "heap_sort", "test": "def check():\n    assert heap_sort([1, 3, 5, 7, 9, 2, 4, 6, 8, 0])==[0, 1, 2, 3, 4, 5, 6, 7, 8, 9]\n    assert heap_sort([25, 35, 22, 85, 14, 65, 75, 25, 58])==[14, 22, 25, 25, 35, 58, 65, 75, 85]\n    assert heap_sort( [7, 1, 9, 5])==[1,5,7,9]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 619, "prompt": "Write a function to move all the numbers to the end of the given string.\n\ndef move_num(test_str):", "code": "def move_num(test_str):\n  res = ''\n  dig = ''\n  for ele in test_str:\n    if ele.isdigit():\n      dig += ele\n    else:\n      res += ele\n  res += dig\n  return (res) ", "test_imports": [], "test_list": ["assert move_num('I1love143you55three3000thousand') == 'Iloveyouthreethousand1143553000'", "assert move_num('Avengers124Assemble') == 'AvengersAssemble124'", "assert move_num('Its11our12path13to14see15things16do17things') == 'Itsourpathtoseethingsdothings11121314151617'"], "entry_point": "move_num", "test": "def check():\n    assert move_num('I1love143you55three3000thousand') == 'Iloveyouthreethousand1143553000'\n    assert move_num('Avengers124Assemble') == 'AvengersAssemble124'\n    assert move_num('Its11our12path13to14see15things16do17things') == 'Itsourpathtoseethingsdothings11121314151617'\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 8, "prompt": "Write a function to find squares of individual elements in a list.\n\ndef square_nums(nums):", "code": "def square_nums(nums):\n square_nums = list(map(lambda x: x ** 2, nums))\n return square_nums", "test_imports": [], "test_list": ["assert square_nums([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])==[1, 4, 9, 16, 25, 36, 49, 64, 81, 100]", "assert square_nums([10,20,30])==([100,400,900])", "assert square_nums([12,15])==([144,225])"], "entry_point": "square_nums", "test": "def check():\n    assert square_nums([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])==[1, 4, 9, 16, 25, 36, 49, 64, 81, 100]\n    assert square_nums([10,20,30])==([100,400,900])\n    assert square_nums([12,15])==([144,225])\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 91, "prompt": "Write a function to check if a string is present as a substring in a given list of string values.\n\ndef find_substring(str1, sub_str):", "code": "def find_substring(str1, sub_str):\n   if any(sub_str in s for s in str1):\n       return True\n   return False", "test_imports": [], "test_list": ["assert find_substring([\"red\", \"black\", \"white\", \"green\", \"orange\"],\"ack\")==True", "assert find_substring([\"red\", \"black\", \"white\", \"green\", \"orange\"],\"abc\")==False", "assert find_substring([\"red\", \"black\", \"white\", \"green\", \"orange\"],\"ange\")==True"], "entry_point": "find_substring", "test": "def check():\n    assert find_substring([\"red\", \"black\", \"white\", \"green\", \"orange\"],\"ack\")==True\n    assert find_substring([\"red\", \"black\", \"white\", \"green\", \"orange\"],\"abc\")==False\n    assert find_substring([\"red\", \"black\", \"white\", \"green\", \"orange\"],\"ange\")==True\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 593, "prompt": "Write a function to remove leading zeroes from an ip address.\nimport re\ndef removezero_ip(ip):", "code": "import re\ndef removezero_ip(ip):\n string = re.sub('\\.[0]*', '.', ip)\n return string\n", "test_imports": [], "test_list": ["assert removezero_ip(\"**************\")==('************')", "assert removezero_ip(\"12.01.024\")==('12.1.24')", "assert removezero_ip(\"216.08.094.0196\")==('************')"], "entry_point": "removezero_ip", "test": "def check():\n    assert removezero_ip(\"**************\")==('************')\n    assert removezero_ip(\"12.01.024\")==('12.1.24')\n    assert removezero_ip(\"216.08.094.0196\")==('************')\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 631, "prompt": "Write a function to replace whitespaces with an underscore and vice versa in a given string.\n\ndef replace_spaces(text):", "code": "def replace_spaces(text):\n  return \"\".join(\" \" if c == \"_\" else (\"_\" if c == \" \" else c) for c in text)", "test_imports": [], "test_list": ["assert replace_spaces('Jumanji The Jungle') == 'Jumanji_The_Jungle'", "assert replace_spaces('The_Avengers') == 'The Avengers'", "assert replace_spaces('Fast and Furious') == 'Fast_and_Furious'"], "entry_point": "replace_spaces", "test": "def check():\n    assert replace_spaces('Jumanji The Jungle') == 'Jumanji_The_Jungle'\n    assert replace_spaces('The_Avengers') == 'The Avengers'\n    assert replace_spaces('Fast and Furious') == 'Fast_and_Furious'\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 746, "prompt": "Write a function to find area of a sector. The function takes the radius and angle as inputs. Function should return None if the angle is larger than 360 degrees.\nimport math\ndef sector_area(r,a):", "code": "import math\ndef sector_area(r,a):\n    if a > 360:\n        return None\n    return (math.pi*r**2) * (a/360)", "test_imports": [], "test_list": ["assert sector_area(4,45)==6.283185307179586", "assert sector_area(9,45)==31.808625617596654", "assert sector_area(9,361)==None"], "entry_point": "sector_area", "test": "def check():\n    assert sector_area(4,45)==6.283185307179586\n    assert sector_area(9,45)==31.808625617596654\n    assert sector_area(9,361)==None\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 558, "prompt": "Write a python function to find the sum of the per-digit difference between two integers.\n\ndef digit_distance_nums(n1, n2):", "code": "def digit_distance_nums(n1, n2):\n         return sum(map(int,str(abs(n1-n2))))", "test_imports": [], "test_list": ["assert digit_distance_nums(1,2) == 1", "assert digit_distance_nums(23,56) == 6", "assert digit_distance_nums(123,256) == 7"], "entry_point": "digit_distance_nums", "test": "def check():\n    assert digit_distance_nums(1,2) == 1\n    assert digit_distance_nums(23,56) == 6\n    assert digit_distance_nums(123,256) == 7\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 57, "prompt": "Write a python function to find the largest number that can be formed with the given list of digits.\n\ndef find_Max_Num(arr) : ", "code": "def find_Max_Num(arr) : \n    n = len(arr)\n    arr.sort(reverse = True) \n    num = arr[0] \n    for i in range(1,n) : \n        num = num * 10 + arr[i] \n    return num ", "test_imports": [], "test_list": ["assert find_Max_Num([1,2,3]) == 321", "assert find_Max_Num([4,5,6,1]) == 6541", "assert find_Max_Num([1,2,3,9]) == 9321"], "entry_point": "find_Max_Num", "test": "def check():\n    assert find_Max_Num([1,2,3]) == 321\n    assert find_Max_Num([4,5,6,1]) == 6541\n    assert find_Max_Num([1,2,3,9]) == 9321\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 776, "prompt": "Write a function to count those characters which have vowels as their neighbors in the given string.\n\ndef count_vowels(test_str):", "code": "def count_vowels(test_str):\n  res = 0\n  vow_list = ['a', 'e', 'i', 'o', 'u']\n  for idx in range(1, len(test_str) - 1):\n    if test_str[idx] not in vow_list and (test_str[idx - 1] in vow_list or test_str[idx + 1] in vow_list):\n      res += 1\n  if test_str[0] not in vow_list and test_str[1] in vow_list:\n    res += 1\n  if test_str[-1] not in vow_list and test_str[-2] in vow_list:\n    res += 1\n  return (res) ", "test_imports": [], "test_list": ["assert count_vowels('bestinstareels') == 7", "assert count_vowels('partofthejourneyistheend') == 12", "assert count_vowels('amazonprime') == 5"], "entry_point": "count_vowels", "test": "def check():\n    assert count_vowels('bestinstareels') == 7\n    assert count_vowels('partofthejourneyistheend') == 12\n    assert count_vowels('amazonprime') == 5\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 256, "prompt": "Write a python function that takes in a non-negative number and returns the number of prime numbers less than the given non-negative number.\n\ndef count_Primes_nums(n):", "code": "def count_Primes_nums(n):\n    ctr = 0\n    for num in range(n):\n        if num <= 1:\n            continue\n        for i in range(2,num):\n            if (num % i) == 0:\n                break\n        else:\n            ctr += 1\n    return ctr", "test_imports": [], "test_list": ["assert count_Primes_nums(5) == 2", "assert count_Primes_nums(10) == 4", "assert count_Primes_nums(100) == 25"], "entry_point": "count_Primes_nums", "test": "def check():\n    assert count_Primes_nums(5) == 2\n    assert count_Primes_nums(10) == 4\n    assert count_Primes_nums(100) == 25\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 599, "prompt": "Write a function to find sum and average of first n natural numbers.\n\ndef sum_average(number):", "code": "def sum_average(number):\n total = 0\n for value in range(1, number + 1):\n    total = total + value\n average = total / number\n return (total,average)", "test_imports": [], "test_list": ["assert sum_average(10)==(55, 5.5)", "assert sum_average(15)==(120, 8.0)", "assert sum_average(20)==(210, 10.5)"], "entry_point": "sum_average", "test": "def check():\n    assert sum_average(10)==(55, 5.5)\n    assert sum_average(15)==(120, 8.0)\n    assert sum_average(20)==(210, 10.5)\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 6, "prompt": "Write a python function to check whether the two numbers differ at one bit position only or not.\n\ndef differ_At_One_Bit_Pos(a,b): ", "code": "def is_Power_Of_Two (x): \n    return x and (not(x & (x - 1))) \ndef differ_At_One_Bit_Pos(a,b): \n    return is_Power_Of_Two(a ^ b)", "test_imports": [], "test_list": ["assert differ_At_One_Bit_Pos(13,9) == True", "assert differ_At_One_Bit_Pos(15,8) == False", "assert differ_At_One_Bit_Pos(2,4) == False", "assert differ_At_One_Bit_Pos(2, 3) == True", "assert differ_At_One_Bit_Pos(5, 1) == True", "assert differ_At_One_Bit_Pos(1, 5) == True"], "entry_point": "differ_At_One_Bit_Pos", "test": "def check():\n    assert differ_At_One_Bit_Pos(13,9) == True\n    assert differ_At_One_Bit_Pos(15,8) == False\n    assert differ_At_One_Bit_Pos(2,4) == False\n    assert differ_At_One_Bit_Pos(2, 3) == True\n    assert differ_At_One_Bit_Pos(5, 1) == True\n    assert differ_At_One_Bit_Pos(1, 5) == True\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 59, "prompt": "Write a function to find the nth octagonal number.\n\ndef is_octagonal(n): ", "code": "def is_octagonal(n): \n\treturn 3 * n * n - 2 * n ", "test_imports": [], "test_list": ["assert is_octagonal(5) == 65", "assert is_octagonal(10) == 280", "assert is_octagonal(15) == 645"], "entry_point": "is_octagonal", "test": "def check():\n    assert is_octagonal(5) == 65\n    assert is_octagonal(10) == 280\n    assert is_octagonal(15) == 645\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 627, "prompt": "Write a python function to find the smallest missing number from a sorted list of natural numbers.\n\ndef find_First_Missing(array,start=0,end=None):", "code": "def find_First_Missing(array,start=0,end=None):\n    if end is None:\n      end = len(array) - 1   \n    if (start > end): \n        return end + 1\n    if (start != array[start]): \n        return start; \n    mid = int((start + end) / 2) \n    if (array[mid] == mid): \n        return find_First_Missing(array,mid+1,end) \n    return find_First_Missing(array,start,mid) ", "test_imports": [], "test_list": ["assert find_First_Missing([0,1,2,3]) == 4", "assert find_First_Missing([0,1,2,6,9]) == 3", "assert find_First_Missing([2,3,5,8,9]) == 0"], "entry_point": "find_First_Missing", "test": "def check():\n    assert find_First_Missing([0,1,2,3]) == 4\n    assert find_First_Missing([0,1,2,6,9]) == 3\n    assert find_First_Missing([2,3,5,8,9]) == 0\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 106, "prompt": "Write a function to append the given list to the given tuples.\n\ndef add_lists(test_list, test_tup):", "code": "def add_lists(test_list, test_tup):\n  res = tuple(list(test_tup) + test_list)\n  return (res) ", "test_imports": [], "test_list": ["assert add_lists([5, 6, 7], (9, 10)) == (9, 10, 5, 6, 7)", "assert add_lists([6, 7, 8], (10, 11)) == (10, 11, 6, 7, 8)", "assert add_lists([7, 8, 9], (11, 12)) == (11, 12, 7, 8, 9)"], "entry_point": "add_lists", "test": "def check():\n    assert add_lists([5, 6, 7], (9, 10)) == (9, 10, 5, 6, 7)\n    assert add_lists([6, 7, 8], (10, 11)) == (10, 11, 6, 7, 8)\n    assert add_lists([7, 8, 9], (11, 12)) == (11, 12, 7, 8, 9)\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 742, "prompt": "Write a function to caluclate the area of a tetrahedron.\nimport math\ndef area_tetrahedron(side):", "code": "import math\ndef area_tetrahedron(side):\n  area = math.sqrt(3)*(side*side)\n  return area", "test_imports": [], "test_list": ["assert area_tetrahedron(3)==15.588457268119894", "assert area_tetrahedron(20)==692.8203230275509", "assert area_tetrahedron(10)==173.20508075688772"], "entry_point": "area_tetrahedron", "test": "def check():\n    assert area_tetrahedron(3)==15.588457268119894\n    assert area_tetrahedron(20)==692.8203230275509\n    assert area_tetrahedron(10)==173.20508075688772\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 462, "prompt": "Write a function to find all possible combinations of the elements of a given list.\n\ndef combinations_list(list1):", "code": "def combinations_list(list1):\n    if len(list1) == 0:\n        return [[]]\n    result = []\n    for el in combinations_list(list1[1:]):\n        result += [el, el+[list1[0]]]\n    return result", "test_imports": [], "test_list": ["assert combinations_list(['orange', 'red', 'green', 'blue'])==[[], ['orange'], ['red'], ['red', 'orange'], ['green'], ['green', 'orange'], ['green', 'red'], ['green', 'red', 'orange'], ['blue'], ['blue', 'orange'], ['blue', 'red'], ['blue', 'red', 'orange'], ['blue', 'green'], ['blue', 'green', 'orange'], ['blue', 'green', 'red'], ['blue', 'green', 'red', 'orange']]", "assert combinations_list(['red', 'green', 'blue', 'white', 'black', 'orange'])==[[], ['red'], ['green'], ['green', 'red'], ['blue'], ['blue', 'red'], ['blue', 'green'], ['blue', 'green', 'red'], ['white'], ['white', 'red'], ['white', 'green'], ['white', 'green', 'red'], ['white', 'blue'], ['white', 'blue', 'red'], ['white', 'blue', 'green'], ['white', 'blue', 'green', 'red'], ['black'], ['black', 'red'], ['black', 'green'], ['black', 'green', 'red'], ['black', 'blue'], ['black', 'blue', 'red'], ['black', 'blue', 'green'], ['black', 'blue', 'green', 'red'], ['black', 'white'], ['black', 'white', 'red'], ['black', 'white', 'green'], ['black', 'white', 'green', 'red'], ['black', 'white', 'blue'], ['black', 'white', 'blue', 'red'], ['black', 'white', 'blue', 'green'], ['black', 'white', 'blue', 'green', 'red'], ['orange'], ['orange', 'red'], ['orange', 'green'], ['orange', 'green', 'red'], ['orange', 'blue'], ['orange', 'blue', 'red'], ['orange', 'blue', 'green'], ['orange', 'blue', 'green', 'red'], ['orange', 'white'], ['orange', 'white', 'red'], ['orange', 'white', 'green'], ['orange', 'white', 'green', 'red'], ['orange', 'white', 'blue'], ['orange', 'white', 'blue', 'red'], ['orange', 'white', 'blue', 'green'], ['orange', 'white', 'blue', 'green', 'red'], ['orange', 'black'], ['orange', 'black', 'red'], ['orange', 'black', 'green'], ['orange', 'black', 'green', 'red'], ['orange', 'black', 'blue'], ['orange', 'black', 'blue', 'red'], ['orange', 'black', 'blue', 'green'], ['orange', 'black', 'blue', 'green', 'red'], ['orange', 'black', 'white'], ['orange', 'black', 'white', 'red'], ['orange', 'black', 'white', 'green'], ['orange', 'black', 'white', 'green', 'red'], ['orange', 'black', 'white', 'blue'], ['orange', 'black', 'white', 'blue', 'red'], ['orange', 'black', 'white', 'blue', 'green'], ['orange', 'black', 'white', 'blue', 'green', 'red']]", "assert combinations_list(['red', 'green', 'black', 'orange'])==[[], ['red'], ['green'], ['green', 'red'], ['black'], ['black', 'red'], ['black', 'green'], ['black', 'green', 'red'], ['orange'], ['orange', 'red'], ['orange', 'green'], ['orange', 'green', 'red'], ['orange', 'black'], ['orange', 'black', 'red'], ['orange', 'black', 'green'], ['orange', 'black', 'green', 'red']]"], "entry_point": "combinations_list", "test": "def check():\n    assert combinations_list(['orange', 'red', 'green', 'blue'])==[[], ['orange'], ['red'], ['red', 'orange'], ['green'], ['green', 'orange'], ['green', 'red'], ['green', 'red', 'orange'], ['blue'], ['blue', 'orange'], ['blue', 'red'], ['blue', 'red', 'orange'], ['blue', 'green'], ['blue', 'green', 'orange'], ['blue', 'green', 'red'], ['blue', 'green', 'red', 'orange']]\n    assert combinations_list(['red', 'green', 'blue', 'white', 'black', 'orange'])==[[], ['red'], ['green'], ['green', 'red'], ['blue'], ['blue', 'red'], ['blue', 'green'], ['blue', 'green', 'red'], ['white'], ['white', 'red'], ['white', 'green'], ['white', 'green', 'red'], ['white', 'blue'], ['white', 'blue', 'red'], ['white', 'blue', 'green'], ['white', 'blue', 'green', 'red'], ['black'], ['black', 'red'], ['black', 'green'], ['black', 'green', 'red'], ['black', 'blue'], ['black', 'blue', 'red'], ['black', 'blue', 'green'], ['black', 'blue', 'green', 'red'], ['black', 'white'], ['black', 'white', 'red'], ['black', 'white', 'green'], ['black', 'white', 'green', 'red'], ['black', 'white', 'blue'], ['black', 'white', 'blue', 'red'], ['black', 'white', 'blue', 'green'], ['black', 'white', 'blue', 'green', 'red'], ['orange'], ['orange', 'red'], ['orange', 'green'], ['orange', 'green', 'red'], ['orange', 'blue'], ['orange', 'blue', 'red'], ['orange', 'blue', 'green'], ['orange', 'blue', 'green', 'red'], ['orange', 'white'], ['orange', 'white', 'red'], ['orange', 'white', 'green'], ['orange', 'white', 'green', 'red'], ['orange', 'white', 'blue'], ['orange', 'white', 'blue', 'red'], ['orange', 'white', 'blue', 'green'], ['orange', 'white', 'blue', 'green', 'red'], ['orange', 'black'], ['orange', 'black', 'red'], ['orange', 'black', 'green'], ['orange', 'black', 'green', 'red'], ['orange', 'black', 'blue'], ['orange', 'black', 'blue', 'red'], ['orange', 'black', 'blue', 'green'], ['orange', 'black', 'blue', 'green', 'red'], ['orange', 'black', 'white'], ['orange', 'black', 'white', 'red'], ['orange', 'black', 'white', 'green'], ['orange', 'black', 'white', 'green', 'red'], ['orange', 'black', 'white', 'blue'], ['orange', 'black', 'white', 'blue', 'red'], ['orange', 'black', 'white', 'blue', 'green'], ['orange', 'black', 'white', 'blue', 'green', 'red']]\n    assert combinations_list(['red', 'green', 'black', 'orange'])==[[], ['red'], ['green'], ['green', 'red'], ['black'], ['black', 'red'], ['black', 'green'], ['black', 'green', 'red'], ['orange'], ['orange', 'red'], ['orange', 'green'], ['orange', 'green', 'red'], ['orange', 'black'], ['orange', 'black', 'red'], ['orange', 'black', 'green'], ['orange', 'black', 'green', 'red']]\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 111, "prompt": "Write a function to find the common elements in given nested lists.\n\ndef common_in_nested_lists(nestedlist):", "code": "def common_in_nested_lists(nestedlist):\n    result = list(set.intersection(*map(set, nestedlist)))\n    return result", "test_imports": [], "test_list": ["assert set(common_in_nested_lists([[12, 18, 23, 25, 45], [7, 12, 18, 24, 28], [1, 5, 8, 12, 15, 16, 18]]))==set([18, 12])", "assert set(common_in_nested_lists([[12, 5, 23, 25, 45], [7, 11, 5, 23, 28], [1, 5, 8, 18, 23, 16]]))==set([5,23])", "assert set(common_in_nested_lists([[2, 3,4, 1], [4, 5], [6,4, 8],[4, 5], [6, 8,4]]))==set([4])"], "entry_point": "common_in_nested_lists", "test": "def check():\n    assert set(common_in_nested_lists([[12, 18, 23, 25, 45], [7, 12, 18, 24, 28], [1, 5, 8, 12, 15, 16, 18]]))==set([18, 12])\n    assert set(common_in_nested_lists([[12, 5, 23, 25, 45], [7, 11, 5, 23, 28], [1, 5, 8, 18, 23, 16]]))==set([5,23])\n    assert set(common_in_nested_lists([[2, 3,4, 1], [4, 5], [6,4, 8],[4, 5], [6, 8,4]]))==set([4])\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 230, "prompt": "Write a function that takes in a string and character, replaces blank spaces in the string with the character, and returns the string.\n\ndef replace_blank(str1,char):", "code": "def replace_blank(str1,char):\n str2 = str1.replace(' ', char)\n return str2", "test_imports": [], "test_list": ["assert replace_blank(\"hello people\",'@')==(\"hello@people\")", "assert replace_blank(\"python program language\",'$')==(\"python$program$language\")", "assert replace_blank(\"blank space\",\"-\")==(\"blank-space\")"], "entry_point": "replace_blank", "test": "def check():\n    assert replace_blank(\"hello people\",'@')==(\"hello@people\")\n    assert replace_blank(\"python program language\",'$')==(\"python$program$language\")\n    assert replace_blank(\"blank space\",\"-\")==(\"blank-space\")\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 808, "prompt": "Write a function to check if the given tuples contain the k or not.\n\ndef check_K(test_tup, K):", "code": "def check_K(test_tup, K):\n  res = False\n  for ele in test_tup:\n    if ele == K:\n      res = True\n      break\n  return res ", "test_imports": [], "test_list": ["assert check_K((10, 4, 5, 6, 8), 6) == True", "assert check_K((1, 2, 3, 4, 5, 6), 7) == False", "assert check_K((7, 8, 9, 44, 11, 12), 11) == True"], "entry_point": "check_K", "test": "def check():\n    assert check_K((10, 4, 5, 6, 8), 6) == True\n    assert check_K((1, 2, 3, 4, 5, 6), 7) == False\n    assert check_K((7, 8, 9, 44, 11, 12), 11) == True\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 409, "prompt": "Write a function to find the minimum product from the pairs of tuples within a given list.\n\ndef min_product_tuple(list1):", "code": "def min_product_tuple(list1):\n    result_min = min([abs(x * y) for x, y in list1] )\n    return result_min", "test_imports": [], "test_list": ["assert min_product_tuple([(2, 7), (2, 6), (1, 8), (4, 9)] )==8", "assert min_product_tuple([(10,20), (15,2), (5,10)] )==30", "assert min_product_tuple([(11,44), (10,15), (20,5), (12, 9)] )==100"], "entry_point": "min_product_tuple", "test": "def check():\n    assert min_product_tuple([(2, 7), (2, 6), (1, 8), (4, 9)] )==8\n    assert min_product_tuple([(10,20), (15,2), (5,10)] )==30\n    assert min_product_tuple([(11,44), (10,15), (20,5), (12, 9)] )==100\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 71, "prompt": "Write a function to sort a list of elements.\n\ndef comb_sort(nums):", "code": "def comb_sort(nums):\n    shrink_fact = 1.3\n    gaps = len(nums)\n    swapped = True\n    i = 0\n    while gaps > 1 or swapped:\n        gaps = int(float(gaps) / shrink_fact)\n        swapped = False\n        i = 0\n        while gaps + i < len(nums):\n            if nums[i] > nums[i+gaps]:\n                nums[i], nums[i+gaps] = nums[i+gaps], nums[i]\n                swapped = True\n            i += 1\n    return nums", "test_imports": [], "test_list": ["assert comb_sort([5, 15, 37, 25, 79]) == [5, 15, 25, 37, 79]", "assert comb_sort([41, 32, 15, 19, 22]) == [15, 19, 22, 32, 41]", "assert comb_sort([99, 15, 13, 47]) == [13, 15, 47, 99]"], "entry_point": "comb_sort", "test": "def check():\n    assert comb_sort([5, 15, 37, 25, 79]) == [5, 15, 25, 37, 79]\n    assert comb_sort([41, 32, 15, 19, 22]) == [15, 19, 22, 32, 41]\n    assert comb_sort([99, 15, 13, 47]) == [13, 15, 47, 99]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 617, "prompt": "Write a function to check for the number of jumps required of given length to reach a point of form (d, 0) from origin in a 2d plane.\n\ndef min_Jumps(steps, d): ", "code": "def min_Jumps(steps, d): \n    (a, b) = steps\n    temp = a \n    a = min(a, b) \n    b = max(temp, b) \n    if (d >= b): \n        return (d + b - 1) / b \n    if (d == 0): \n        return 0\n    if (d == a): \n        return 1\n    else:\n        return 2", "test_imports": [], "test_list": ["assert min_Jumps((3,4),11)==3.5", "assert min_Jumps((3,4),0)==0", "assert min_Jumps((11,14),11)==1"], "entry_point": "min_Jumps", "test": "def check():\n    assert min_Jumps((3,4),11)==3.5\n    assert min_Jumps((3,4),0)==0\n    assert min_Jumps((11,14),11)==1\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 413, "prompt": "Write a function to extract the nth element from a given list of tuples.\n\ndef extract_nth_element(list1, n):", "code": "def extract_nth_element(list1, n):\n    result = [x[n] for x in list1]\n    return result", "test_imports": [], "test_list": ["assert extract_nth_element([('Greyson Fulton', 98, 99), ('Brady Kent', 97, 96), ('Wyatt Knott', 91, 94), ('Beau Turnbull', 94, 98)] ,0)==['Greyson Fulton', 'Brady Kent', 'Wyatt Knott', 'Beau Turnbull']", "assert extract_nth_element([('Greyson Fulton', 98, 99), ('Brady Kent', 97, 96), ('Wyatt Knott', 91, 94), ('Beau Turnbull', 94, 98)] ,2)==[99, 96, 94, 98]", "assert extract_nth_element([('Greyson Fulton', 98, 99), ('Brady Kent', 97, 96), ('Wyatt Knott', 91, 94), ('Beau Turnbull', 94, 98)],1)==[98, 97, 91, 94]"], "entry_point": "extract_nth_element", "test": "def check():\n    assert extract_nth_element([('Greyson Fulton', 98, 99), ('Brady Kent', 97, 96), ('Wyatt Knott', 91, 94), ('Beau Turnbull', 94, 98)] ,0)==['Greyson Fulton', 'Brady Kent', 'Wyatt Knott', 'Beau Turnbull']\n    assert extract_nth_element([('Greyson Fulton', 98, 99), ('Brady Kent', 97, 96), ('Wyatt Knott', 91, 94), ('Beau Turnbull', 94, 98)] ,2)==[99, 96, 94, 98]\n    assert extract_nth_element([('Greyson Fulton', 98, 99), ('Brady Kent', 97, 96), ('Wyatt Knott', 91, 94), ('Beau Turnbull', 94, 98)],1)==[98, 97, 91, 94]\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 420, "prompt": "Write a python function to find the cube sum of first n even natural numbers.\n\ndef cube_Sum(n): ", "code": "def cube_Sum(n): \n    sum = 0\n    for i in range(1,n + 1): \n        sum += (2*i)*(2*i)*(2*i) \n    return sum", "test_imports": [], "test_list": ["assert cube_Sum(2) == 72", "assert cube_Sum(3) == 288", "assert cube_Sum(4) == 800"], "entry_point": "cube_Sum", "test": "def check():\n    assert cube_Sum(2) == 72\n    assert cube_Sum(3) == 288\n    assert cube_Sum(4) == 800\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 68, "prompt": "Write a python function to check whether the given array is monotonic or not.\n\ndef is_Monotonic(A): ", "code": "def is_Monotonic(A): \n    return (all(A[i] <= A[i + 1] for i in range(len(A) - 1)) or\n            all(A[i] >= A[i + 1] for i in range(len(A) - 1))) ", "test_imports": [], "test_list": ["assert is_Monotonic([6, 5, 4, 4]) == True", "assert is_Monotonic([1, 2, 2, 3]) == True", "assert is_Monotonic([1, 3, 2]) == False"], "entry_point": "is_Monotonic", "test": "def check():\n    assert is_Monotonic([6, 5, 4, 4]) == True\n    assert is_Monotonic([1, 2, 2, 3]) == True\n    assert is_Monotonic([1, 3, 2]) == False\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 11, "prompt": "Write a python function to remove first and last occurrence of a given character from the string.\n\ndef remove_Occ(s,ch): ", "code": "def remove_Occ(s,ch): \n    for i in range(len(s)): \n        if (s[i] == ch): \n            s = s[0 : i] + s[i + 1:] \n            break\n    for i in range(len(s) - 1,-1,-1):  \n        if (s[i] == ch): \n            s = s[0 : i] + s[i + 1:] \n            break\n    return s ", "test_imports": [], "test_list": ["assert remove_Occ(\"hello\",\"l\") == \"heo\"", "assert remove_Occ(\"abcda\",\"a\") == \"bcd\"", "assert remove_Occ(\"PHP\",\"P\") == \"H\""], "entry_point": "remove_Occ", "test": "def check():\n    assert remove_Occ(\"hello\",\"l\") == \"heo\"\n    assert remove_Occ(\"abcda\",\"a\") == \"bcd\"\n    assert remove_Occ(\"PHP\",\"P\") == \"H\"\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 784, "prompt": "Write a function to find the product of first even and odd number of a given list.\n\ndef mul_even_odd(list1):", "code": "def mul_even_odd(list1):\n    first_even = next((el for el in list1 if el%2==0),-1)\n    first_odd = next((el for el in list1 if el%2!=0),-1)\n    return (first_even*first_odd)", "test_imports": [], "test_list": ["assert mul_even_odd([1,3,5,7,4,1,6,8])==4", "assert mul_even_odd([1,2,3,4,5,6,7,8,9,10])==2", "assert mul_even_odd([1,5,7,9,10])==10"], "entry_point": "mul_even_odd", "test": "def check():\n    assert mul_even_odd([1,3,5,7,4,1,6,8])==4\n    assert mul_even_odd([1,2,3,4,5,6,7,8,9,10])==2\n    assert mul_even_odd([1,5,7,9,10])==10\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 471, "prompt": "Write a python function to find the product of the array multiplication modulo n.\n\ndef find_remainder(arr, n): ", "code": "def find_remainder(arr, n): \n    mul = 1\n    for i in range(len(arr)):  \n        mul = (mul * (arr[i] % n)) % n \n    return mul % n ", "test_imports": [], "test_list": ["assert find_remainder([ 100, 10, 5, 25, 35, 14 ],11) ==9", "assert find_remainder([1,1,1],1) == 0", "assert find_remainder([1,2,1],2) == 0"], "entry_point": "find_remainder", "test": "def check():\n    assert find_remainder([ 100, 10, 5, 25, 35, 14 ],11) ==9\n    assert find_remainder([1,1,1],1) == 0\n    assert find_remainder([1,2,1],2) == 0\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 228, "prompt": "Write a python function to check whether all the bits are unset in the given range or not.\n\ndef all_Bits_Set_In_The_Given_Range(n,l,r):  ", "code": "def all_Bits_Set_In_The_Given_Range(n,l,r):  \n    num = (((1 << r) - 1) ^ ((1 << (l - 1)) - 1)) \n    new_num = n & num\n    if (new_num == 0): \n        return True\n    return False", "test_imports": [], "test_list": ["assert all_Bits_Set_In_The_Given_Range(4,1,2) == True", "assert all_Bits_Set_In_The_Given_Range(17,2,4) == True", "assert all_Bits_Set_In_The_Given_Range(39,4,6) == False"], "entry_point": "all_Bits_Set_In_The_Given_Range", "test": "def check():\n    assert all_Bits_Set_In_The_Given_Range(4,1,2) == True\n    assert all_Bits_Set_In_The_Given_Range(17,2,4) == True\n    assert all_Bits_Set_In_The_Given_Range(39,4,6) == False\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 171, "prompt": "Write a function to find the perimeter of a regular pentagon from the length of its sides.\nimport math\ndef perimeter_pentagon(a):", "code": "import math\ndef perimeter_pentagon(a):\n  perimeter=(5*a)\n  return perimeter", "test_imports": [], "test_list": ["assert perimeter_pentagon(5) == 25", "assert perimeter_pentagon(10) == 50", "assert perimeter_pentagon(15) == 75"], "entry_point": "perimeter_pentagon", "test": "def check():\n    assert perimeter_pentagon(5) == 25\n    assert perimeter_pentagon(10) == 50\n    assert perimeter_pentagon(15) == 75\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 643, "prompt": "Write a function that checks if a strings contains 'z', except at the start and end of the word.\nimport re\ndef text_match_wordz_middle(text):", "code": "import re\ndef text_match_wordz_middle(text):\n        return bool(re.search(r'\\Bz\\B',  text))", "test_imports": [], "test_list": ["assert text_match_wordz_middle(\"pythonzabc.\")==True", "assert text_match_wordz_middle(\"zxyabc.\")==False", "assert text_match_wordz_middle(\"  lang  .\")==False"], "entry_point": "text_match_wordz_middle", "test": "def check():\n    assert text_match_wordz_middle(\"pythonzabc.\")==True\n    assert text_match_wordz_middle(\"zxyabc.\")==False\n    assert text_match_wordz_middle(\"  lang  .\")==False\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 780, "prompt": "Write a function to find the combinations of sums with tuples in the given tuple list. https://www.geeksforgeeks.org/python-combinations-of-sum-with-tuples-in-tuple-list/\nfrom itertools import combinations \ndef find_combinations(test_list):", "code": "from itertools import combinations \ndef find_combinations(test_list):\n  res = [(b1 + a1, b2 + a2) for (a1, a2), (b1, b2) in combinations(test_list, 2)]\n  return (res) ", "test_imports": [], "test_list": ["assert find_combinations([(2, 4), (6, 7), (5, 1), (6, 10)]) == [(8, 11), (7, 5), (8, 14), (11, 8), (12, 17), (11, 11)]", "assert find_combinations([(3, 5), (7, 8), (6, 2), (7, 11)]) == [(10, 13), (9, 7), (10, 16), (13, 10), (14, 19), (13, 13)]", "assert find_combinations([(4, 6), (8, 9), (7, 3), (8, 12)]) == [(12, 15), (11, 9), (12, 18), (15, 12), (16, 21), (15, 15)]"], "entry_point": "find_combinations", "test": "def check():\n    assert find_combinations([(2, 4), (6, 7), (5, 1), (6, 10)]) == [(8, 11), (7, 5), (8, 14), (11, 8), (12, 17), (11, 11)]\n    assert find_combinations([(3, 5), (7, 8), (6, 2), (7, 11)]) == [(10, 13), (9, 7), (10, 16), (13, 10), (14, 19), (13, 13)]\n    assert find_combinations([(4, 6), (8, 9), (7, 3), (8, 12)]) == [(12, 15), (11, 9), (12, 18), (15, 12), (16, 21), (15, 15)]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 240, "prompt": "Write a function that takes in two lists and replaces the last element of the first list with the elements of the second list.\n\ndef replace_list(list1,list2):", "code": "def replace_list(list1,list2):\n list1[-1:] = list2\n replace_list=list1\n return replace_list\n", "test_imports": [], "test_list": ["assert replace_list([1, 3, 5, 7, 9, 10],[2, 4, 6, 8])==[1, 3, 5, 7, 9, 2, 4, 6, 8]", "assert replace_list([1,2,3,4,5],[5,6,7,8])==[1,2,3,4,5,6,7,8]", "assert replace_list([\"red\",\"blue\",\"green\"],[\"yellow\"])==[\"red\",\"blue\",\"yellow\"]"], "entry_point": "replace_list", "test": "def check():\n    assert replace_list([1, 3, 5, 7, 9, 10],[2, 4, 6, 8])==[1, 3, 5, 7, 9, 2, 4, 6, 8]\n    assert replace_list([1,2,3,4,5],[5,6,7,8])==[1,2,3,4,5,6,7,8]\n    assert replace_list([\"red\",\"blue\",\"green\"],[\"yellow\"])==[\"red\",\"blue\",\"yellow\"]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 235, "prompt": "Write a python function to set all even bits of a given number.\n\ndef even_bit_set_number(n): ", "code": "def even_bit_set_number(n): \n    count = 0;res = 0;temp = n \n    while(temp > 0): \n        if (count % 2 == 1): \n            res |= (1 << count)\n        count+=1\n        temp >>= 1\n    return (n | res) ", "test_imports": [], "test_list": ["assert even_bit_set_number(10) == 10", "assert even_bit_set_number(20) == 30", "assert even_bit_set_number(30) == 30"], "entry_point": "even_bit_set_number", "test": "def check():\n    assert even_bit_set_number(10) == 10\n    assert even_bit_set_number(20) == 30\n    assert even_bit_set_number(30) == 30\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 264, "prompt": "Write a function to calculate a dog's age in dog's years.\n\ndef dog_age(h_age):", "code": "def dog_age(h_age):\n if h_age < 0:\n \texit()\n elif h_age <= 2:\n\t d_age = h_age * 10.5\n else:\n\t d_age = 21 + (h_age - 2)*4\n return d_age", "test_imports": [], "test_list": ["assert dog_age(12)==61", "assert dog_age(15)==73", "assert dog_age(24)==109"], "entry_point": "dog_age", "test": "def check():\n    assert dog_age(12)==61\n    assert dog_age(15)==73\n    assert dog_age(24)==109\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 612, "prompt": "Write a python function which takes a list of lists, where each sublist has two elements, and returns a list of two lists where the first list has the first element of each sublist and the second one has the second.\n\ndef merge(lst):  ", "code": "def merge(lst):  \n    return [list(ele) for ele in list(zip(*lst))] ", "test_imports": [], "test_list": ["assert merge([['x', 'y'], ['a', 'b'], ['m', 'n']]) == [['x', 'a', 'm'], ['y', 'b', 'n']]", "assert merge([[1, 2], [3, 4], [5, 6], [7, 8]]) == [[1, 3, 5, 7], [2, 4, 6, 8]]", "assert merge([['x', 'y','z' ], ['a', 'b','c'], ['m', 'n','o']]) == [['x', 'a', 'm'], ['y', 'b', 'n'],['z', 'c','o']]"], "entry_point": "merge", "test": "def check():\n    assert merge([['x', 'y'], ['a', 'b'], ['m', 'n']]) == [['x', 'a', 'm'], ['y', 'b', 'n']]\n    assert merge([[1, 2], [3, 4], [5, 6], [7, 8]]) == [[1, 3, 5, 7], [2, 4, 6, 8]]\n    assert merge([['x', 'y','z' ], ['a', 'b','c'], ['m', 'n','o']]) == [['x', 'a', 'm'], ['y', 'b', 'n'],['z', 'c','o']]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 578, "prompt": "Write a function to interleave 3 lists of the same length into a single flat list.\n\ndef interleave_lists(list1,list2,list3):", "code": "def interleave_lists(list1,list2,list3):\n    result = [el for pair in zip(list1, list2, list3) for el in pair]\n    return result", "test_imports": [], "test_list": ["assert interleave_lists([1,2,3,4,5,6,7],[10,20,30,40,50,60,70],[100,200,300,400,500,600,700])==[1, 10, 100, 2, 20, 200, 3, 30, 300, 4, 40, 400, 5, 50, 500, 6, 60, 600, 7, 70, 700]", "assert interleave_lists([10,20],[15,2],[5,10])==[10,15,5,20,2,10]", "assert interleave_lists([11,44], [10,15], [20,5])==[11,10,20,44,15,5]"], "entry_point": "interleave_lists", "test": "def check():\n    assert interleave_lists([1,2,3,4,5,6,7],[10,20,30,40,50,60,70],[100,200,300,400,500,600,700])==[1, 10, 100, 2, 20, 200, 3, 30, 300, 4, 40, 400, 5, 50, 500, 6, 60, 600, 7, 70, 700]\n    assert interleave_lists([10,20],[15,2],[5,10])==[10,15,5,20,2,10]\n    assert interleave_lists([11,44], [10,15], [20,5])==[11,10,20,44,15,5]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 285, "prompt": "Write a function that checks whether a string contains the 'a' character followed by two or three 'b' characters.\nimport re\ndef text_match_two_three(text):", "code": "import re\ndef text_match_two_three(text):\n        patterns = 'ab{2,3}'\n        if re.search(patterns,  text):\n                return True\n        else:\n                return False", "test_imports": [], "test_list": ["assert text_match_two_three(\"ac\")==(False)", "assert text_match_two_three(\"dc\")==(False)", "assert text_match_two_three(\"abbbba\")==(True)"], "entry_point": "text_match_two_three", "test": "def check():\n    assert text_match_two_three(\"ac\")==(False)\n    assert text_match_two_three(\"dc\")==(False)\n    assert text_match_two_three(\"abbbba\")==(True)\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 758, "prompt": "Write a function to count lists within a list. The function should return a dictionary where every list is converted to a tuple and the value of such tuple is the number of its occurencies in the original list.\n\ndef unique_sublists(list1):", "code": "def unique_sublists(list1):\n    result ={}\n    for l in  list1: \n        result.setdefault(tuple(l), list()).append(1) \n    for a, b in result.items(): \n        result[a] = sum(b)\n    return result", "test_imports": [], "test_list": ["assert unique_sublists([[1, 3], [5, 7], [1, 3], [13, 15, 17], [5, 7], [9, 11]] )=={(1, 3): 2, (5, 7): 2, (13, 15, 17): 1, (9, 11): 1}", "assert unique_sublists([['green', 'orange'], ['black'], ['green', 'orange'], ['white']])=={('green', 'orange'): 2, ('black',): 1, ('white',): 1}", "assert unique_sublists([[10, 20, 30, 40], [60, 70, 50, 50], [90, 100, 200]])=={(10, 20, 30, 40): 1, (60, 70, 50, 50): 1, (90, 100, 200): 1}", "assert unique_sublists([['john']])=={('john',): 1}"], "entry_point": "unique_sublists", "test": "def check():\n    assert unique_sublists([[1, 3], [5, 7], [1, 3], [13, 15, 17], [5, 7], [9, 11]] )=={(1, 3): 2, (5, 7): 2, (13, 15, 17): 1, (9, 11): 1}\n    assert unique_sublists([['green', 'orange'], ['black'], ['green', 'orange'], ['white']])=={('green', 'orange'): 2, ('black',): 1, ('white',): 1}\n    assert unique_sublists([[10, 20, 30, 40], [60, 70, 50, 50], [90, 100, 200]])=={(10, 20, 30, 40): 1, (60, 70, 50, 50): 1, (90, 100, 200): 1}\n    assert unique_sublists([['john']])=={('john',): 1}\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 781, "prompt": "Write a python function to check whether the count of divisors is even. https://www.w3resource.com/python-exercises/basic/python-basic-1-exercise-24.php\nimport math \ndef count_divisors(n) : ", "code": "import math \ndef count_divisors(n) : \n    count = 0\n    for i in range(1, (int)(math.sqrt(n)) + 2) : \n        if (n % i == 0) : \n            if( n // i == i) : \n                count = count + 1\n            else : \n                count = count + 2\n    return count % 2 == 0", "test_imports": [], "test_list": ["assert count_divisors(10)", "assert not count_divisors(100)", "assert count_divisors(125)"], "entry_point": "count_divisors", "test": "def check():\n    assert count_divisors(10)\n    assert not count_divisors(100)\n    assert count_divisors(125)\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 760, "prompt": "Write a python function to check whether a list of numbers contains only one distinct element or not.\n\ndef unique_Element(arr):", "code": "def unique_Element(arr):\n    s = set(arr)\n    return len(s) == 1", "test_imports": [], "test_list": ["assert unique_Element([1,1,1]) == True", "assert unique_Element([1,2,1,2]) == False", "assert unique_Element([1,2,3,4,5]) == False"], "entry_point": "unique_Element", "test": "def check():\n    assert unique_Element([1,1,1]) == True\n    assert unique_Element([1,2,1,2]) == False\n    assert unique_Element([1,2,3,4,5]) == False\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 281, "prompt": "Write a python function to check if the elements of a given list are unique or not.\n\ndef all_unique(test_list):", "code": "def all_unique(test_list):\n    if len(test_list) > len(set(test_list)):\n        return False\n    return True", "test_imports": [], "test_list": ["assert all_unique([1,2,3]) == True", "assert all_unique([1,2,1,2]) == False", "assert all_unique([1,2,3,4,5]) == True"], "entry_point": "all_unique", "test": "def check():\n    assert all_unique([1,2,3]) == True\n    assert all_unique([1,2,1,2]) == False\n    assert all_unique([1,2,3,4,5]) == True\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 388, "prompt": "Write a python function to find the highest power of 2 that is less than or equal to n.\n\ndef highest_Power_of_2(n): ", "code": "def highest_Power_of_2(n): \n    res = 0 \n    for i in range(n, 0, -1): \n        if ((i & (i - 1)) == 0): \n            res = i \n            break \n    return res ", "test_imports": [], "test_list": ["assert highest_Power_of_2(10) == 8", "assert highest_Power_of_2(19) == 16", "assert highest_Power_of_2(32) == 32"], "entry_point": "highest_Power_of_2", "test": "def check():\n    assert highest_Power_of_2(10) == 8\n    assert highest_Power_of_2(19) == 16\n    assert highest_Power_of_2(32) == 32\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 229, "prompt": "Write a function that takes in an array and an integer n, and re-arranges the first n elements of the given array so that all negative elements appear before positive ones, and where the relative order among negative and positive elements is preserved.\n\ndef re_arrange_array(arr, n):", "code": "def re_arrange_array(arr, n):\n  j=0\n  for i in range(0, n):\n    if (arr[i] < 0):\n      temp = arr[i]\n      arr[i] = arr[j]\n      arr[j] = temp\n      j = j + 1\n  return arr", "test_imports": [], "test_list": ["assert re_arrange_array([-1, 2, -3, 4, 5, 6, -7, 8, 9], 9) == [-1, -3, -7, 4, 5, 6, 2, 8, 9]", "assert re_arrange_array([12, -14, -26, 13, 15], 5) == [-14, -26, 12, 13, 15]", "assert re_arrange_array([10, 24, 36, -42, -39, -78, 85], 7) == [-42, -39, -78, 10, 24, 36, 85]"], "entry_point": "re_arrange_array", "test": "def check():\n    assert re_arrange_array([-1, 2, -3, 4, 5, 6, -7, 8, 9], 9) == [-1, -3, -7, 4, 5, 6, 2, 8, 9]\n    assert re_arrange_array([12, -14, -26, 13, 15], 5) == [-14, -26, 12, 13, 15]\n    assert re_arrange_array([10, 24, 36, -42, -39, -78, 85], 7) == [-42, -39, -78, 10, 24, 36, 85]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 787, "prompt": "Write a function that matches a string that has an a followed by three 'b'.\nimport re\ndef text_match_three(text):", "code": "import re\ndef text_match_three(text):\n        patterns = 'ab{3}?'\n        return re.search(patterns,  text)", "test_imports": [], "test_list": ["assert not text_match_three(\"ac\")", "assert not text_match_three(\"dc\")", "assert text_match_three(\"abbbba\")", "assert text_match_three(\"caacabbbba\")"], "entry_point": "text_match_three", "test": "def check():\n    assert not text_match_three(\"ac\")\n    assert not text_match_three(\"dc\")\n    assert text_match_three(\"abbbba\")\n    assert text_match_three(\"caacabbbba\")\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 405, "prompt": "Write a function to check whether an element exists within a tuple.\n\ndef check_tuplex(tuplex,tuple1): ", "code": "def check_tuplex(tuplex,tuple1): \n  if tuple1 in tuplex:\n    return True\n  else:\n     return False", "test_imports": [], "test_list": ["assert check_tuplex((\"w\", 3, \"r\", \"e\", \"s\", \"o\", \"u\", \"r\", \"c\", \"e\"),'r')==True", "assert check_tuplex((\"w\", 3, \"r\", \"e\", \"s\", \"o\", \"u\", \"r\", \"c\", \"e\"),'5')==False", "assert check_tuplex((\"w\", 3, \"r\", \"e\", \"s\", \"o\", \"u\", \"r\", \"c\",\"e\"),3)==True"], "entry_point": "check_tuplex", "test": "def check():\n    assert check_tuplex((\"w\", 3, \"r\", \"e\", \"s\", \"o\", \"u\", \"r\", \"c\", \"e\"),'r')==True\n    assert check_tuplex((\"w\", 3, \"r\", \"e\", \"s\", \"o\", \"u\", \"r\", \"c\", \"e\"),'5')==False\n    assert check_tuplex((\"w\", 3, \"r\", \"e\", \"s\", \"o\", \"u\", \"r\", \"c\",\"e\"),3)==True\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 730, "prompt": "Write a function to remove consecutive duplicates of a given list.\nfrom itertools import groupby\ndef consecutive_duplicates(nums):", "code": "from itertools import groupby\ndef consecutive_duplicates(nums):\n    return [key for key, group in groupby(nums)] ", "test_imports": [], "test_list": ["assert consecutive_duplicates([0, 0, 1, 2, 3, 4, 4, 5, 6, 6, 6, 7, 8, 9, 4, 4 ])==[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 4]", "assert consecutive_duplicates([10, 10, 15, 19, 18, 18, 17, 26, 26, 17, 18, 10])==[10, 15, 19, 18, 17, 26, 17, 18, 10]", "assert consecutive_duplicates(['a', 'a', 'b', 'c', 'd', 'd'])==['a', 'b', 'c', 'd']", "assert consecutive_duplicates(['a', 'a', 'b', 'c', 'd', 'd', 'a', 'a'])==['a', 'b', 'c', 'd', 'a']"], "entry_point": "consecutive_duplicates", "test": "def check():\n    assert consecutive_duplicates([0, 0, 1, 2, 3, 4, 4, 5, 6, 6, 6, 7, 8, 9, 4, 4 ])==[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 4]\n    assert consecutive_duplicates([10, 10, 15, 19, 18, 18, 17, 26, 26, 17, 18, 10])==[10, 15, 19, 18, 17, 26, 17, 18, 10]\n    assert consecutive_duplicates(['a', 'a', 'b', 'c', 'd', 'd'])==['a', 'b', 'c', 'd']\n    assert consecutive_duplicates(['a', 'a', 'b', 'c', 'd', 'd', 'a', 'a'])==['a', 'b', 'c', 'd', 'a']\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 565, "prompt": "Write a python function to split a string into characters.\n\ndef split(word): ", "code": "def split(word): \n    return [char for char in word] ", "test_imports": [], "test_list": ["assert split('python') == ['p','y','t','h','o','n']", "assert split('Name') == ['N','a','m','e']", "assert split('program') == ['p','r','o','g','r','a','m']"], "entry_point": "split", "test": "def check():\n    assert split('python') == ['p','y','t','h','o','n']\n    assert split('Name') == ['N','a','m','e']\n    assert split('program') == ['p','r','o','g','r','a','m']\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 137, "prompt": "Write a function to find the ratio of zeroes to non-zeroes in an array of integers.\nfrom array import array\ndef zero_count(nums):", "code": "from array import array\ndef zero_count(nums):\n    n = len(nums)\n    n1 = 0\n    for x in nums:\n        if x == 0:\n            n1 += 1\n        else:\n          None\n    return n1/(n-n1)", "test_imports": ["import math"], "test_list": ["assert math.isclose(zero_count([0, 1, 2, -1, -5, 6, 0, -3, -2, 3, 4, 6, 8]), 0.181818, rel_tol=0.001)", "assert math.isclose(zero_count([2, 1, 2, -1, -5, 6, 4, -3, -2, 3, 4, 6, 8]), 0.00, rel_tol=0.001)", "assert math.isclose(zero_count([2, 4, -6, -9, 11, -12, 14, -5, 17]), 0.00, rel_tol=0.001)"], "entry_point": "zero_count", "test": "def check():\n    assert math.isclose(zero_count([0, 1, 2, -1, -5, 6, 0, -3, -2, 3, 4, 6, 8]), 0.181818, rel_tol=0.001)\n    assert math.isclose(zero_count([2, 1, 2, -1, -5, 6, 4, -3, -2, 3, 4, 6, 8]), 0.00, rel_tol=0.001)\n    assert math.isclose(zero_count([2, 4, -6, -9, 11, -12, 14, -5, 17]), 0.00, rel_tol=0.001)\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 457, "prompt": "Write a python function to find the sublist having minimum length.\n\ndef Find_Min(lst): ", "code": "def Find_Min(lst): \n    return min(lst, key=len) ", "test_imports": [], "test_list": ["assert Find_Min([[1],[1,2],[1,2,3]]) == [1]", "assert Find_Min([[1,1],[1,1,1],[1,2,7,8]]) == [1,1]", "assert Find_Min([['x'],['x','y'],['x','y','z']]) == ['x']"], "entry_point": "Find_Min", "test": "def check():\n    assert Find_Min([[1],[1,2],[1,2,3]]) == [1]\n    assert Find_Min([[1,1],[1,1,1],[1,2,7,8]]) == [1,1]\n    assert Find_Min([['x'],['x','y'],['x','y','z']]) == ['x']\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 421, "prompt": "Write a function to concatenate each element of tuple by the delimiter.\n\ndef concatenate_tuple(test_tup):", "code": "def concatenate_tuple(test_tup):\n    delim = \"-\"\n    res = ''.join([str(ele) + delim for ele in test_tup])\n    res = res[ : len(res) - len(delim)]\n    return (str(res)) ", "test_imports": [], "test_list": ["assert concatenate_tuple((\"ID\", \"is\", 4, \"UTS\") ) == 'ID-is-4-UTS'", "assert concatenate_tuple((\"QWE\", \"is\", 4, \"RTY\") ) == 'QWE-is-4-RTY'", "assert concatenate_tuple((\"ZEN\", \"is\", 4, \"OP\") ) == 'ZEN-is-4-OP'"], "entry_point": "concatenate_tuple", "test": "def check():\n    assert concatenate_tuple((\"ID\", \"is\", 4, \"UTS\") ) == 'ID-is-4-UTS'\n    assert concatenate_tuple((\"QWE\", \"is\", 4, \"RTY\") ) == 'QWE-is-4-RTY'\n    assert concatenate_tuple((\"ZEN\", \"is\", 4, \"OP\") ) == 'ZEN-is-4-OP'\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 126, "prompt": "Write a python function to find the sum of common divisors of two given numbers.\n\ndef sum(a,b): ", "code": "def sum(a,b): \n    sum = 0\n    for i in range (1,min(a,b)): \n        if (a % i == 0 and b % i == 0): \n            sum += i \n    return sum", "test_imports": [], "test_list": ["assert sum(10,15) == 6", "assert sum(100,150) == 93", "assert sum(4,6) == 3"], "entry_point": "sum", "test": "def check():\n    assert sum(10,15) == 6\n    assert sum(100,150) == 93\n    assert sum(4,6) == 3\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 414, "prompt": "Write a python function to check whether any value in a sequence exists in a sequence or not.\n\ndef overlapping(list1,list2):  ", "code": "def overlapping(list1,list2):  \n    for i in range(len(list1)): \n        for j in range(len(list2)): \n            if(list1[i]==list2[j]): \n                return True\n    return False", "test_imports": [], "test_list": ["assert overlapping([1,2,3,4,5],[6,7,8,9]) == False", "assert overlapping([1,2,3],[4,5,6]) == False", "assert overlapping([1,4,5],[1,4,5]) == True"], "entry_point": "overlapping", "test": "def check():\n    assert overlapping([1,2,3,4,5],[6,7,8,9]) == False\n    assert overlapping([1,2,3],[4,5,6]) == False\n    assert overlapping([1,4,5],[1,4,5]) == True\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 282, "prompt": "Write a function to subtract two lists element-wise.\n\ndef sub_list(nums1,nums2):", "code": "def sub_list(nums1,nums2):\n  result = map(lambda x, y: x - y, nums1, nums2)\n  return list(result)", "test_imports": [], "test_list": ["assert sub_list([1, 2, 3],[4,5,6])==[-3,-3,-3]", "assert sub_list([1,2],[3,4])==[-2,-2]", "assert sub_list([90,120],[50,70])==[40,50]"], "entry_point": "sub_list", "test": "def check():\n    assert sub_list([1, 2, 3],[4,5,6])==[-3,-3,-3]\n    assert sub_list([1,2],[3,4])==[-2,-2]\n    assert sub_list([90,120],[50,70])==[40,50]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 242, "prompt": "Write a function to count the total number of characters in a string.\n\ndef count_charac(str1):", "code": "def count_charac(str1):\n total = 0\n for i in str1:\n    total = total + 1\n return total", "test_imports": [], "test_list": ["assert count_charac(\"python programming\")==18", "assert count_charac(\"language\")==8", "assert count_charac(\"words\")==5"], "entry_point": "count_charac", "test": "def check():\n    assert count_charac(\"python programming\")==18\n    assert count_charac(\"language\")==8\n    assert count_charac(\"words\")==5\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 595, "prompt": "Write a python function to count minimum number of swaps required to convert one binary number represented as a string to another.\n\ndef min_Swaps(str1,str2) : ", "code": "def min_Swaps(str1,str2) : \n    count = 0\n    for i in range(len(str1)) :  \n        if str1[i] != str2[i] : \n            count += 1\n    if count % 2 == 0 : \n        return (count // 2) \n    else : \n        return (\"Not Possible\") ", "test_imports": [], "test_list": ["assert min_Swaps(\"1101\",\"1110\") == 1", "assert min_Swaps(\"111\",\"000\") == \"Not Possible\"", "assert min_Swaps(\"111\",\"110\") == \"Not Possible\""], "entry_point": "min_Swaps", "test": "def check():\n    assert min_Swaps(\"1101\",\"1110\") == 1\n    assert min_Swaps(\"111\",\"000\") == \"Not Possible\"\n    assert min_Swaps(\"111\",\"110\") == \"Not Possible\"\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 779, "prompt": "Write a function to count the number of lists within a list. The function should return a dictionary, where every list is turned to a tuple, and the value of the tuple is the number of its occurrences.\n\ndef unique_sublists(list1):", "code": "def unique_sublists(list1):\n    result ={}\n    for l in list1: \n        result.setdefault(tuple(l), list()).append(1) \n    for a, b in result.items(): \n        result[a] = sum(b)\n    return result", "test_imports": [], "test_list": ["assert unique_sublists([[1, 3], [5, 7], [1, 3], [13, 15, 17], [5, 7], [9, 11]])=={(1, 3): 2, (5, 7): 2, (13, 15, 17): 1, (9, 11): 1}", "assert unique_sublists([['green', 'orange'], ['black'], ['green', 'orange'], ['white']])=={('green', 'orange'): 2, ('black',): 1, ('white',): 1}", "assert unique_sublists([[1, 2], [3, 4], [4, 5], [6, 7]])=={(1, 2): 1, (3, 4): 1, (4, 5): 1, (6, 7): 1}"], "entry_point": "unique_sublists", "test": "def check():\n    assert unique_sublists([[1, 3], [5, 7], [1, 3], [13, 15, 17], [5, 7], [9, 11]])=={(1, 3): 2, (5, 7): 2, (13, 15, 17): 1, (9, 11): 1}\n    assert unique_sublists([['green', 'orange'], ['black'], ['green', 'orange'], ['white']])=={('green', 'orange'): 2, ('black',): 1, ('white',): 1}\n    assert unique_sublists([[1, 2], [3, 4], [4, 5], [6, 7]])=={(1, 2): 1, (3, 4): 1, (4, 5): 1, (6, 7): 1}\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 633, "prompt": "Write a python function to find the sum of xor of all pairs of numbers in the given list.\n\ndef pair_xor_Sum(arr,n) : ", "code": "def pair_xor_Sum(arr,n) : \n    ans = 0 \n    for i in range(0,n) :    \n        for j in range(i + 1,n) :   \n            ans = ans + (arr[i] ^ arr[j])          \n    return ans ", "test_imports": [], "test_list": ["assert pair_xor_Sum([5,9,7,6],4) == 47", "assert pair_xor_Sum([7,3,5],3) == 12", "assert pair_xor_Sum([7,3],2) == 4"], "entry_point": "pair_xor_Sum", "test": "def check():\n    assert pair_xor_Sum([5,9,7,6],4) == 47\n    assert pair_xor_Sum([7,3,5],3) == 12\n    assert pair_xor_Sum([7,3],2) == 4\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 778, "prompt": "Write a function to pack consecutive duplicates of a given list elements into sublists.\nfrom itertools import groupby\ndef pack_consecutive_duplicates(list1):", "code": "from itertools import groupby\ndef pack_consecutive_duplicates(list1):\n    return [list(group) for key, group in groupby(list1)]", "test_imports": [], "test_list": ["assert pack_consecutive_duplicates([0, 0, 1, 2, 3, 4, 4, 5, 6, 6, 6, 7, 8, 9, 4, 4])==[[0, 0], [1], [2], [3], [4, 4], [5], [6, 6, 6], [7], [8], [9], [4, 4]]", "assert pack_consecutive_duplicates([10, 10, 15, 19, 18, 18, 17, 26, 26, 17, 18, 10])==[[10, 10], [15], [19], [18, 18], [17], [26, 26], [17], [18], [10]]", "assert pack_consecutive_duplicates(['a', 'a', 'b', 'c', 'd', 'd'])==[['a', 'a'], ['b'], ['c'], ['d', 'd']]"], "entry_point": "pack_consecutive_duplicates", "test": "def check():\n    assert pack_consecutive_duplicates([0, 0, 1, 2, 3, 4, 4, 5, 6, 6, 6, 7, 8, 9, 4, 4])==[[0, 0], [1], [2], [3], [4, 4], [5], [6, 6, 6], [7], [8], [9], [4, 4]]\n    assert pack_consecutive_duplicates([10, 10, 15, 19, 18, 18, 17, 26, 26, 17, 18, 10])==[[10, 10], [15], [19], [18, 18], [17], [26, 26], [17], [18], [10]]\n    assert pack_consecutive_duplicates(['a', 'a', 'b', 'c', 'd', 'd'])==[['a', 'a'], ['b'], ['c'], ['d', 'd']]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 788, "prompt": "Write a function to create a new tuple from the given string and list.\n\ndef new_tuple(test_list, test_str):", "code": "def new_tuple(test_list, test_str):\n  return tuple(test_list + [test_str])", "test_imports": [], "test_list": ["assert new_tuple([\"WEB\", \"is\"], \"best\") == ('WEB', 'is', 'best')", "assert new_tuple([\"We\", \"are\"], \"Developers\") == ('We', 'are', 'Developers')", "assert new_tuple([\"Part\", \"is\"], \"Wrong\") == ('Part', 'is', 'Wrong')"], "entry_point": "new_tuple", "test": "def check():\n    assert new_tuple([\"WEB\", \"is\"], \"best\") == ('WEB', 'is', 'best')\n    assert new_tuple([\"We\", \"are\"], \"Developers\") == ('We', 'are', 'Developers')\n    assert new_tuple([\"Part\", \"is\"], \"Wrong\") == ('Part', 'is', 'Wrong')\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 804, "prompt": "Write a function to check whether the product of numbers in a list is even or not.\n\ndef is_product_even(arr): ", "code": "def is_product_even(arr): \n    for i in range(len(arr)): \n        if (arr[i] & 1) == 0: \n            return True\n    return False", "test_imports": [], "test_list": ["assert is_product_even([1,2,3])", "assert is_product_even([1,2,1,4])", "assert not is_product_even([1,1])"], "entry_point": "is_product_even", "test": "def check():\n    assert is_product_even([1,2,3])\n    assert is_product_even([1,2,1,4])\n    assert not is_product_even([1,1])\n"}
{"source_file": "Ellen's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 306, "prompt": "Write a function to find the maximum sum of increasing subsequence from prefix until ith index and also including a given kth element which is after i, i.e., k > i .\n\ndef max_sum_increasing_subseq(a, n, index, k):", "code": "def max_sum_increasing_subseq(a, n, index, k):\n\tdp = [[0 for i in range(n)] \n\t\t\tfor i in range(n)]\n\tfor i in range(n):\n\t\tif a[i] > a[0]:\n\t\t\tdp[0][i] = a[i] + a[0]\n\t\telse:\n\t\t\tdp[0][i] = a[i]\n\tfor i in range(1, n):\n\t\tfor j in range(n):\n\t\t\tif a[j] > a[i] and j > i:\n\t\t\t\tif dp[i - 1][i] + a[j] > dp[i - 1][j]:\n\t\t\t\t\tdp[i][j] = dp[i - 1][i] + a[j]\n\t\t\t\telse:\n\t\t\t\t\tdp[i][j] = dp[i - 1][j]\n\t\t\telse:\n\t\t\t\tdp[i][j] = dp[i - 1][j]\n\treturn dp[index][k]", "test_imports": [], "test_list": ["assert max_sum_increasing_subseq([1, 101, 2, 3, 100, 4, 5 ], 7, 4, 6) == 11", "assert max_sum_increasing_subseq([1, 101, 2, 3, 100, 4, 5 ], 7, 2, 5) == 7", "assert max_sum_increasing_subseq([11, 15, 19, 21, 26, 28, 31], 7, 2, 4) == 71"], "entry_point": "max_sum_increasing_subseq", "test": "def check():\n    assert max_sum_increasing_subseq([1, 101, 2, 3, 100, 4, 5 ], 7, 4, 6) == 11\n    assert max_sum_increasing_subseq([1, 101, 2, 3, 100, 4, 5 ], 7, 2, 5) == 7\n    assert max_sum_increasing_subseq([11, 15, 19, 21, 26, 28, 31], 7, 2, 4) == 71\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 757, "prompt": "Write a function to count the pairs of reverse strings in the given string list. https://www.geeksforgeeks.org/python-program-to-count-the-pairs-of-reverse-strings/\n\ndef count_reverse_pairs(test_list):", "code": "def count_reverse_pairs(test_list):\n  res = sum([1 for idx in range(0, len(test_list)) for idxn in range(idx, len( \n\ttest_list)) if test_list[idxn] == str(''.join(list(reversed(test_list[idx]))))]) \n  return res", "test_imports": [], "test_list": ["assert count_reverse_pairs([\"julia\", \"best\", \"tseb\", \"for\", \"ailuj\"])== 2", "assert count_reverse_pairs([\"geeks\", \"best\", \"for\", \"skeeg\"]) == 1", "assert count_reverse_pairs([\"makes\", \"best\", \"sekam\", \"for\", \"rof\"]) == 2"], "entry_point": "count_reverse_pairs", "test": "def check():\n    assert count_reverse_pairs([\"julia\", \"best\", \"tseb\", \"for\", \"ailuj\"])== 2\n    assert count_reverse_pairs([\"geeks\", \"best\", \"for\", \"skeeg\"]) == 1\n    assert count_reverse_pairs([\"makes\", \"best\", \"sekam\", \"for\", \"rof\"]) == 2\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 723, "prompt": "The input is defined as two lists of the same length. Write a function to count indices where the lists have the same values.\nfrom operator import eq\ndef count_same_pair(nums1, nums2):", "code": "from operator import eq\ndef count_same_pair(nums1, nums2):\n    result = sum(map(eq, nums1, nums2))\n    return result", "test_imports": [], "test_list": ["assert count_same_pair([1, 2, 3, 4, 5, 6, 7, 8],[2, 2, 3, 1, 2, 6, 7, 9])==4", "assert count_same_pair([0, 1, 2, -1, -5, 6, 0, -3, -2, 3, 4, 6, 8],[2, 1, 2, -1, -5, 6, 4, -3, -2, 3, 4, 6, 8])==11", "assert count_same_pair([2, 4, -6, -9, 11, -12, 14, -5, 17],[2, 1, 2, -1, -5, 6, 4, -3, -2, 3, 4, 6, 8])==1", "assert count_same_pair([0, 1, 1, 2],[0, 1, 2, 2])==3"], "entry_point": "count_same_pair", "test": "def check():\n    assert count_same_pair([1, 2, 3, 4, 5, 6, 7, 8],[2, 2, 3, 1, 2, 6, 7, 9])==4\n    assert count_same_pair([0, 1, 2, -1, -5, 6, 0, -3, -2, 3, 4, 6, 8],[2, 1, 2, -1, -5, 6, 4, -3, -2, 3, 4, 6, 8])==11\n    assert count_same_pair([2, 4, -6, -9, 11, -12, 14, -5, 17],[2, 1, 2, -1, -5, 6, 4, -3, -2, 3, 4, 6, 8])==1\n    assert count_same_pair([0, 1, 1, 2],[0, 1, 2, 2])==3\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 82, "prompt": "Write a function to find the volume of a sphere.\nimport math\ndef volume_sphere(r):", "code": "import math\ndef volume_sphere(r):\n  volume=(4/3)*math.pi*r*r*r\n  return volume", "test_imports": ["import math"], "test_list": ["assert math.isclose(volume_sphere(10), 4188.790204786391, rel_tol=0.001)", "assert math.isclose(volume_sphere(25), 65449.84694978735, rel_tol=0.001)", "assert math.isclose(volume_sphere(20), 33510.32163829113, rel_tol=0.001)"], "entry_point": "volume_sphere", "test": "def check():\n    assert math.isclose(volume_sphere(10), 4188.790204786391, rel_tol=0.001)\n    assert math.isclose(volume_sphere(25), 65449.84694978735, rel_tol=0.001)\n    assert math.isclose(volume_sphere(20), 33510.32163829113, rel_tol=0.001)\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 266, "prompt": "Write a function to find the lateral surface area of a cube given its side length.\n\ndef lateralsurface_cube(l):", "code": "def lateralsurface_cube(l):\n  LSA = 4 * (l * l)\n  return LSA", "test_imports": [], "test_list": ["assert lateralsurface_cube(5)==100", "assert lateralsurface_cube(9)==324", "assert lateralsurface_cube(10)==400"], "entry_point": "lateralsurface_cube", "test": "def check():\n    assert lateralsurface_cube(5)==100\n    assert lateralsurface_cube(9)==324\n    assert lateralsurface_cube(10)==400\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 607, "prompt": "Write a function to search a string for a regex pattern. The function should return the matching subtring, a start index and an end index.\nimport re\ndef find_literals(text, pattern):", "code": "import re\n\ndef find_literals(text, pattern):\n  match = re.search(pattern, text)\n  s = match.start()\n  e = match.end()\n  return (match.re.pattern, s, e)", "test_imports": [], "test_list": ["assert find_literals('The quick brown fox jumps over the lazy dog.', 'fox') == ('fox', 16, 19)", "assert find_literals('Its been a very crazy procedure right', 'crazy') == ('crazy', 16, 21)", "assert find_literals('Hardest choices required strongest will', 'will') == ('will', 35, 39)"], "entry_point": "find_literals", "test": "def check():\n    assert find_literals('The quick brown fox jumps over the lazy dog.', 'fox') == ('fox', 16, 19)\n    assert find_literals('Its been a very crazy procedure right', 'crazy') == ('crazy', 16, 21)\n    assert find_literals('Hardest choices required strongest will', 'will') == ('will', 35, 39)\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 786, "prompt": "Write a function to locate the right insertion point for a specified value in sorted order.\nimport bisect\ndef right_insertion(a, x):", "code": "import bisect\ndef right_insertion(a, x):\n    return bisect.bisect_right(a, x)", "test_imports": [], "test_list": ["assert right_insertion([1,2,4,5],6)==4", "assert right_insertion([1,2,4,5],3)==2", "assert right_insertion([1,2,4,5],7)==4"], "entry_point": "right_insertion", "test": "def check():\n    assert right_insertion([1,2,4,5],6)==4\n    assert right_insertion([1,2,4,5],3)==2\n    assert right_insertion([1,2,4,5],7)==4\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 439, "prompt": "Write a function to join a list of multiple integers into a single integer.\n\ndef multiple_to_single(L):", "code": "def multiple_to_single(L):\n  x = int(\"\".join(map(str, L)))\n  return x", "test_imports": [], "test_list": ["assert multiple_to_single([11, 33, 50])==113350", "assert multiple_to_single([-1,2,3,4,5,6])==-123456", "assert multiple_to_single([10,15,20,25])==10152025"], "entry_point": "multiple_to_single", "test": "def check():\n    assert multiple_to_single([11, 33, 50])==113350\n    assert multiple_to_single([-1,2,3,4,5,6])==-123456\n    assert multiple_to_single([10,15,20,25])==10152025\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 440, "prompt": "Write a function to find the first adverb and their positions in a given sentence.\nimport re\ndef find_adverb_position(text):", "code": "import re\ndef find_adverb_position(text):\n for m in re.finditer(r\"\\w+ly\", text):\n    return (m.start(), m.end(), m.group(0))", "test_imports": [], "test_list": ["assert find_adverb_position(\"clearly!! we can see the sky\")==(0, 7, 'clearly')", "assert find_adverb_position(\"seriously!! there are many roses\")==(0, 9, 'seriously')", "assert find_adverb_position(\"unfortunately!! sita is going to home\")==(0, 13, 'unfortunately')"], "entry_point": "find_adverb_position", "test": "def check():\n    assert find_adverb_position(\"clearly!! we can see the sky\")==(0, 7, 'clearly')\n    assert find_adverb_position(\"seriously!! there are many roses\")==(0, 9, 'seriously')\n    assert find_adverb_position(\"unfortunately!! sita is going to home\")==(0, 13, 'unfortunately')\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 105, "prompt": "Write a python function to count true booleans in the given list.\n\ndef count(lst):   ", "code": "def count(lst):   \n    return sum(lst) ", "test_imports": [], "test_list": ["assert count([True,False,True]) == 2", "assert count([False,False]) == 0", "assert count([True,True,True]) == 3"], "entry_point": "count", "test": "def check():\n    assert count([True,False,True]) == 2\n    assert count([False,False]) == 0\n    assert count([True,True,True]) == 3\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 232, "prompt": "Write a function that takes in a list and an integer n and returns a list containing the n largest items from the list.\nimport heapq\ndef larg_nnum(list1,n):", "code": "import heapq\ndef larg_nnum(list1,n):\n largest=heapq.nlargest(n,list1)\n return largest", "test_imports": [], "test_list": ["assert set(larg_nnum([10, 20, 50, 70, 90, 20, 50, 40, 60, 80, 100],2))==set([100,90])", "assert set(larg_nnum([10, 20, 50, 70, 90, 20, 50, 40, 60, 80, 100],5))==set([100,90,80,70,60])", "assert set(larg_nnum([10, 20, 50, 70, 90, 20, 50, 40, 60, 80, 100],3))==set([100,90,80])"], "entry_point": "larg_nnum", "test": "def check():\n    assert set(larg_nnum([10, 20, 50, 70, 90, 20, 50, 40, 60, 80, 100],2))==set([100,90])\n    assert set(larg_nnum([10, 20, 50, 70, 90, 20, 50, 40, 60, 80, 100],5))==set([100,90,80,70,60])\n    assert set(larg_nnum([10, 20, 50, 70, 90, 20, 50, 40, 60, 80, 100],3))==set([100,90,80])\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 555, "prompt": "Write a python function to find the difference between the sum of cubes of the first n natural numbers and the sum of the first n natural numbers.\n\ndef difference(n) :  ", "code": "def difference(n) :  \n    S = (n*(n + 1))//2;  \n    res = S*(S-1);  \n    return res;  ", "test_imports": [], "test_list": ["assert difference(3) == 30", "assert difference(5) == 210", "assert difference(2) == 6"], "entry_point": "difference", "test": "def check():\n    assert difference(3) == 30\n    assert difference(5) == 210\n    assert difference(2) == 6\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 753, "prompt": "Write a function to find minimum k records from tuple list. https://www.geeksforgeeks.org/python-find-minimum-k-records-from-tuple-list/ - in this case a verbatim copy of test cases\n\ndef min_k(test_list, K):", "code": "def min_k(test_list, K):\n  res = sorted(test_list, key = lambda x: x[1])[:K]\n  return (res) ", "test_imports": [], "test_list": ["assert min_k([('Manjeet', 10), ('Akshat', 4), ('Akash', 2), ('Nikhil', 8)], 2) == [('Akash', 2), ('Akshat', 4)]", "assert min_k([('Sanjeev', 11), ('Angat', 5), ('Akash', 3), ('Nepin', 9)], 3) == [('Akash', 3), ('Angat', 5), ('Nepin', 9)]", "assert min_k([('tanmay', 14), ('Amer', 11), ('Ayesha', 9), ('SKD', 16)], 1) == [('Ayesha', 9)]"], "entry_point": "min_k", "test": "def check():\n    assert min_k([('Manjeet', 10), ('Akshat', 4), ('Akash', 2), ('Nikhil', 8)], 2) == [('Akash', 2), ('Akshat', 4)]\n    assert min_k([('Sanjeev', 11), ('Angat', 5), ('Akash', 3), ('Nepin', 9)], 3) == [('Akash', 3), ('Angat', 5), ('Nepin', 9)]\n    assert min_k([('tanmay', 14), ('Amer', 11), ('Ayesha', 9), ('SKD', 16)], 1) == [('Ayesha', 9)]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 9, "prompt": "Write a python function to find the minimum number of rotations (greater than 0) required to get the same string.\n\ndef find_Rotations(str): ", "code": "def find_Rotations(str): \n    tmp = str + str\n    n = len(str) \n    for i in range(1,n + 1): \n        substring = tmp[i: i+n] \n        if (str == substring): \n            return i \n    return n ", "test_imports": [], "test_list": ["assert find_Rotations(\"aaaa\") == 1", "assert find_Rotations(\"ab\") == 2", "assert find_Rotations(\"abc\") == 3"], "entry_point": "find_Rotations", "test": "def check():\n    assert find_Rotations(\"aaaa\") == 1\n    assert find_Rotations(\"ab\") == 2\n    assert find_Rotations(\"abc\") == 3\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 790, "prompt": "Write a python function to check whether every even index contains even numbers of a given list.\n\ndef even_position(nums):", "code": "def even_position(nums):\n\treturn all(nums[i]%2==i%2 for i in range(len(nums)))", "test_imports": [], "test_list": ["assert even_position([3,2,1]) == False", "assert even_position([1,2,3]) == False", "assert even_position([2,1,4]) == True"], "entry_point": "even_position", "test": "def check():\n    assert even_position([3,2,1]) == False\n    assert even_position([1,2,3]) == False\n    assert even_position([2,1,4]) == True\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 588, "prompt": "Write a python function to find the difference between largest and smallest value in a given list.\n\ndef big_diff(nums):", "code": "def big_diff(nums):\n     diff= max(nums)-min(nums)\n     return diff", "test_imports": [], "test_list": ["assert big_diff([1,2,3,4]) == 3", "assert big_diff([4,5,12]) == 8", "assert big_diff([9,2,3]) == 7"], "entry_point": "big_diff", "test": "def check():\n    assert big_diff([1,2,3,4]) == 3\n    assert big_diff([4,5,12]) == 8\n    assert big_diff([9,2,3]) == 7\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 286, "prompt": "Write a function to find the largest sum of a contiguous array in the modified array which is formed by repeating the given array k times.\n\ndef max_sub_array_sum_repeated(a, n, k): ", "code": "def max_sub_array_sum_repeated(a, n, k): \n\tmax_so_far = -2147483648\n\tmax_ending_here = 0\n\tfor i in range(n*k): \n\t\tmax_ending_here = max_ending_here + a[i%n] \n\t\tif (max_so_far < max_ending_here): \n\t\t\tmax_so_far = max_ending_here \n\t\tif (max_ending_here < 0): \n\t\t\tmax_ending_here = 0\n\treturn max_so_far", "test_imports": [], "test_list": ["assert max_sub_array_sum_repeated([10, 20, -30, -1], 4, 3) == 30", "assert max_sub_array_sum_repeated([-1, 10, 20], 3, 2) == 59", "assert max_sub_array_sum_repeated([-1, -2, -3], 3, 3) == -1"], "entry_point": "max_sub_array_sum_repeated", "test": "def check():\n    assert max_sub_array_sum_repeated([10, 20, -30, -1], 4, 3) == 30\n    assert max_sub_array_sum_repeated([-1, 10, 20], 3, 2) == 59\n    assert max_sub_array_sum_repeated([-1, -2, -3], 3, 3) == -1\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 278, "prompt": "Write a function to find the number of elements that occurs before the tuple element in the given tuple.\n\ndef count_first_elements(test_tup):", "code": "def count_first_elements(test_tup):\n  for count, ele in enumerate(test_tup):\n    if isinstance(ele, tuple):\n      break\n  return (count) ", "test_imports": [], "test_list": ["assert count_first_elements((1, 5, 7, (4, 6), 10) ) == 3", "assert count_first_elements((2, 9, (5, 7), 11) ) == 2", "assert count_first_elements((11, 15, 5, 8, (2, 3), 8) ) == 4"], "entry_point": "count_first_elements", "test": "def check():\n    assert count_first_elements((1, 5, 7, (4, 6), 10) ) == 3\n    assert count_first_elements((2, 9, (5, 7), 11) ) == 2\n    assert count_first_elements((11, 15, 5, 8, (2, 3), 8) ) == 4\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 16, "prompt": "Write a function to that returns true if the input string contains sequences of lowercase letters joined with an underscore and false otherwise.\nimport re\ndef text_lowercase_underscore(text):", "code": "import re\ndef text_lowercase_underscore(text):\n        patterns = '^[a-z]+_[a-z]+$'\n        if re.search(patterns,  text):\n                return True\n        else:\n                return False", "test_imports": [], "test_list": ["assert text_lowercase_underscore(\"aab_cbbbc\")==(True)", "assert text_lowercase_underscore(\"aab_Abbbc\")==(False)", "assert text_lowercase_underscore(\"Aaab_abbbc\")==(False)"], "entry_point": "text_lowercase_underscore", "test": "def check():\n    assert text_lowercase_underscore(\"aab_cbbbc\")==(True)\n    assert text_lowercase_underscore(\"aab_Abbbc\")==(False)\n    assert text_lowercase_underscore(\"Aaab_abbbc\")==(False)\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 434, "prompt": "Write a function that matches a string that has an a followed by one or more b's.\nimport re\ndef text_match_one(text):", "code": "import re\ndef text_match_one(text):\n        patterns = 'ab+?'\n        if re.search(patterns,  text):\n                return True\n        else:\n                return False\n", "test_imports": [], "test_list": ["assert text_match_one(\"ac\")==False", "assert text_match_one(\"dc\")==False", "assert text_match_one(\"abba\")==True"], "entry_point": "text_match_one", "test": "def check():\n    assert text_match_one(\"ac\")==False\n    assert text_match_one(\"dc\")==False\n    assert text_match_one(\"abba\")==True\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 222, "prompt": "Write a function to check if all the elements in tuple have same data type or not.\n\ndef check_type(test_tuple):", "code": "def check_type(test_tuple):\n  res = True\n  for ele in test_tuple:\n    if not isinstance(ele, type(test_tuple[0])):\n      res = False\n      break\n  return (res) ", "test_imports": [], "test_list": ["assert check_type((5, 6, 7, 3, 5, 6) ) == True", "assert check_type((1, 2, \"4\") ) == False", "assert check_type((3, 2, 1, 4, 5) ) == True"], "entry_point": "check_type", "test": "def check():\n    assert check_type((5, 6, 7, 3, 5, 6) ) == True\n    assert check_type((1, 2, \"4\") ) == False\n    assert check_type((3, 2, 1, 4, 5) ) == True\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 133, "prompt": "Write a function to calculate the sum of the negative numbers of a given list of numbers.\n\ndef sum_negativenum(nums):", "code": "def sum_negativenum(nums):\n  sum_negativenum = list(filter(lambda nums:nums<0,nums))\n  return sum(sum_negativenum)", "test_imports": [], "test_list": ["assert sum_negativenum([2, 4, -6, -9, 11, -12, 14, -5, 17])==-32", "assert sum_negativenum([10,15,-14,13,-18,12,-20])==-52", "assert sum_negativenum([19, -65, 57, 39, 152,-639, 121, 44, 90, -190])==-894"], "entry_point": "sum_negativenum", "test": "def check():\n    assert sum_negativenum([2, 4, -6, -9, 11, -12, 14, -5, 17])==-32\n    assert sum_negativenum([10,15,-14,13,-18,12,-20])==-52\n    assert sum_negativenum([19, -65, 57, 39, 152,-639, 121, 44, 90, -190])==-894\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 450, "prompt": "Write a function to extract specified size of strings from a given list of string values.\n\ndef extract_string(string_list, size):", "code": "def extract_string(str, l):\n    result = [e for e in str if len(e) == l] \n    return result", "test_imports": [], "test_list": ["assert extract_string(['Python', 'list', 'exercises', 'practice', 'solution'] ,8)==['practice', 'solution']", "assert extract_string(['Python', 'list', 'exercises', 'practice', 'solution'] ,6)==['Python']", "assert extract_string(['Python', 'list', 'exercises', 'practice', 'solution'] ,9)==['exercises']"], "entry_point": "extract_string", "test": "def check():\n    assert extract_string(['Python', 'list', 'exercises', 'practice', 'solution'] ,8)==['practice', 'solution']\n    assert extract_string(['Python', 'list', 'exercises', 'practice', 'solution'] ,6)==['Python']\n    assert extract_string(['Python', 'list', 'exercises', 'practice', 'solution'] ,9)==['exercises']\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 610, "prompt": "Write a python function which takes a list and returns a list with the same elements, but the k'th element removed.\n\ndef remove_kth_element(list1, L):", "code": "def remove_kth_element(list1, L):\n    return  list1[:L-1] + list1[L:]", "test_imports": [], "test_list": ["assert remove_kth_element([1,1,2,3,4,4,5,1],3)==[1, 1, 3, 4, 4, 5, 1]", "assert remove_kth_element([0, 0, 1, 2, 3, 4, 4, 5, 6, 6, 6, 7, 8, 9, 4, 4],4)==[0, 0, 1, 3, 4, 4, 5, 6, 6, 6, 7, 8, 9, 4, 4]", "assert remove_kth_element([10, 10, 15, 19, 18, 18, 17, 26, 26, 17, 18, 10],5)==[10,10,15,19, 18, 17, 26, 26, 17, 18, 10]"], "entry_point": "remove_kth_element", "test": "def check():\n    assert remove_kth_element([1,1,2,3,4,4,5,1],3)==[1, 1, 3, 4, 4, 5, 1]\n    assert remove_kth_element([0, 0, 1, 2, 3, 4, 4, 5, 6, 6, 6, 7, 8, 9, 4, 4],4)==[0, 0, 1, 3, 4, 4, 5, 6, 6, 6, 7, 8, 9, 4, 4]\n    assert remove_kth_element([10, 10, 15, 19, 18, 18, 17, 26, 26, 17, 18, 10],5)==[10,10,15,19, 18, 17, 26, 26, 17, 18, 10]\n"}
{"source_file": "Ellen's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 297, "prompt": "Write a function to flatten a given nested list structure.\n\ndef flatten_list(list1):", "code": "def flatten_list(list1):\n    result_list = []\n    if not list1: return result_list\n    stack = [list(list1)]\n    while stack:\n        c_num = stack.pop()\n        next = c_num.pop()\n        if c_num: stack.append(c_num)\n        if isinstance(next, list):\n            if next: stack.append(list(next))\n        else: result_list.append(next)\n    result_list.reverse()\n    return result_list ", "test_imports": [], "test_list": ["assert flatten_list([0, 10, [20, 30], 40, 50, [60, 70, 80], [90, 100, 110, 120]])==[0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120]", "assert flatten_list([[10, 20], [40], [30, 56, 25], [10, 20], [33], [40]])==[10, 20, 40, 30, 56, 25, 10, 20, 33, 40]", "assert flatten_list([[1,2,3], [4,5,6], [10,11,12], [7,8,9]])==[1, 2, 3, 4, 5, 6, 10, 11, 12, 7, 8, 9]"], "entry_point": "flatten_list", "test": "def check():\n    assert flatten_list([0, 10, [20, 30], 40, 50, [60, 70, 80], [90, 100, 110, 120]])==[0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120]\n    assert flatten_list([[10, 20], [40], [30, 56, 25], [10, 20], [33], [40]])==[10, 20, 40, 30, 56, 25, 10, 20, 33, 40]\n    assert flatten_list([[1,2,3], [4,5,6], [10,11,12], [7,8,9]])==[1, 2, 3, 4, 5, 6, 10, 11, 12, 7, 8, 9]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 782, "prompt": "Write a python function to find the sum of all odd length subarrays. https://www.geeksforgeeks.org/sum-of-all-odd-length-subarrays/\n\ndef odd_length_sum(arr):", "code": "def odd_length_sum(arr):\n    Sum = 0\n    l = len(arr)\n    for i in range(l):\n        Sum += ((((i + 1) *(l - i) + 1) // 2) * arr[i])\n    return Sum", "test_imports": [], "test_list": ["assert odd_length_sum([1,2,4]) == 14", "assert odd_length_sum([1,2,1,2]) == 15", "assert odd_length_sum([1,7]) == 8"], "entry_point": "odd_length_sum", "test": "def check():\n    assert odd_length_sum([1,2,4]) == 14\n    assert odd_length_sum([1,2,1,2]) == 15\n    assert odd_length_sum([1,7]) == 8\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 452, "prompt": "Write a function that gives loss amount on a sale if the given amount has loss else return 0.\n\ndef loss_amount(actual_cost,sale_amount): ", "code": "def loss_amount(actual_cost,sale_amount): \n  if(sale_amount > actual_cost):\n    amount = sale_amount - actual_cost\n    return amount\n  else:\n    return 0", "test_imports": [], "test_list": ["assert loss_amount(1500,1200)==0", "assert loss_amount(100,200)==100", "assert loss_amount(2000,5000)==3000"], "entry_point": "loss_amount", "test": "def check():\n    assert loss_amount(1500,1200)==0\n    assert loss_amount(100,200)==100\n    assert loss_amount(2000,5000)==3000\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 460, "prompt": "Write a python function to get the first element of each sublist.\n\ndef Extract(lst): ", "code": "def Extract(lst): \n    return [item[0] for item in lst] ", "test_imports": [], "test_list": ["assert Extract([[1, 2], [3, 4, 5], [6, 7, 8, 9]]) == [1, 3, 6]", "assert Extract([[1,2,3],[4, 5]]) == [1,4]", "assert Extract([[9,8,1],[1,2]]) == [9,1]"], "entry_point": "Extract", "test": "def check():\n    assert Extract([[1, 2], [3, 4, 5], [6, 7, 8, 9]]) == [1, 3, 6]\n    assert Extract([[1,2,3],[4, 5]]) == [1,4]\n    assert Extract([[9,8,1],[1,2]]) == [9,1]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 401, "prompt": "Write a function to perform index wise addition of tuple elements in the given two nested tuples.\n\ndef add_nested_tuples(test_tup1, test_tup2):", "code": "def add_nested_tuples(test_tup1, test_tup2):\n  res = tuple(tuple(a + b for a, b in zip(tup1, tup2))\n   for tup1, tup2 in zip(test_tup1, test_tup2))\n  return (res) ", "test_imports": [], "test_list": ["assert add_nested_tuples(((1, 3), (4, 5), (2, 9), (1, 10)), ((6, 7), (3, 9), (1, 1), (7, 3))) == ((7, 10), (7, 14), (3, 10), (8, 13))", "assert add_nested_tuples(((2, 4), (5, 6), (3, 10), (2, 11)), ((7, 8), (4, 10), (2, 2), (8, 4))) == ((9, 12), (9, 16), (5, 12), (10, 15))", "assert add_nested_tuples(((3, 5), (6, 7), (4, 11), (3, 12)), ((8, 9), (5, 11), (3, 3), (9, 5))) == ((11, 14), (11, 18), (7, 14), (12, 17))"], "entry_point": "add_nested_tuples", "test": "def check():\n    assert add_nested_tuples(((1, 3), (4, 5), (2, 9), (1, 10)), ((6, 7), (3, 9), (1, 1), (7, 3))) == ((7, 10), (7, 14), (3, 10), (8, 13))\n    assert add_nested_tuples(((2, 4), (5, 6), (3, 10), (2, 11)), ((7, 8), (4, 10), (2, 2), (8, 4))) == ((9, 12), (9, 16), (5, 12), (10, 15))\n    assert add_nested_tuples(((3, 5), (6, 7), (4, 11), (3, 12)), ((8, 9), (5, 11), (3, 3), (9, 5))) == ((11, 14), (11, 18), (7, 14), (12, 17))\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 733, "prompt": "Write a function to find the index of the first occurrence of a given number in a sorted array.\n\ndef find_first_occurrence(A, x):", "code": "def find_first_occurrence(A, x):\n    (left, right) = (0, len(A) - 1)\n    result = -1\n    while left <= right:\n        mid = (left + right) // 2\n        if x == A[mid]:\n            result = mid\n            right = mid - 1\n        elif x < A[mid]:\n            right = mid - 1\n        else:\n            left = mid + 1\n    return result", "test_imports": [], "test_list": ["assert find_first_occurrence([2, 5, 5, 5, 6, 6, 8, 9, 9, 9], 5) == 1", "assert find_first_occurrence([2, 3, 5, 5, 6, 6, 8, 9, 9, 9], 5) == 2", "assert find_first_occurrence([2, 4, 1, 5, 6, 6, 8, 9, 9, 9], 6) == 4"], "entry_point": "find_first_occurrence", "test": "def check():\n    assert find_first_occurrence([2, 5, 5, 5, 6, 6, 8, 9, 9, 9], 5) == 1\n    assert find_first_occurrence([2, 3, 5, 5, 6, 6, 8, 9, 9, 9], 5) == 2\n    assert find_first_occurrence([2, 4, 1, 5, 6, 6, 8, 9, 9, 9], 6) == 4\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 143, "prompt": "Write a function to find number of lists present in the given tuple.\n\ndef find_lists(Input): ", "code": "def find_lists(Input): \n\tif isinstance(Input, list): \n\t\treturn 1\n\telse: \n\t\treturn len(Input) ", "test_imports": [], "test_list": ["assert find_lists(([1, 2, 3, 4], [5, 6, 7, 8])) == 2", "assert find_lists(([1, 2], [3, 4], [5, 6]))  == 3", "assert find_lists(([9, 8, 7, 6, 5, 4, 3, 2, 1])) == 1"], "entry_point": "find_lists", "test": "def check():\n    assert find_lists(([1, 2, 3, 4], [5, 6, 7, 8])) == 2\n    assert find_lists(([1, 2], [3, 4], [5, 6]))  == 3\n    assert find_lists(([9, 8, 7, 6, 5, 4, 3, 2, 1])) == 1\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 581, "prompt": "Write a python function to find the surface area of a square pyramid with a given base edge and height.\n\ndef surface_Area(b,s): ", "code": "def surface_Area(b,s): \n    return 2 * b * s + pow(b,2) ", "test_imports": [], "test_list": ["assert surface_Area(3,4) == 33", "assert surface_Area(4,5) == 56", "assert surface_Area(1,2) == 5"], "entry_point": "surface_Area", "test": "def check():\n    assert surface_Area(3,4) == 33\n    assert surface_Area(4,5) == 56\n    assert surface_Area(1,2) == 5\n"}
{"source_file": "Ellen's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 293, "prompt": "Write a function to find the third side of a right angled triangle.\nimport math\ndef otherside_rightangle(w,h):", "code": "import math\ndef otherside_rightangle(w,h):\n  s=math.sqrt((w*w)+(h*h))\n  return s", "test_imports": [], "test_list": ["assert otherside_rightangle(7,8)==10.63014581273465", "assert otherside_rightangle(3,4)==5", "assert otherside_rightangle(7,15)==16.55294535724685"], "entry_point": "otherside_rightangle", "test": "def check():\n    assert otherside_rightangle(7,8)==10.63014581273465\n    assert otherside_rightangle(3,4)==5\n    assert otherside_rightangle(7,15)==16.55294535724685\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 422, "prompt": "Write a python function to find the average of cubes of first n natural numbers.\n\ndef find_Average_Of_Cube(n):  ", "code": "def find_Average_Of_Cube(n):  \n    sum = 0\n    for i in range(1, n + 1): \n        sum += i * i * i  \n    return round(sum / n, 6) ", "test_imports": [], "test_list": ["assert find_Average_Of_Cube(2) == 4.5", "assert find_Average_Of_Cube(3) == 12", "assert find_Average_Of_Cube(1) == 1"], "entry_point": "find_Average_Of_Cube", "test": "def check():\n    assert find_Average_Of_Cube(2) == 4.5\n    assert find_Average_Of_Cube(3) == 12\n    assert find_Average_Of_Cube(1) == 1\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 274, "prompt": "Write a python function that takes in a positive integer n and finds the sum of even index binomial coefficients.\nimport math  \ndef even_binomial_Coeff_Sum( n): ", "code": "import math  \ndef even_binomial_Coeff_Sum( n): \n    return (1 << (n - 1)) ", "test_imports": [], "test_list": ["assert even_binomial_Coeff_Sum(4) == 8", "assert even_binomial_Coeff_Sum(6) == 32", "assert even_binomial_Coeff_Sum(2) == 2"], "entry_point": "even_binomial_Coeff_Sum", "test": "def check():\n    assert even_binomial_Coeff_Sum(4) == 8\n    assert even_binomial_Coeff_Sum(6) == 32\n    assert even_binomial_Coeff_Sum(2) == 2\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 4, "prompt": "Write a function to find the n largest integers from a given list of numbers, returned in descending order.\nimport heapq as hq\ndef heap_queue_largest(nums,n):", "code": "import heapq as hq\ndef heap_queue_largest(nums,n):\n  largest_nums = hq.nlargest(n, nums)\n  return largest_nums", "test_imports": [], "test_list": ["assert heap_queue_largest( [25, 35, 22, 85, 14, 65, 75, 22, 58],3)==[85, 75, 65]", "assert heap_queue_largest( [25, 35, 22, 85, 14, 65, 75, 22, 58],2)==[85, 75]", "assert heap_queue_largest( [25, 35, 22, 85, 14, 65, 75, 22, 58],5)==[85, 75, 65, 58, 35]"], "entry_point": "heap_queue_largest", "test": "def check():\n    assert heap_queue_largest( [25, 35, 22, 85, 14, 65, 75, 22, 58],3)==[85, 75, 65]\n    assert heap_queue_largest( [25, 35, 22, 85, 14, 65, 75, 22, 58],2)==[85, 75]\n    assert heap_queue_largest( [25, 35, 22, 85, 14, 65, 75, 22, 58],5)==[85, 75, 65, 58, 35]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 161, "prompt": "Write a function to remove all elements from a given list present in another list.\n\ndef remove_elements(list1, list2):", "code": "def remove_elements(list1, list2):\n    result = [x for x in list1 if x not in list2]\n    return result", "test_imports": [], "test_list": ["assert remove_elements([1, 2, 3, 4, 5, 6, 7, 8, 9, 10], [2, 4, 6, 8]) == [1, 3, 5, 7, 9, 10]", "assert remove_elements([1, 2, 3, 4, 5, 6, 7, 8, 9, 10], [1, 3, 5, 7]) == [2, 4, 6, 8, 9, 10]", "assert remove_elements([1, 2, 3, 4, 5, 6, 7, 8, 9, 10], [5, 7]) == [1, 2, 3, 4, 6, 8, 9, 10]"], "entry_point": "remove_elements", "test": "def check():\n    assert remove_elements([1, 2, 3, 4, 5, 6, 7, 8, 9, 10], [2, 4, 6, 8]) == [1, 3, 5, 7, 9, 10]\n    assert remove_elements([1, 2, 3, 4, 5, 6, 7, 8, 9, 10], [1, 3, 5, 7]) == [2, 4, 6, 8, 9, 10]\n    assert remove_elements([1, 2, 3, 4, 5, 6, 7, 8, 9, 10], [5, 7]) == [1, 2, 3, 4, 6, 8, 9, 10]\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 119, "prompt": "Write a python function to find the element that appears only once in a sorted array.\n\ndef search(arr):", "code": "def search(arr):\n    n = len(arr)\n    XOR = 0\n    for i in range(n) :\n        XOR = XOR ^ arr[i]\n    return (XOR)", "test_imports": [], "test_list": ["assert search([1,1,2,2,3]) == 3", "assert search([1,1,3,3,4,4,5,5,7,7,8]) == 8", "assert search([1,2,2,3,3,4,4]) == 1"], "entry_point": "search", "test": "def check():\n    assert search([1,1,2,2,3]) == 3\n    assert search([1,1,3,3,4,4,5,5,7,7,8]) == 8\n    assert search([1,2,2,3,3,4,4]) == 1\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 441, "prompt": "Write a function to find the surface area of a cube of a given size.\n\ndef surfacearea_cube(l):", "code": "def surfacearea_cube(l):\n  surfacearea= 6*l*l\n  return surfacearea", "test_imports": [], "test_list": ["assert surfacearea_cube(5)==150", "assert surfacearea_cube(3)==54", "assert surfacearea_cube(10)==600"], "entry_point": "surfacearea_cube", "test": "def check():\n    assert surfacearea_cube(5)==150\n    assert surfacearea_cube(3)==54\n    assert surfacearea_cube(10)==600\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 247, "prompt": "Write a function to find the length of the longest palindromic subsequence in the given string.\n\ndef lps(str): ", "code": "def lps(str): \n\tn = len(str) \n\tL = [[0 for x in range(n)] for x in range(n)] \n\tfor i in range(n): \n\t\tL[i][i] = 1\n\tfor cl in range(2, n+1): \n\t\tfor i in range(n-cl+1): \n\t\t\tj = i+cl-1\n\t\t\tif str[i] == str[j] and cl == 2: \n\t\t\t\tL[i][j] = 2\n\t\t\telif str[i] == str[j]: \n\t\t\t\tL[i][j] = L[i+1][j-1] + 2\n\t\t\telse: \n\t\t\t\tL[i][j] = max(L[i][j-1], L[i+1][j]); \n\treturn L[0][n-1]", "test_imports": [], "test_list": ["assert lps(\"TENS FOR TENS\") == 5", "assert lps(\"CARDIO FOR CARDS\") == 7", "assert lps(\"PART OF THE JOURNEY IS PART\") == 9"], "entry_point": "lps", "test": "def check():\n    assert lps(\"TENS FOR TENS\") == 5\n    assert lps(\"CARDIO FOR CARDS\") == 7\n    assert lps(\"PART OF THE JOURNEY IS PART\") == 9\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 165, "prompt": "Write a function to count the number of characters in a string that occur at the same position in the string as in the English alphabet (case insensitive).\n\ndef count_char_position(str1): ", "code": "def count_char_position(str1): \n    count_chars = 0\n    for i in range(len(str1)):\n        if ((i == ord(str1[i]) - ord('A')) or \n            (i == ord(str1[i]) - ord('a'))): \n            count_chars += 1\n    return count_chars ", "test_imports": [], "test_list": ["assert count_char_position(\"xbcefg\") == 2", "assert count_char_position(\"ABcED\") == 3", "assert count_char_position(\"AbgdeF\") == 5"], "entry_point": "count_char_position", "test": "def check():\n    assert count_char_position(\"xbcefg\") == 2\n    assert count_char_position(\"ABcED\") == 3\n    assert count_char_position(\"AbgdeF\") == 5\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 270, "prompt": "Write a python function to find the sum of even numbers at even positions of a list.\n\ndef sum_even_and_even_index(arr):  ", "code": "def sum_even_and_even_index(arr):  \n    i = 0\n    sum = 0\n    for i in range(0, len(arr),2): \n        if (arr[i] % 2 == 0) : \n            sum += arr[i]  \n    return sum", "test_imports": [], "test_list": ["assert sum_even_and_even_index([5, 6, 12, 1, 18, 8]) == 30", "assert sum_even_and_even_index([3, 20, 17, 9, 2, 10, 18, 13, 6, 18]) == 26", "assert sum_even_and_even_index([5, 6, 12, 1]) == 12"], "entry_point": "sum_even_and_even_index", "test": "def check():\n    assert sum_even_and_even_index([5, 6, 12, 1, 18, 8]) == 30\n    assert sum_even_and_even_index([3, 20, 17, 9, 2, 10, 18, 13, 6, 18]) == 26\n    assert sum_even_and_even_index([5, 6, 12, 1]) == 12\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 622, "prompt": "Write a function to find the median of two sorted lists of same size.\n\ndef get_median(arr1, arr2, n):", "code": "def get_median(arr1, arr2, n):\n  i = 0\n  j = 0\n  m1 = -1\n  m2 = -1\n  count = 0\n  while count < n + 1:\n    count += 1\n    if i == n:\n      m1 = m2\n      m2 = arr2[0]\n      break\n    elif j == n:\n      m1 = m2\n      m2 = arr1[0]\n      break\n    if arr1[i] <= arr2[j]:\n      m1 = m2\n      m2 = arr1[i]\n      i += 1\n    else:\n      m1 = m2\n      m2 = arr2[j]\n      j += 1\n  return (m1 + m2)/2", "test_imports": [], "test_list": ["assert get_median([1, 12, 15, 26, 38], [2, 13, 17, 30, 45], 5) == 16.0", "assert get_median([2, 4, 8, 9], [7, 13, 19, 28], 4) == 8.5", "assert get_median([3, 6, 14, 23, 36, 42], [2, 18, 27, 39, 49, 55], 6) == 25.0"], "entry_point": "get_median", "test": "def check():\n    assert get_median([1, 12, 15, 26, 38], [2, 13, 17, 30, 45], 5) == 16.0\n    assert get_median([2, 4, 8, 9], [7, 13, 19, 28], 4) == 8.5\n    assert get_median([3, 6, 14, 23, 36, 42], [2, 18, 27, 39, 49, 55], 6) == 25.0\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 459, "prompt": "Write a function to remove uppercase substrings from a given string.\nimport re\ndef remove_uppercase(str1):", "code": "import re\ndef remove_uppercase(str1):\n  return re.sub('[A-Z]', '', str1)", "test_imports": [], "test_list": ["assert remove_uppercase('cAstyoUrFavoRitETVshoWs') == 'cstyoravoitshos'", "assert remove_uppercase('wAtchTheinTernEtrAdIo') == 'wtchheinerntrdo'", "assert remove_uppercase('VoicESeaRchAndreComMendaTionS') == 'oiceachndreomendaion'"], "entry_point": "remove_uppercase", "test": "def check():\n    assert remove_uppercase('cAstyoUrFavoRitETVshoWs') == 'cstyoravoitshos'\n    assert remove_uppercase('wAtchTheinTernEtrAdIo') == 'wtchheinerntrdo'\n    assert remove_uppercase('VoicESeaRchAndreComMendaTionS') == 'oiceachndreomendaion'\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 166, "prompt": "Write a function that counts the number of pairs of integers in a list that xor to an even number.\n\ndef find_even_pair(A): ", "code": "def find_even_pair(A): \n  count = 0\n  for i in range(0, len(A)): \n    for j in range(i+1, len(A)): \n        if ((A[i] ^ A[j]) % 2 == 0): \n          count += 1\n\n  return count", "test_imports": [], "test_list": ["assert find_even_pair([5, 4, 7, 2, 1]) == 4", "assert find_even_pair([7, 2, 8, 1, 0, 5, 11]) == 9", "assert find_even_pair([1, 2, 3]) == 1"], "entry_point": "find_even_pair", "test": "def check():\n    assert find_even_pair([5, 4, 7, 2, 1]) == 4\n    assert find_even_pair([7, 2, 8, 1, 0, 5, 11]) == 9\n    assert find_even_pair([1, 2, 3]) == 1\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 478, "prompt": "Write a function to remove lowercase substrings from a given string.\nimport re\ndef remove_lowercase(str1):", "code": "import re\ndef remove_lowercase(str1):\n return re.sub('[a-z]', '', str1)", "test_imports": [], "test_list": ["assert remove_lowercase(\"PYTHon\")==('PYTH')", "assert remove_lowercase(\"FInD\")==('FID')", "assert remove_lowercase(\"STRinG\")==('STRG')"], "entry_point": "remove_lowercase", "test": "def check():\n    assert remove_lowercase(\"PYTHon\")==('PYTH')\n    assert remove_lowercase(\"FInD\")==('FID')\n    assert remove_lowercase(\"STRinG\")==('STRG')\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 118, "prompt": "Write a function to convert a string to a list of strings split on the space character.\n\ndef string_to_list(string): ", "code": "def string_to_list(string): \n    lst = list(string.split(\" \")) \n    return lst", "test_imports": [], "test_list": ["assert string_to_list(\"python programming\")==['python','programming']", "assert string_to_list(\"lists tuples strings\")==['lists','tuples','strings']", "assert string_to_list(\"write a program\")==['write','a','program']"], "entry_point": "string_to_list", "test": "def check():\n    assert string_to_list(\"python programming\")==['python','programming']\n    assert string_to_list(\"lists tuples strings\")==['lists','tuples','strings']\n    assert string_to_list(\"write a program\")==['write','a','program']\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 65, "prompt": "Write a function to flatten a list and sum all of its elements.\n\ndef recursive_list_sum(data_list):", "code": "def recursive_list_sum(data_list):\n\ttotal = 0\n\tfor element in data_list:\n\t\tif type(element) == type([]):\n\t\t\ttotal = total + recursive_list_sum(element)\n\t\telse:\n\t\t\ttotal = total + element\n\treturn total", "test_imports": [], "test_list": ["assert recursive_list_sum(([1, 2, [3,4],[5,6]]))==21", "assert recursive_list_sum(([7, 10, [15,14],[19,41]]))==106", "assert recursive_list_sum(([10, 20, [30,40],[50,60]]))==210"], "entry_point": "recursive_list_sum", "test": "def check():\n    assert recursive_list_sum(([1, 2, [3,4],[5,6]]))==21\n    assert recursive_list_sum(([7, 10, [15,14],[19,41]]))==106\n    assert recursive_list_sum(([10, 20, [30,40],[50,60]]))==210\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 83, "prompt": "Write a python function to find the character made by adding the ASCII value of all the characters of the given string modulo 26.\n\ndef get_Char(strr):  ", "code": "def get_Char(strr):  \n    summ = 0\n    for i in range(len(strr)): \n        summ += (ord(strr[i]) - ord('a') + 1)  \n    if (summ % 26 == 0): \n        return ord('z') \n    else: \n        summ = summ % 26\n        return chr(ord('a') + summ - 1)", "test_imports": [], "test_list": ["assert get_Char(\"abc\") == \"f\"", "assert get_Char(\"gfg\") == \"t\"", "assert get_Char(\"ab\") == \"c\""], "entry_point": "get_Char", "test": "def check():\n    assert get_Char(\"abc\") == \"f\"\n    assert get_Char(\"gfg\") == \"t\"\n    assert get_Char(\"ab\") == \"c\"\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 271, "prompt": "Write a python function that takes in an integer n and finds the sum of the first n even natural numbers that are raised to the fifth power.\n\ndef even_Power_Sum(n): ", "code": "def even_Power_Sum(n): \n    sum = 0; \n    for i in range(1,n+1): \n        j = 2*i; \n        sum = sum + (j*j*j*j*j); \n    return sum; ", "test_imports": [], "test_list": ["assert even_Power_Sum(2) == 1056", "assert even_Power_Sum(3) == 8832", "assert even_Power_Sum(1) == 32"], "entry_point": "even_Power_Sum", "test": "def check():\n    assert even_Power_Sum(2) == 1056\n    assert even_Power_Sum(3) == 8832\n    assert even_Power_Sum(1) == 32\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 246, "prompt": "Write a function for computing square roots using the babylonian method.\n\ndef babylonian_squareroot(number):", "code": "def babylonian_squareroot(number):\n    if(number == 0):\n        return 0;\n    g = number/2.0;\n    g2 = g + 1;\n    while(g != g2):\n        n = number/ g;\n        g2 = g;\n        g = (g + n)/2;\n    return g;", "test_imports": ["import math"], "test_list": ["assert math.isclose(babylonian_squareroot(10), 3.162277660168379, rel_tol=0.001)", "assert math.isclose(babylonian_squareroot(2), 1.414213562373095, rel_tol=0.001)", "assert math.isclose(babylonian_squareroot(9), 3.0, rel_tol=0.001)"], "entry_point": "babylonian_squareroot", "test": "def check():\n    assert math.isclose(babylonian_squareroot(10), 3.162277660168379, rel_tol=0.001)\n    assert math.isclose(babylonian_squareroot(2), 1.414213562373095, rel_tol=0.001)\n    assert math.isclose(babylonian_squareroot(9), 3.0, rel_tol=0.001)\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 443, "prompt": "Write a python function to find the largest negative number from the given list.\n\ndef largest_neg(list1): ", "code": "def largest_neg(list1): \n    max = list1[0] \n    for x in list1: \n        if x < max : \n             max = x  \n    return max", "test_imports": [], "test_list": ["assert largest_neg([1,2,3,-4,-6]) == -6", "assert largest_neg([1,2,3,-8,-9]) == -9", "assert largest_neg([1,2,3,4,-1]) == -1"], "entry_point": "largest_neg", "test": "def check():\n    assert largest_neg([1,2,3,-4,-6]) == -6\n    assert largest_neg([1,2,3,-8,-9]) == -9\n    assert largest_neg([1,2,3,4,-1]) == -1\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 412, "prompt": "Write a python function to remove odd numbers from a given list.\n\ndef remove_odd(l):", "code": "def remove_odd(l):\n    for i in l:\n        if i % 2 != 0:\n            l.remove(i)\n    return l", "test_imports": [], "test_list": ["assert remove_odd([1,2,3]) == [2]", "assert remove_odd([2,4,6]) == [2,4,6]", "assert remove_odd([10,20,3]) == [10,20]"], "entry_point": "remove_odd", "test": "def check():\n    assert remove_odd([1,2,3]) == [2]\n    assert remove_odd([2,4,6]) == [2,4,6]\n    assert remove_odd([10,20,3]) == [10,20]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 390, "prompt": "Write a function to apply a given format string to all of the elements in a list.\n\ndef add_string(list_, string):", "code": "def add_string(list_, string):\n add_string=[string.format(i) for i in  list_]\n return add_string", "test_imports": [], "test_list": ["assert add_string([1,2,3,4],'temp{0}')==['temp1', 'temp2', 'temp3', 'temp4']", "assert add_string(['a','b','c','d'], 'python{0}')==[ 'pythona', 'pythonb', 'pythonc', 'pythond']", "assert add_string([5,6,7,8],'string{0}')==['string5', 'string6', 'string7', 'string8']"], "entry_point": "add_string", "test": "def check():\n    assert add_string([1,2,3,4],'temp{0}')==['temp1', 'temp2', 'temp3', 'temp4']\n    assert add_string(['a','b','c','d'], 'python{0}')==[ 'pythona', 'pythonb', 'pythonc', 'pythond']\n    assert add_string([5,6,7,8],'string{0}')==['string5', 'string6', 'string7', 'string8']\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 117, "prompt": "Write a function to convert all possible convertible elements in a list of lists to floats.\n\ndef list_to_float(test_list):", "code": "def list_to_float(test_list):\n  res = []\n  for tup in test_list:\n    temp = []\n    for ele in tup:\n      if ele.isalpha():\n        temp.append(ele)\n      else:\n        temp.append(float(ele))\n    res.append((temp[0],temp[1])) \n  return res", "test_imports": [], "test_list": ["assert list_to_float( [(\"3\", \"4\"), (\"1\", \"26.45\"), (\"7.32\", \"8\"), (\"4\", \"8\")] ) == [(3.0, 4.0), (1.0, 26.45), (7.32, 8.0), (4.0, 8.0)]", "assert list_to_float( [(\"4\", \"4\"), (\"2\", \"27\"), (\"4.12\", \"9\"), (\"7\", \"11\")] ) == [(4.0, 4.0), (2.0, 27.0), (4.12, 9.0), (7.0, 11.0)]", "assert list_to_float( [(\"6\", \"78\"), (\"5\", \"26.45\"), (\"1.33\", \"4\"), (\"82\", \"13\")] ) == [(6.0, 78.0), (5.0, 26.45), (1.33, 4.0), (82.0, 13.0)]"], "entry_point": "list_to_float", "test": "def check():\n    assert list_to_float( [(\"3\", \"4\"), (\"1\", \"26.45\"), (\"7.32\", \"8\"), (\"4\", \"8\")] ) == [(3.0, 4.0), (1.0, 26.45), (7.32, 8.0), (4.0, 8.0)]\n    assert list_to_float( [(\"4\", \"4\"), (\"2\", \"27\"), (\"4.12\", \"9\"), (\"7\", \"11\")] ) == [(4.0, 4.0), (2.0, 27.0), (4.12, 9.0), (7.0, 11.0)]\n    assert list_to_float( [(\"6\", \"78\"), (\"5\", \"26.45\"), (\"1.33\", \"4\"), (\"82\", \"13\")] ) == [(6.0, 78.0), (5.0, 26.45), (1.33, 4.0), (82.0, 13.0)]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 640, "prompt": "Write a function to remove the parenthesis and what is inbetween them from a string.\nimport re\ndef remove_parenthesis(items):", "code": "import re\ndef remove_parenthesis(items):\n for item in items:\n    return (re.sub(r\" ?\\([^)]+\\)\", \"\", item))", "test_imports": [], "test_list": ["assert remove_parenthesis([\"python (chrome)\"])==(\"python\")", "assert remove_parenthesis([\"string(.abc)\"])==(\"string\")", "assert remove_parenthesis([\"alpha(num)\"])==(\"alpha\")"], "entry_point": "remove_parenthesis", "test": "def check():\n    assert remove_parenthesis([\"python (chrome)\"])==(\"python\")\n    assert remove_parenthesis([\"string(.abc)\"])==(\"string\")\n    assert remove_parenthesis([\"alpha(num)\"])==(\"alpha\")\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 735, "prompt": "Write a python function to toggle bits of the number except the first and the last bit. https://www.geeksforgeeks.org/toggle-bits-number-expect-first-last-bits/\n\ndef toggle_middle_bits(n):  ", "code": "def set_middle_bits(n):  \n    n |= n >> 1; \n    n |= n >> 2; \n    n |= n >> 4; \n    n |= n >> 8; \n    n |= n >> 16;  \n    return (n >> 1) ^ 1\ndef toggle_middle_bits(n): \n    if (n == 1): \n        return 1\n    return n ^ set_middle_bits(n) ", "test_imports": [], "test_list": ["assert toggle_middle_bits(9) == 15", "assert toggle_middle_bits(10) == 12", "assert toggle_middle_bits(11) == 13", "assert toggle_middle_bits(0b1000001) == 0b1111111", "assert toggle_middle_bits(0b1001101) == 0b1110011"], "entry_point": "toggle_middle_bits", "test": "def check():\n    assert toggle_middle_bits(9) == 15\n    assert toggle_middle_bits(10) == 12\n    assert toggle_middle_bits(11) == 13\n    assert toggle_middle_bits(0b1000001) == 0b1111111\n    assert toggle_middle_bits(0b1001101) == 0b1110011\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 461, "prompt": "Write a python function to count the upper case characters in a given string.\n\ndef upper_ctr(str):", "code": "def upper_ctr(str):\n    upper_ctr = 0\n    for i in range(len(str)):\n          if str[i] >= 'A' and str[i] <= 'Z': upper_ctr += 1\n          return upper_ctr", "test_imports": [], "test_list": ["assert upper_ctr('PYthon') == 1", "assert upper_ctr('BigData') == 1", "assert upper_ctr('program') == 0"], "entry_point": "upper_ctr", "test": "def check():\n    assert upper_ctr('PYthon') == 1\n    assert upper_ctr('BigData') == 1\n    assert upper_ctr('program') == 0\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 468, "prompt": "Write a function to find the maximum product formed by multiplying numbers of an increasing subsequence of that array.\n\ndef max_product(arr):   ", "code": "def max_product(arr):   \n  n = len(arr)\n  mpis = arr[:]\n  for i in range(n): \n    current_prod = arr[i]\n    j = i + 1\n    while j < n:\n      if arr[j-1] > arr[j]: \n        break\n      current_prod *= arr[j]\n      if current_prod > mpis[j]:\n        mpis[j] = current_prod \n      j = j + 1\n  return max(mpis)", "test_imports": [], "test_list": ["assert max_product([3, 100, 4, 5, 150, 6]) == 3000", "assert max_product([4, 42, 55, 68, 80]) == 50265600", "assert max_product([10, 22, 9, 33, 21, 50, 41, 60]) == 2460"], "entry_point": "max_product", "test": "def check():\n    assert max_product([3, 100, 4, 5, 150, 6]) == 3000\n    assert max_product([4, 42, 55, 68, 80]) == 50265600\n    assert max_product([10, 22, 9, 33, 21, 50, 41, 60]) == 2460\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 747, "prompt": "Write a function to find the longest common subsequence for the given three string sequence. https://www.geeksforgeeks.org/lcs-longest-common-subsequence-three-strings/\n\ndef lcs_of_three(X, Y, Z): ", "code": "def lcs_of_three(X, Y, Z): \n  m = len(X)\n  n = len(Y)\n  o = len(Z)\n  L = [[[0 for i in range(o+1)] for j in range(n+1)] for k in range(m+1)]\n  for i in range(m+1): \n\t  for j in range(n+1): \n\t\t  for k in range(o+1): \n\t\t\t  if (i == 0 or j == 0 or k == 0): \n\t\t\t\t  L[i][j][k] = 0\n\t\t\t  elif (X[i-1] == Y[j-1] and X[i-1] == Z[k-1]): \n\t\t\t\t  L[i][j][k] = L[i-1][j-1][k-1] + 1\n\t\t\t  else: \n\t\t\t\t  L[i][j][k] = max(max(L[i-1][j][k], L[i][j-1][k]), L[i][j][k-1]) \n  return L[m][n][o]", "test_imports": [], "test_list": ["assert lcs_of_three('AGGT12', '12TXAYB', '12XBA') == 2", "assert lcs_of_three('Reels', 'Reelsfor', 'ReelsforReels') == 5", "assert lcs_of_three('abcd1e2', 'bc12ea', 'bd1ea') == 3"], "entry_point": "lcs_of_three", "test": "def check():\n    assert lcs_of_three('AGGT12', '12TXAYB', '12XBA') == 2\n    assert lcs_of_three('Reels', 'Reelsfor', 'ReelsforReels') == 5\n    assert lcs_of_three('abcd1e2', 'bc12ea', 'bd1ea') == 3\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 598, "prompt": "Write a function to check whether the given number is armstrong or not.\n\ndef armstrong_number(number):", "code": "def armstrong_number(number):\n sum = 0\n times = 0\n temp = number\n while temp > 0:\n           times = times + 1\n           temp = temp // 10\n temp = number\n while temp > 0:\n           reminder = temp % 10\n           sum = sum + (reminder ** times)\n           temp //= 10\n if number == sum:\n           return True\n else:\n           return False", "test_imports": [], "test_list": ["assert armstrong_number(153)==True", "assert armstrong_number(259)==False", "assert armstrong_number(4458)==False"], "entry_point": "armstrong_number", "test": "def check():\n    assert armstrong_number(153)==True\n    assert armstrong_number(259)==False\n    assert armstrong_number(4458)==False\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 249, "prompt": "Write a function to find the intersection of two arrays.\n\ndef intersection_array(array_nums1,array_nums2):", "code": "def intersection_array(array_nums1,array_nums2):\n result = list(filter(lambda x: x in array_nums1, array_nums2)) \n return result", "test_imports": [], "test_list": ["assert intersection_array([1, 2, 3, 5, 7, 8, 9, 10],[1, 2, 4, 8, 9])==[1, 2, 8, 9]", "assert intersection_array([1, 2, 3, 5, 7, 8, 9, 10],[3,5,7,9])==[3,5,7,9]", "assert intersection_array([1, 2, 3, 5, 7, 8, 9, 10],[10,20,30,40])==[10]"], "entry_point": "intersection_array", "test": "def check():\n    assert intersection_array([1, 2, 3, 5, 7, 8, 9, 10],[1, 2, 4, 8, 9])==[1, 2, 8, 9]\n    assert intersection_array([1, 2, 3, 5, 7, 8, 9, 10],[3,5,7,9])==[3,5,7,9]\n    assert intersection_array([1, 2, 3, 5, 7, 8, 9, 10],[10,20,30,40])==[10]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 273, "prompt": "Write a function that takes in two tuples and subtracts the elements of the first tuple by the elements of the second tuple with the same index.\n\ndef substract_elements(test_tup1, test_tup2):", "code": "def substract_elements(test_tup1, test_tup2):\n  res = tuple(map(lambda i, j: i - j, test_tup1, test_tup2))\n  return (res) ", "test_imports": [], "test_list": ["assert substract_elements((10, 4, 5), (2, 5, 18)) == (8, -1, -13)", "assert substract_elements((11, 2, 3), (24, 45 ,16)) == (-13, -43, -13)", "assert substract_elements((7, 18, 9), (10, 11, 12)) == (-3, 7, -3)"], "entry_point": "substract_elements", "test": "def check():\n    assert substract_elements((10, 4, 5), (2, 5, 18)) == (8, -1, -13)\n    assert substract_elements((11, 2, 3), (24, 45 ,16)) == (-13, -43, -13)\n    assert substract_elements((7, 18, 9), (10, 11, 12)) == (-3, 7, -3)\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 625, "prompt": "Write a python function to interchange the first and last element in a given list.\n\ndef swap_List(newList): ", "code": "def swap_List(newList): \n    size = len(newList) \n    temp = newList[0] \n    newList[0] = newList[size - 1] \n    newList[size - 1] = temp   \n    return newList ", "test_imports": [], "test_list": ["assert swap_List([1,2,3]) == [3,2,1]", "assert swap_List([1,2,3,4,4]) == [4,2,3,4,1]", "assert swap_List([4,5,6]) == [6,5,4]"], "entry_point": "swap_List", "test": "def check():\n    assert swap_List([1,2,3]) == [3,2,1]\n    assert swap_List([1,2,3,4,4]) == [4,2,3,4,1]\n    assert swap_List([4,5,6]) == [6,5,4]\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 140, "prompt": "Write a function to flatten the list of lists into a single set of numbers.\n\ndef extract_singly(test_list):", "code": "def extract_singly(test_list):\n  res = []\n  temp = set()\n  for inner in test_list:\n    for ele in inner:\n      if not ele in temp:\n        temp.add(ele)\n        res.append(ele)\n  return (res) ", "test_imports": [], "test_list": ["assert set(extract_singly([(3, 4, 5), (4, 5, 7), (1, 4)])) == set([3, 4, 5, 7, 1])", "assert set(extract_singly([(1, 2, 3), (4, 2, 3), (7, 8)])) == set([1, 2, 3, 4, 7, 8])", "assert set(extract_singly([(7, 8, 9), (10, 11, 12), (10, 11)])) == set([7, 8, 9, 10, 11, 12])"], "entry_point": "extract_singly", "test": "def check():\n    assert set(extract_singly([(3, 4, 5), (4, 5, 7), (1, 4)])) == set([3, 4, 5, 7, 1])\n    assert set(extract_singly([(1, 2, 3), (4, 2, 3), (7, 8)])) == set([1, 2, 3, 4, 7, 8])\n    assert set(extract_singly([(7, 8, 9), (10, 11, 12), (10, 11)])) == set([7, 8, 9, 10, 11, 12])\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 739, "prompt": "Write a python function to find the index of smallest triangular number with n digits. https://www.geeksforgeeks.org/index-of-smallest-triangular-number-with-n-digits/\nimport math \ndef find_Index(n): ", "code": "import math \ndef find_Index(n): \n    x = math.sqrt(2 * math.pow(10,(n - 1)))\n    return round(x)", "test_imports": [], "test_list": ["assert find_Index(2) == 4", "assert find_Index(3) == 14", "assert find_Index(4) == 45"], "entry_point": "find_Index", "test": "def check():\n    assert find_Index(2) == 4\n    assert find_Index(3) == 14\n    assert find_Index(4) == 45\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 431, "prompt": "Write a function that takes two lists and returns true if they have at least one common element.\n\ndef common_element(list1, list2):", "code": "def common_element(list1, list2):\n     result = False\n     for x in list1:\n         for y in list2:\n             if x == y:\n                 result = True\n                 return result", "test_imports": [], "test_list": ["assert common_element([1,2,3,4,5], [5,6,7,8,9])==True", "assert common_element([1,2,3,4,5], [6,7,8,9])==None", "assert common_element(['a','b','c'], ['d','b','e'])==True"], "entry_point": "common_element", "test": "def check():\n    assert common_element([1,2,3,4,5], [5,6,7,8,9])==True\n    assert common_element([1,2,3,4,5], [6,7,8,9])==None\n    assert common_element(['a','b','c'], ['d','b','e'])==True\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 579, "prompt": "Write a function to find the dissimilar elements in the given two tuples.\n\ndef find_dissimilar(test_tup1, test_tup2):", "code": "def find_dissimilar(test_tup1, test_tup2):\n  res = tuple(set(test_tup1) ^ set(test_tup2))\n  return (res) ", "test_imports": [], "test_list": ["assert find_dissimilar((3, 4, 5, 6), (5, 7, 4, 10)) == (3, 6, 7, 10)", "assert find_dissimilar((1, 2, 3, 4), (7, 2, 3, 9)) == (1, 4, 7, 9)", "assert find_dissimilar((21, 11, 25, 26), (26, 34, 21, 36)) == (34, 36, 11, 25)"], "entry_point": "find_dissimilar", "test": "def check():\n    assert find_dissimilar((3, 4, 5, 6), (5, 7, 4, 10)) == (3, 6, 7, 10)\n    assert find_dissimilar((1, 2, 3, 4), (7, 2, 3, 9)) == (1, 4, 7, 9)\n    assert find_dissimilar((21, 11, 25, 26), (26, 34, 21, 36)) == (34, 36, 11, 25)\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 738, "prompt": "Write a function to calculate the geometric sum of n-1. https://www.w3resource.com/python-exercises/data-structures-and-algorithms/python-recursion-exercise-9.php\n\ndef geometric_sum(n):", "code": "def geometric_sum(n):\n  if n < 0:\n    return 0\n  else:\n    return 1 / (pow(2, n)) + geometric_sum(n - 1)", "test_imports": [], "test_list": ["assert geometric_sum(7) == 1.9921875", "assert geometric_sum(4) == 1.9375", "assert geometric_sum(8) == 1.99609375"], "entry_point": "geometric_sum", "test": "def check():\n    assert geometric_sum(7) == 1.9921875\n    assert geometric_sum(4) == 1.9375\n    assert geometric_sum(8) == 1.99609375\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 458, "prompt": "Write a function to find the area of a rectangle.\n\ndef rectangle_area(l,b):", "code": "def rectangle_area(l,b):\n  area=l*b\n  return area", "test_imports": [], "test_list": ["assert rectangle_area(10,20)==200", "assert rectangle_area(10,5)==50", "assert rectangle_area(4,2)==8"], "entry_point": "rectangle_area", "test": "def check():\n    assert rectangle_area(10,20)==200\n    assert rectangle_area(10,5)==50\n    assert rectangle_area(4,2)==8\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 569, "prompt": "Write a function to sort each sublist of strings in a given list of lists.\n\ndef sort_sublists(list1):", "code": "def sort_sublists(list1):\n    result = list(map(sorted,list1)) \n    return result", "test_imports": [], "test_list": ["assert sort_sublists([['green', 'orange'], ['black', 'white'], ['white', 'black', 'orange']])==[['green', 'orange'], ['black', 'white'], ['black', 'orange', 'white']]", "assert sort_sublists([['green', 'orange'], ['black'], ['green', 'orange'], ['white']])==[['green', 'orange'], ['black'], ['green', 'orange'], ['white']]", "assert sort_sublists([['a','b'],['d','c'],['g','h'] , ['f','e']])==[['a', 'b'], ['c', 'd'], ['g', 'h'], ['e', 'f']]"], "entry_point": "sort_sublists", "test": "def check():\n    assert sort_sublists([['green', 'orange'], ['black', 'white'], ['white', 'black', 'orange']])==[['green', 'orange'], ['black', 'white'], ['black', 'orange', 'white']]\n    assert sort_sublists([['green', 'orange'], ['black'], ['green', 'orange'], ['white']])==[['green', 'orange'], ['black'], ['green', 'orange'], ['white']]\n    assert sort_sublists([['a','b'],['d','c'],['g','h'] , ['f','e']])==[['a', 'b'], ['c', 'd'], ['g', 'h'], ['e', 'f']]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 620, "prompt": "Write a function to find the size of the largest subset of a list of numbers so that every pair is divisible.\n\ndef largest_subset(a):", "code": "def largest_subset(a):\n\tn = len(a)\n\tdp = [0 for i in range(n)]\n\tdp[n - 1] = 1; \n\tfor i in range(n - 2, -1, -1):\n\t\tmxm = 0;\n\t\tfor j in range(i + 1, n):\n\t\t\tif a[j] % a[i] == 0 or a[i] % a[j] == 0:\n\t\t\t\tmxm = max(mxm, dp[j])\n\t\tdp[i] = 1 + mxm\n\treturn max(dp)", "test_imports": [], "test_list": ["assert largest_subset([ 1, 3, 6, 13, 17, 18 ]) == 4", "assert largest_subset([10, 5, 3, 15, 20]) == 3", "assert largest_subset([18, 1, 3, 6, 13, 17]) == 4"], "entry_point": "largest_subset", "test": "def check():\n    assert largest_subset([ 1, 3, 6, 13, 17, 18 ]) == 4\n    assert largest_subset([10, 5, 3, 15, 20]) == 3\n    assert largest_subset([18, 1, 3, 6, 13, 17]) == 4\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 391, "prompt": "Write a function to convert more than one list to nested dictionary.\n\ndef convert_list_dictionary(l1, l2, l3):", "code": "def convert_list_dictionary(l1, l2, l3):\n     result = [{x: {y: z}} for (x, y, z) in zip(l1, l2, l3)]\n     return result", "test_imports": [], "test_list": ["assert convert_list_dictionary([\"S001\", \"S002\", \"S003\", \"S004\"],[\"Adina Park\", \"Leyton Marsh\", \"Duncan Boyle\", \"Saim Richards\"] ,[85, 98, 89, 92])==[{'S001': {'Adina Park': 85}}, {'S002': {'Leyton Marsh': 98}}, {'S003': {'Duncan Boyle': 89}}, {'S004': {'Saim Richards': 92}}]", "assert convert_list_dictionary([\"abc\",\"def\",\"ghi\",\"jkl\"],[\"python\",\"program\",\"language\",\"programs\"],[100,200,300,400])==[{'abc':{'python':100}},{'def':{'program':200}},{'ghi':{'language':300}},{'jkl':{'programs':400}}]", "assert convert_list_dictionary([\"A1\",\"A2\",\"A3\",\"A4\"],[\"java\",\"C\",\"C++\",\"DBMS\"],[10,20,30,40])==[{'A1':{'java':10}},{'A2':{'C':20}},{'A3':{'C++':30}},{'A4':{'DBMS':40}}]"], "entry_point": "convert_list_dictionary", "test": "def check():\n    assert convert_list_dictionary([\"S001\", \"S002\", \"S003\", \"S004\"],[\"Adina Park\", \"Leyton Marsh\", \"Duncan Boyle\", \"Saim Richards\"] ,[85, 98, 89, 92])==[{'S001': {'Adina Park': 85}}, {'S002': {'Leyton Marsh': 98}}, {'S003': {'Duncan Boyle': 89}}, {'S004': {'Saim Richards': 92}}]\n    assert convert_list_dictionary([\"abc\",\"def\",\"ghi\",\"jkl\"],[\"python\",\"program\",\"language\",\"programs\"],[100,200,300,400])==[{'abc':{'python':100}},{'def':{'program':200}},{'ghi':{'language':300}},{'jkl':{'programs':400}}]\n    assert convert_list_dictionary([\"A1\",\"A2\",\"A3\",\"A4\"],[\"java\",\"C\",\"C++\",\"DBMS\"],[10,20,30,40])==[{'A1':{'java':10}},{'A2':{'C':20}},{'A3':{'C++':30}},{'A4':{'DBMS':40}}]\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 446, "prompt": "Write a python function to count the occurence of all elements of list in a tuple.\nfrom collections import Counter \ndef count_Occurrence(tup, lst): ", "code": "from collections import Counter \ndef count_Occurrence(tup, lst): \n    count = 0\n    for item in tup: \n        if item in lst: \n            count+= 1 \n    return count  ", "test_imports": [], "test_list": ["assert count_Occurrence(('a', 'a', 'c', 'b', 'd'),['a', 'b'] ) == 3", "assert count_Occurrence((1, 2, 3, 1, 4, 6, 7, 1, 4),[1, 4, 7]) == 6", "assert count_Occurrence((1,2,3,4,5,6),[1,2]) == 2"], "entry_point": "count_Occurrence", "test": "def check():\n    assert count_Occurrence(('a', 'a', 'c', 'b', 'd'),['a', 'b'] ) == 3\n    assert count_Occurrence((1, 2, 3, 1, 4, 6, 7, 1, 4),[1, 4, 7]) == 6\n    assert count_Occurrence((1,2,3,4,5,6),[1,2]) == 2\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 725, "prompt": "Write a function to extract values between quotation marks \" \" of the given string.\nimport re\ndef extract_quotation(text1):", "code": "import re\ndef extract_quotation(text1):\n  return (re.findall(r'\"(.*?)\"', text1))", "test_imports": [], "test_list": ["assert extract_quotation('Cortex \"A53\" Based \"multi\" tasking \"Processor\"') == ['A53', 'multi', 'Processor']", "assert extract_quotation('Cast your \"favorite\" entertainment \"apps\"') == ['favorite', 'apps']", "assert extract_quotation('Watch content \"4k Ultra HD\" resolution with \"HDR 10\" Support') == ['4k Ultra HD', 'HDR 10']", "assert extract_quotation(\"Watch content '4k Ultra HD' resolution with 'HDR 10' Support\") == []"], "entry_point": "extract_quotation", "test": "def check():\n    assert extract_quotation('Cortex \"A53\" Based \"multi\" tasking \"Processor\"') == ['A53', 'multi', 'Processor']\n    assert extract_quotation('Cast your \"favorite\" entertainment \"apps\"') == ['favorite', 'apps']\n    assert extract_quotation('Watch content \"4k Ultra HD\" resolution with \"HDR 10\" Support') == ['4k Ultra HD', 'HDR 10']\n    assert extract_quotation(\"Watch content '4k Ultra HD' resolution with 'HDR 10' Support\") == []\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 785, "prompt": "Write a function to convert tuple string to integer tuple.\n\ndef tuple_str_int(test_str):", "code": "def tuple_str_int(test_str):\n  res = tuple(int(num) for num in test_str.replace('(', '').replace(')', '').replace('...', '').split(', '))\n  return (res) ", "test_imports": [], "test_list": ["assert tuple_str_int(\"(7, 8, 9)\") == (7, 8, 9)", "assert tuple_str_int(\"(1, 2, 3)\") == (1, 2, 3)", "assert tuple_str_int(\"(4, 5, 6)\") == (4, 5, 6)", "assert tuple_str_int(\"(7, 81, 19)\") == (7, 81, 19)"], "entry_point": "tuple_str_int", "test": "def check():\n    assert tuple_str_int(\"(7, 8, 9)\") == (7, 8, 9)\n    assert tuple_str_int(\"(1, 2, 3)\") == (1, 2, 3)\n    assert tuple_str_int(\"(4, 5, 6)\") == (4, 5, 6)\n    assert tuple_str_int(\"(7, 81, 19)\") == (7, 81, 19)\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 596, "prompt": "Write a function to find the size in bytes of the given tuple.\nimport sys \ndef tuple_size(tuple_list):", "code": "import sys \ndef tuple_size(tuple_list):\n  return (sys.getsizeof(tuple_list)) ", "test_imports": [], "test_list": ["assert tuple_size((\"A\", 1, \"B\", 2, \"C\", 3) ) == sys.getsizeof((\"A\", 1, \"B\", 2, \"C\", 3))", "assert tuple_size((1, \"Raju\", 2, \"Nikhil\", 3, \"Deepanshu\") ) == sys.getsizeof((1, \"Raju\", 2, \"Nikhil\", 3, \"Deepanshu\"))", "assert tuple_size(((1, \"Lion\"), ( 2, \"Tiger\"), (3, \"Fox\"), (4, \"Wolf\"))  ) == sys.getsizeof(((1, \"Lion\"), ( 2, \"Tiger\"), (3, \"Fox\"), (4, \"Wolf\")))"], "entry_point": "tuple_size", "test": "def check():\n    assert tuple_size((\"A\", 1, \"B\", 2, \"C\", 3) ) == sys.getsizeof((\"A\", 1, \"B\", 2, \"C\", 3))\n    assert tuple_size((1, \"Raju\", 2, \"Nikhil\", 3, \"Deepanshu\") ) == sys.getsizeof((1, \"Raju\", 2, \"Nikhil\", 3, \"Deepanshu\"))\n    assert tuple_size(((1, \"Lion\"), ( 2, \"Tiger\"), (3, \"Fox\"), (4, \"Wolf\"))  ) == sys.getsizeof(((1, \"Lion\"), ( 2, \"Tiger\"), (3, \"Fox\"), (4, \"Wolf\")))\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 253, "prompt": "Write a python function that returns the number of integer elements in a given list.\n\ndef count_integer(list1):", "code": "def count_integer(list1):\n    ctr = 0\n    for i in list1:\n        if isinstance(i, int):\n            ctr = ctr + 1\n    return ctr", "test_imports": [], "test_list": ["assert count_integer([1,2,'abc',1.2]) == 2", "assert count_integer([1,2,3]) == 3", "assert count_integer([1,1.2,4,5.1]) == 2"], "entry_point": "count_integer", "test": "def check():\n    assert count_integer([1,2,'abc',1.2]) == 2\n    assert count_integer([1,2,3]) == 3\n    assert count_integer([1,1.2,4,5.1]) == 2\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 84, "prompt": "Write a function to find the nth number in the newman conway sequence.\n\ndef sequence(n): ", "code": "def sequence(n): \n\tif n == 1 or n == 2: \n\t\treturn 1\n\telse: \n\t\treturn sequence(sequence(n-1)) + sequence(n-sequence(n-1))", "test_imports": [], "test_list": ["assert sequence(10) == 6", "assert sequence(2) == 1", "assert sequence(3) == 2"], "entry_point": "sequence", "test": "def check():\n    assert sequence(10) == 6\n    assert sequence(2) == 1\n    assert sequence(3) == 2\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 17, "prompt": "Write a function that returns the perimeter of a square given its side length as input.\n\ndef square_perimeter(a):", "code": "def square_perimeter(a):\n  perimeter=4*a\n  return perimeter", "test_imports": [], "test_list": ["assert square_perimeter(10)==40", "assert square_perimeter(5)==20", "assert square_perimeter(4)==16"], "entry_point": "square_perimeter", "test": "def check():\n    assert square_perimeter(10)==40\n    assert square_perimeter(5)==20\n    assert square_perimeter(4)==16\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 583, "prompt": "Write a function which returns nth catalan number.\n\ndef catalan_number(num):", "code": "def catalan_number(num):\n    if num <=1:\n         return 1   \n    res_num = 0\n    for i in range(num):\n        res_num += catalan_number(i) * catalan_number(num-i-1)\n    return res_num", "test_imports": [], "test_list": ["assert catalan_number(10)==16796", "assert catalan_number(9)==4862", "assert catalan_number(7)==429"], "entry_point": "catalan_number", "test": "def check():\n    assert catalan_number(10)==16796\n    assert catalan_number(9)==4862\n    assert catalan_number(7)==429\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 393, "prompt": "Write a function to find the list with maximum length.\n\ndef max_length_list(input_list):", "code": "def max_length_list(input_list):\n    max_length = max(len(x) for x in input_list )   \n    max_list = max(input_list, key = lambda i: len(i))    \n    return(max_length, max_list)", "test_imports": [], "test_list": ["assert max_length_list([[0], [1, 3], [5, 7], [9, 11], [13, 15, 17]])==(3, [13, 15, 17])", "assert max_length_list([[1,2,3,4,5],[1,2,3,4],[1,2,3],[1,2],[1]])==(5,[1,2,3,4,5])", "assert max_length_list([[3,4,5],[6,7,8,9],[10,11,12]])==(4,[6,7,8,9])"], "entry_point": "max_length_list", "test": "def check():\n    assert max_length_list([[0], [1, 3], [5, 7], [9, 11], [13, 15, 17]])==(3, [13, 15, 17])\n    assert max_length_list([[1,2,3,4,5],[1,2,3,4],[1,2,3],[1,2],[1]])==(5,[1,2,3,4,5])\n    assert max_length_list([[3,4,5],[6,7,8,9],[10,11,12]])==(4,[6,7,8,9])\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 584, "prompt": "Write a function to find the first adverb ending with ly and its positions in a given string.\nimport re\ndef find_adverbs(text):", "code": "import re\ndef find_adverbs(text):\n  for m in re.finditer(r\"\\w+ly\", text):\n    return ('%d-%d: %s' % (m.start(), m.end(), m.group(0)))", "test_imports": [], "test_list": ["assert find_adverbs(\"Clearly, he has no excuse for such behavior.\") == '0-7: Clearly'", "assert find_adverbs(\"Please handle the situation carefuly\") == '28-36: carefuly'", "assert find_adverbs(\"Complete the task quickly\") == '18-25: quickly'"], "entry_point": "find_adverbs", "test": "def check():\n    assert find_adverbs(\"Clearly, he has no excuse for such behavior.\") == '0-7: Clearly'\n    assert find_adverbs(\"Please handle the situation carefuly\") == '28-36: carefuly'\n    assert find_adverbs(\"Complete the task quickly\") == '18-25: quickly'\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 563, "prompt": "Write a function to extract values between quotation marks from a string.\nimport re\ndef extract_values(text):", "code": "import re\ndef extract_values(text):\n return (re.findall(r'\"(.*?)\"', text))", "test_imports": [], "test_list": ["assert extract_values('\"Python\", \"PHP\", \"Java\"')==['Python', 'PHP', 'Java']", "assert extract_values('\"python\",\"program\",\"language\"')==['python','program','language']", "assert extract_values('\"red\",\"blue\",\"green\",\"yellow\"')==['red','blue','green','yellow']"], "entry_point": "extract_values", "test": "def check():\n    assert extract_values('\"Python\", \"PHP\", \"Java\"')==['Python', 'PHP', 'Java']\n    assert extract_values('\"python\",\"program\",\"language\"')==['python','program','language']\n    assert extract_values('\"red\",\"blue\",\"green\",\"yellow\"')==['red','blue','green','yellow']\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 233, "prompt": "Write a function to find the lateral surface area of a cylinder.\n\ndef lateralsuface_cylinder(r,h):", "code": "def lateralsuface_cylinder(r,h):\n  lateralsurface= 2*3.1415*r*h\n  return lateralsurface", "test_imports": ["import math"], "test_list": ["assert math.isclose(lateralsuface_cylinder(10,5), 314.15000000000003, rel_tol=0.001)", "assert math.isclose(lateralsuface_cylinder(4,5), 125.66000000000001, rel_tol=0.001)", "assert math.isclose(lateralsuface_cylinder(4,10), 251.32000000000002, rel_tol=0.001)"], "entry_point": "lateralsuface_cylinder", "test": "def check():\n    assert math.isclose(lateralsuface_cylinder(10,5), 314.15000000000003, rel_tol=0.001)\n    assert math.isclose(lateralsuface_cylinder(4,5), 125.66000000000001, rel_tol=0.001)\n    assert math.isclose(lateralsuface_cylinder(4,10), 251.32000000000002, rel_tol=0.001)\n"}
{"source_file": "Ellen's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 312, "prompt": "Write a function to find the volume of a cone.\nimport math\ndef volume_cone(r,h):", "code": "import math\ndef volume_cone(r,h):\n  volume = (1.0/3) * math.pi * r * r * h\n  return volume", "test_imports": ["import math"], "test_list": ["assert math.isclose(volume_cone(5,12), 314.15926535897927, rel_tol=0.001)", "assert math.isclose(volume_cone(10,15), 1570.7963267948965, rel_tol=0.001)", "assert math.isclose(volume_cone(19,17), 6426.651371693521, rel_tol=0.001)"], "entry_point": "volume_cone", "test": "def check():\n    assert math.isclose(volume_cone(5,12), 314.15926535897927, rel_tol=0.001)\n    assert math.isclose(volume_cone(10,15), 1570.7963267948965, rel_tol=0.001)\n    assert math.isclose(volume_cone(19,17), 6426.651371693521, rel_tol=0.001)\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 477, "prompt": "Write a python function to convert the given string to lower case.\n\ndef is_lower(string):", "code": "def is_lower(string):\n  return (string.lower())", "test_imports": [], "test_list": ["assert is_lower(\"InValid\") == \"invalid\"", "assert is_lower(\"TruE\") == \"true\"", "assert is_lower(\"SenTenCE\") == \"sentence\""], "entry_point": "is_lower", "test": "def check():\n    assert is_lower(\"InValid\") == \"invalid\"\n    assert is_lower(\"TruE\") == \"true\"\n    assert is_lower(\"SenTenCE\") == \"sentence\"\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 605, "prompt": "Write a function to check if the given integer is a prime number.\n\ndef prime_num(num):", "code": "def prime_num(num):\n  if num >=1:\n   for i in range(2, num//2):\n     if (num % i) == 0:\n                return False\n     else:\n                return True\n  else:\n          return False", "test_imports": [], "test_list": ["assert prime_num(13)==True", "assert prime_num(7)==True", "assert prime_num(-1010)==False"], "entry_point": "prime_num", "test": "def check():\n    assert prime_num(13)==True\n    assert prime_num(7)==True\n    assert prime_num(-1010)==False\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 618, "prompt": "Write a function to divide two lists element wise.\n\ndef div_list(nums1,nums2):", "code": "def div_list(nums1,nums2):\n  result = map(lambda x, y: x / y, nums1, nums2)\n  return list(result)", "test_imports": [], "test_list": ["assert div_list([4,5,6],[1, 2, 3])==[4.0,2.5,2.0]", "assert div_list([3,2],[1,4])==[3.0, 0.5]", "assert div_list([90,120],[50,70])==[1.8, 1.7142857142857142]"], "entry_point": "div_list", "test": "def check():\n    assert div_list([4,5,6],[1, 2, 3])==[4.0,2.5,2.0]\n    assert div_list([3,2],[1,4])==[3.0, 0.5]\n    assert div_list([90,120],[50,70])==[1.8, 1.7142857142857142]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 638, "prompt": "Write a function to calculate the wind chill index rounded to the next integer given the wind velocity in km/h and a temperature in celsius.\nimport math\ndef wind_chill(v,t):", "code": "import math\ndef wind_chill(v,t):\n windchill = 13.12 + 0.6215*t -  11.37*math.pow(v, 0.16) + 0.3965*t*math.pow(v, 0.16)\n return int(round(windchill, 0))", "test_imports": [], "test_list": ["assert wind_chill(120,35)==40", "assert wind_chill(40,20)==19", "assert wind_chill(10,8)==6"], "entry_point": "wind_chill", "test": "def check():\n    assert wind_chill(120,35)==40\n    assert wind_chill(40,20)==19\n    assert wind_chill(10,8)==6\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 239, "prompt": "Write a function that takes in positive integers m and n and finds the number of possible sequences of length n, such that each element is a positive integer and is greater than or equal to twice the previous element but less than or equal to m.\n\ndef get_total_number_of_sequences(m,n): ", "code": "def get_total_number_of_sequences(m,n): \n\tT=[[0 for i in range(n+1)] for i in range(m+1)] \n\tfor i in range(m+1): \n\t\tfor j in range(n+1): \n\t\t\tif i==0 or j==0: \n\t\t\t\tT[i][j]=0\n\t\t\telif i<j: \n\t\t\t\tT[i][j]=0\n\t\t\telif j==1: \n\t\t\t\tT[i][j]=i \n\t\t\telse: \n\t\t\t\tT[i][j]=T[i-1][j]+T[i//2][j-1] \n\treturn T[m][n]", "test_imports": [], "test_list": ["assert get_total_number_of_sequences(10, 4) == 4", "assert get_total_number_of_sequences(5, 2) == 6", "assert get_total_number_of_sequences(16, 3) == 84"], "entry_point": "get_total_number_of_sequences", "test": "def check():\n    assert get_total_number_of_sequences(10, 4) == 4\n    assert get_total_number_of_sequences(5, 2) == 6\n    assert get_total_number_of_sequences(16, 3) == 84\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 775, "prompt": "Write a python function to check whether every odd index contains odd numbers of a given list.\n\ndef odd_position(nums):", "code": "def odd_position(nums):\n\treturn all(nums[i]%2==i%2 for i in range(len(nums)))", "test_imports": [], "test_list": ["assert odd_position([2,1,4,3,6,7,6,3]) == True", "assert odd_position([4,1,2]) == True", "assert odd_position([1,2,3]) == False"], "entry_point": "odd_position", "test": "def check():\n    assert odd_position([2,1,4,3,6,7,6,3]) == True\n    assert odd_position([4,1,2]) == True\n    assert odd_position([1,2,3]) == False\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 590, "prompt": "Write a function to convert polar coordinates to rectangular coordinates.\nimport cmath\ndef polar_rect(x,y):", "code": "import cmath\ndef polar_rect(x,y):\n cn = complex(x,y)\n cn=cmath.polar(cn)\n cn1 = cmath.rect(2, cmath.pi)\n return (cn,cn1)", "test_imports": [], "test_list": ["assert polar_rect(3,4)==((5.0, 0.9272952180016122), (-2+2.4492935982947064e-16j))", "assert polar_rect(4,7)==((8.06225774829855, 1.0516502125483738), (-2+2.4492935982947064e-16j))", "assert polar_rect(15,17)==((22.67156809750927, 0.8478169733934057), (-2+2.4492935982947064e-16j))"], "entry_point": "polar_rect", "test": "def check():\n    assert polar_rect(3,4)==((5.0, 0.9272952180016122), (-2+2.4492935982947064e-16j))\n    assert polar_rect(4,7)==((8.06225774829855, 1.0516502125483738), (-2+2.4492935982947064e-16j))\n    assert polar_rect(15,17)==((22.67156809750927, 0.8478169733934057), (-2+2.4492935982947064e-16j))\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 793, "prompt": "Write a python function to find the last position of an element in a sorted array.\n\ndef last(arr,x):", "code": "def last(arr,x):\n    n = len(arr)\n    low = 0\n    high = n - 1\n    res = -1  \n    while (low <= high):\n        mid = (low + high) // 2 \n        if arr[mid] > x:\n            high = mid - 1\n        elif arr[mid] < x:\n            low = mid + 1\n        else:\n            res = mid\n            low = mid + 1\n    return res", "test_imports": [], "test_list": ["assert last([1,2,3],1) == 0", "assert last([1,1,1,2,3,4],1) == 2", "assert last([2,3,2,3,6,8,9],3) == 3"], "entry_point": "last", "test": "def check():\n    assert last([1,2,3],1) == 0\n    assert last([1,1,1,2,3,4],1) == 2\n    assert last([2,3,2,3,6,8,9],3) == 3\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 602, "prompt": "Write a python function to find the first repeated character in a given string.\n\ndef first_repeated_char(str1):", "code": "def first_repeated_char(str1):\n  for index,c in enumerate(str1):\n    if str1[:index+1].count(c) > 1:\n      return c", "test_imports": [], "test_list": ["assert first_repeated_char(\"abcabc\") == \"a\"", "assert first_repeated_char(\"abc\") == None", "assert first_repeated_char(\"123123\") == \"1\""], "entry_point": "first_repeated_char", "test": "def check():\n    assert first_repeated_char(\"abcabc\") == \"a\"\n    assert first_repeated_char(\"abc\") == None\n    assert first_repeated_char(\"123123\") == \"1\"\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 445, "prompt": "Write a function to perform index wise multiplication of tuple elements in the given two tuples.\n\ndef index_multiplication(test_tup1, test_tup2):", "code": "def index_multiplication(test_tup1, test_tup2):\n  res = tuple(tuple(a * b for a, b in zip(tup1, tup2))\n   for tup1, tup2 in zip(test_tup1, test_tup2))\n  return (res) ", "test_imports": [], "test_list": ["assert index_multiplication(((1, 3), (4, 5), (2, 9), (1, 10)),((6, 7), (3, 9), (1, 1), (7, 3)) ) == ((6, 21), (12, 45), (2, 9), (7, 30))", "assert index_multiplication(((2, 4), (5, 6), (3, 10), (2, 11)),((7, 8), (4, 10), (2, 2), (8, 4)) ) == ((14, 32), (20, 60), (6, 20), (16, 44))", "assert index_multiplication(((3, 5), (6, 7), (4, 11), (3, 12)),((8, 9), (5, 11), (3, 3), (9, 5)) ) == ((24, 45), (30, 77), (12, 33), (27, 60))"], "entry_point": "index_multiplication", "test": "def check():\n    assert index_multiplication(((1, 3), (4, 5), (2, 9), (1, 10)),((6, 7), (3, 9), (1, 1), (7, 3)) ) == ((6, 21), (12, 45), (2, 9), (7, 30))\n    assert index_multiplication(((2, 4), (5, 6), (3, 10), (2, 11)),((7, 8), (4, 10), (2, 2), (8, 4)) ) == ((14, 32), (20, 60), (6, 20), (16, 44))\n    assert index_multiplication(((3, 5), (6, 7), (4, 11), (3, 12)),((8, 9), (5, 11), (3, 3), (9, 5)) ) == ((24, 45), (30, 77), (12, 33), (27, 60))\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 767, "prompt": "Write a python function to count the number of pairs whose sum is equal to \u2018sum\u2019. The funtion gets as input a list of numbers and the sum,\n\ndef get_pairs_count(arr, sum):", "code": "def get_pairs_count(arr, sum):\n    count = 0  \n    for i in range(len(arr)):\n        for j in range(i + 1,len(arr)):\n            if arr[i] + arr[j] == sum:\n                count += 1\n    return count", "test_imports": [], "test_list": ["assert get_pairs_count([1,1,1,1],2) == 6", "assert get_pairs_count([1,5,7,-1,5],6) == 3", "assert get_pairs_count([1,-2,3],1) == 1", "assert get_pairs_count([-1,-2,3],-3) == 1"], "entry_point": "get_pairs_count", "test": "def check():\n    assert get_pairs_count([1,1,1,1],2) == 6\n    assert get_pairs_count([1,5,7,-1,5],6) == 3\n    assert get_pairs_count([1,-2,3],1) == 1\n    assert get_pairs_count([-1,-2,3],-3) == 1\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 797, "prompt": "Write a python function to find the sum of all odd natural numbers within the range l and r.\n\ndef sum_in_range(l,r): ", "code": "def sum_odd(n): \n    terms = (n + 1)//2\n    sum1 = terms * terms \n    return sum1  \ndef sum_in_range(l,r): \n    return sum_odd(r) - sum_odd(l - 1)", "test_imports": [], "test_list": ["assert sum_in_range(2,5) == 8", "assert sum_in_range(5,7) == 12", "assert sum_in_range(7,13) == 40"], "entry_point": "sum_in_range", "test": "def check():\n    assert sum_in_range(2,5) == 8\n    assert sum_in_range(5,7) == 12\n    assert sum_in_range(7,13) == 40\n"}
{"source_file": "Ellen's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 294, "prompt": "Write a function to find the maximum value in a given heterogeneous list.\n\ndef max_val(listval):", "code": "def max_val(listval):\n     max_val = max(i for i in listval if isinstance(i, int)) \n     return(max_val)", "test_imports": [], "test_list": ["assert max_val(['Python', 3, 2, 4, 5, 'version'])==5", "assert max_val(['Python', 15, 20, 25])==25", "assert max_val(['Python', 30, 20, 40, 50, 'version'])==50"], "entry_point": "max_val", "test": "def check():\n    assert max_val(['Python', 3, 2, 4, 5, 'version'])==5\n    assert max_val(['Python', 15, 20, 25])==25\n    assert max_val(['Python', 30, 20, 40, 50, 'version'])==50\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 262, "prompt": "Write a function that takes in a list and an integer L and splits the given list into two parts where the length of the first part of the list is L, and returns the resulting lists in a tuple.\n\ndef split_two_parts(list1, L):", "code": "def split_two_parts(list1, L):\n    return list1[:L], list1[L:]", "test_imports": [], "test_list": ["assert split_two_parts([1,1,2,3,4,4,5,1],3)==([1, 1, 2], [3, 4, 4, 5, 1])", "assert split_two_parts(['a', 'b', 'c', 'd'],2)==(['a', 'b'], ['c', 'd'])", "assert split_two_parts(['p', 'y', 't', 'h', 'o', 'n'],4)==(['p', 'y', 't', 'h'], ['o', 'n'])"], "entry_point": "split_two_parts", "test": "def check():\n    assert split_two_parts([1,1,2,3,4,4,5,1],3)==([1, 1, 2], [3, 4, 4, 5, 1])\n    assert split_two_parts(['a', 'b', 'c', 'd'],2)==(['a', 'b'], ['c', 'd'])\n    assert split_two_parts(['p', 'y', 't', 'h', 'o', 'n'],4)==(['p', 'y', 't', 'h'], ['o', 'n'])\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 407, "prompt": "Write a function to create the next bigger number by rearranging the digits of a given number.\n\ndef rearrange_bigger(n):", "code": "def rearrange_bigger(n):\n    nums = list(str(n))\n    for i in range(len(nums)-2,-1,-1):\n        if nums[i] < nums[i+1]:\n            z = nums[i:]\n            y = min(filter(lambda x: x > z[0], z))\n            z.remove(y)\n            z.sort()\n            nums[i:] = [y] + z\n            return int(\"\".join(nums))\n    return False", "test_imports": [], "test_list": ["assert rearrange_bigger(12)==21", "assert rearrange_bigger(10)==False", "assert rearrange_bigger(102)==120"], "entry_point": "rearrange_bigger", "test": "def check():\n    assert rearrange_bigger(12)==21\n    assert rearrange_bigger(10)==False\n    assert rearrange_bigger(102)==120\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 474, "prompt": "Write a function to replace characters in a string.\n\ndef replace_char(str1,ch,newch):", "code": "def replace_char(str1,ch,newch):\n str2 = str1.replace(ch, newch)\n return str2", "test_imports": [], "test_list": ["assert replace_char(\"polygon\",'y','l')==(\"pollgon\")", "assert replace_char(\"character\",'c','a')==(\"aharaater\")", "assert replace_char(\"python\",'l','a')==(\"python\")"], "entry_point": "replace_char", "test": "def check():\n    assert replace_char(\"polygon\",'y','l')==(\"pollgon\")\n    assert replace_char(\"character\",'c','a')==(\"aharaater\")\n    assert replace_char(\"python\",'l','a')==(\"python\")\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 444, "prompt": "Write a function to trim each tuple by k in the given tuple list.\n\ndef trim_tuple(test_list, K):", "code": "def trim_tuple(test_list, K):\n  res = []\n  for ele in test_list:\n    N = len(ele)\n    res.append(tuple(list(ele)[K: N - K]))\n  return (str(res)) ", "test_imports": [], "test_list": ["assert trim_tuple([(5, 3, 2, 1, 4), (3, 4, 9, 2, 1),(9, 1, 2, 3, 5), (4, 8, 2, 1, 7)], 2) == '[(2,), (9,), (2,), (2,)]'", "assert trim_tuple([(5, 3, 2, 1, 4), (3, 4, 9, 2, 1), (9, 1, 2, 3, 5), (4, 8, 2, 1, 7)], 1) == '[(3, 2, 1), (4, 9, 2), (1, 2, 3), (8, 2, 1)]'", "assert trim_tuple([(7, 8, 4, 9), (11, 8, 12, 4),(4, 1, 7, 8), (3, 6, 9, 7)], 1) == '[(8, 4), (8, 12), (1, 7), (6, 9)]'"], "entry_point": "trim_tuple", "test": "def check():\n    assert trim_tuple([(5, 3, 2, 1, 4), (3, 4, 9, 2, 1),(9, 1, 2, 3, 5), (4, 8, 2, 1, 7)], 2) == '[(2,), (9,), (2,), (2,)]'\n    assert trim_tuple([(5, 3, 2, 1, 4), (3, 4, 9, 2, 1), (9, 1, 2, 3, 5), (4, 8, 2, 1, 7)], 1) == '[(3, 2, 1), (4, 9, 2), (1, 2, 3), (8, 2, 1)]'\n    assert trim_tuple([(7, 8, 4, 9), (11, 8, 12, 4),(4, 1, 7, 8), (3, 6, 9, 7)], 1) == '[(8, 4), (8, 12), (1, 7), (6, 9)]'\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 237, "prompt": "Write a function that takes in a list of tuples and returns a dictionary mapping each unique tuple to the number of times it occurs in the list.\nfrom collections import Counter \ndef check_occurences(test_list):", "code": "from collections import Counter \ndef check_occurences(test_list):\n  res = dict(Counter(tuple(ele) for ele in map(sorted, test_list)))\n  return  (res) ", "test_imports": [], "test_list": ["assert check_occurences([(3, 1), (1, 3), (2, 5), (5, 2), (6, 3)] ) == {(1, 3): 2, (2, 5): 2, (3, 6): 1}", "assert check_occurences([(4, 2), (2, 4), (3, 6), (6, 3), (7, 4)] ) == {(2, 4): 2, (3, 6): 2, (4, 7): 1}", "assert check_occurences([(13, 2), (11, 23), (12, 25), (25, 12), (16, 23)] ) == {(2, 13): 1, (11, 23): 1, (12, 25): 2, (16, 23): 1}"], "entry_point": "check_occurences", "test": "def check():\n    assert check_occurences([(3, 1), (1, 3), (2, 5), (5, 2), (6, 3)] ) == {(1, 3): 2, (2, 5): 2, (3, 6): 1}\n    assert check_occurences([(4, 2), (2, 4), (3, 6), (6, 3), (7, 4)] ) == {(2, 4): 2, (3, 6): 2, (4, 7): 1}\n    assert check_occurences([(13, 2), (11, 23), (12, 25), (25, 12), (16, 23)] ) == {(2, 13): 1, (11, 23): 1, (12, 25): 2, (16, 23): 1}\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 244, "prompt": "Write a python function to find the next perfect square greater than a given number.\nimport math  \ndef next_Perfect_Square(N): ", "code": "import math  \ndef next_Perfect_Square(N): \n    nextN = math.floor(math.sqrt(N)) + 1\n    return nextN * nextN ", "test_imports": [], "test_list": ["assert next_Perfect_Square(35) == 36", "assert next_Perfect_Square(6) == 9", "assert next_Perfect_Square(9) == 16"], "entry_point": "next_Perfect_Square", "test": "def check():\n    assert next_Perfect_Square(35) == 36\n    assert next_Perfect_Square(6) == 9\n    assert next_Perfect_Square(9) == 16\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 436, "prompt": "Write a python function to return the negative numbers in a list.\n\ndef neg_nos(list1):", "code": "def neg_nos(list1):\n  out = []\n  for num in list1: \n    if num < 0: \n      out.append(num)\n  return out ", "test_imports": [], "test_list": ["assert neg_nos([-1,4,5,-6]) == [-1,-6]", "assert neg_nos([-1,-2,3,4]) == [-1,-2]", "assert neg_nos([-7,-6,8,9]) == [-7,-6]"], "entry_point": "neg_nos", "test": "def check():\n    assert neg_nos([-1,4,5,-6]) == [-1,-6]\n    assert neg_nos([-1,-2,3,4]) == [-1,-2]\n    assert neg_nos([-7,-6,8,9]) == [-7,-6]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 749, "prompt": "Write a function to sort a given list of strings of numbers numerically. https://www.geeksforgeeks.org/python-sort-numeric-strings-in-a-list/\n\ndef sort_numeric_strings(nums_str):", "code": "def sort_numeric_strings(nums_str):\n    result = [int(x) for x in nums_str]\n    result.sort()\n    return result", "test_imports": [], "test_list": ["assert sort_numeric_strings( ['4','12','45','7','0','100','200','-12','-500'])==[-500, -12, 0, 4, 7, 12, 45, 100, 200]", "assert sort_numeric_strings(['2','3','8','4','7','9','8','2','6','5','1','6','1','2','3','4','6','9','1','2'])==[1, 1, 1, 2, 2, 2, 2, 3, 3, 4, 4, 5, 6, 6, 6, 7, 8, 8, 9, 9]", "assert sort_numeric_strings(['1','3','5','7','1', '3','13', '15', '17','5', '7 ','9','1', '11'])==[1, 1, 1, 3, 3, 5, 5, 7, 7, 9, 11, 13, 15, 17]"], "entry_point": "sort_numeric_strings", "test": "def check():\n    assert sort_numeric_strings( ['4','12','45','7','0','100','200','-12','-500'])==[-500, -12, 0, 4, 7, 12, 45, 100, 200]\n    assert sort_numeric_strings(['2','3','8','4','7','9','8','2','6','5','1','6','1','2','3','4','6','9','1','2'])==[1, 1, 1, 2, 2, 2, 2, 3, 3, 4, 4, 5, 6, 6, 6, 7, 8, 8, 9, 9]\n    assert sort_numeric_strings(['1','3','5','7','1', '3','13', '15', '17','5', '7 ','9','1', '11'])==[1, 1, 1, 3, 3, 5, 5, 7, 7, 9, 11, 13, 15, 17]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 580, "prompt": "Write a function to remove uneven elements in the nested mixed tuple.\n\ndef extract_even(test_tuple): ", "code": "def even_ele(test_tuple, even_fnc): \n\tres = tuple() \n\tfor ele in test_tuple: \n\t\tif isinstance(ele, tuple): \n\t\t\tres += (even_ele(ele, even_fnc), ) \n\t\telif even_fnc(ele): \n\t\t\tres += (ele, ) \n\treturn res \ndef extract_even(test_tuple):\n  res = even_ele(test_tuple, lambda x: x % 2 == 0)\n  return (res) ", "test_imports": [], "test_list": ["assert extract_even((4, 5, (7, 6, (2, 4)), 6, 8)) == (4, (6, (2, 4)), 6, 8)", "assert extract_even((5, 6, (8, 7, (4, 8)), 7, 9)) == (6, (8, (4, 8)))", "assert extract_even((5, 6, (9, 8, (4, 6)), 8, 10)) == (6, (8, (4, 6)), 8, 10)"], "entry_point": "extract_even", "test": "def check():\n    assert extract_even((4, 5, (7, 6, (2, 4)), 6, 8)) == (4, (6, (2, 4)), 6, 8)\n    assert extract_even((5, 6, (8, 7, (4, 8)), 7, 9)) == (6, (8, (4, 8)))\n    assert extract_even((5, 6, (9, 8, (4, 6)), 8, 10)) == (6, (8, (4, 6)), 8, 10)\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 251, "prompt": "Write a function that takes in a list and an element and inserts the element before each element in the list, and returns the resulting list.\n\ndef insert_element(list,element):", "code": "def insert_element(list,element):\n list = [v for elt in list for v in (element, elt)]\n return list", "test_imports": [], "test_list": ["assert insert_element(['Red', 'Green', 'Black'] ,'c')==['c', 'Red', 'c', 'Green', 'c', 'Black']", "assert insert_element(['python', 'java'] ,'program')==['program', 'python', 'program', 'java']", "assert insert_element(['happy', 'sad'] ,'laugh')==['laugh', 'happy', 'laugh', 'sad']"], "entry_point": "insert_element", "test": "def check():\n    assert insert_element(['Red', 'Green', 'Black'] ,'c')==['c', 'Red', 'c', 'Green', 'c', 'Black']\n    assert insert_element(['python', 'java'] ,'program')==['program', 'python', 'program', 'java']\n    assert insert_element(['happy', 'sad'] ,'laugh')==['laugh', 'happy', 'laugh', 'sad']\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 731, "prompt": "Write a function to find the lateral surface area of a cone given radius r and the height h.\nimport math\ndef lateralsurface_cone(r,h):", "code": "import math\ndef lateralsurface_cone(r,h):\n  l = math.sqrt(r * r + h * h)\n  LSA = math.pi * r  * l\n  return LSA", "test_imports": [], "test_list": ["assert lateralsurface_cone(5,12)==204.20352248333654", "assert lateralsurface_cone(10,15)==566.3586699569488", "assert lateralsurface_cone(19,17)==1521.8090132193388"], "entry_point": "lateralsurface_cone", "test": "def check():\n    assert lateralsurface_cone(5,12)==204.20352248333654\n    assert lateralsurface_cone(10,15)==566.3586699569488\n    assert lateralsurface_cone(19,17)==1521.8090132193388\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 227, "prompt": "Write a function to find minimum of three numbers.\n\ndef min_of_three(a,b,c): ", "code": "def min_of_three(a,b,c): \n      if (a <= b) and (a <= c): \n        smallest = a \n      elif (b <= a) and (b <= c): \n        smallest = b \n      else: \n        smallest = c \n      return smallest ", "test_imports": [], "test_list": ["assert min_of_three(10,20,0)==0", "assert min_of_three(19,15,18)==15", "assert min_of_three(-10,-20,-30)==-30"], "entry_point": "min_of_three", "test": "def check():\n    assert min_of_three(10,20,0)==0\n    assert min_of_three(19,15,18)==15\n    assert min_of_three(-10,-20,-30)==-30\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 614, "prompt": "Write a function to find the cumulative sum of all the values that are present in the given tuple list.\n\ndef cummulative_sum(test_list):", "code": "def cummulative_sum(test_list):\n  res = sum(map(sum, test_list))\n  return (res)", "test_imports": [], "test_list": ["assert cummulative_sum([(1, 3), (5, 6, 7), (2, 6)]) == 30", "assert cummulative_sum([(2, 4), (6, 7, 8), (3, 7)]) == 37", "assert cummulative_sum([(3, 5), (7, 8, 9), (4, 8)]) == 44"], "entry_point": "cummulative_sum", "test": "def check():\n    assert cummulative_sum([(1, 3), (5, 6, 7), (2, 6)]) == 30\n    assert cummulative_sum([(2, 4), (6, 7, 8), (3, 7)]) == 37\n    assert cummulative_sum([(3, 5), (7, 8, 9), (4, 8)]) == 44\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 259, "prompt": "Write a function to maximize the given two tuples.\n\ndef maximize_elements(test_tup1, test_tup2):", "code": "def maximize_elements(test_tup1, test_tup2):\n  res = tuple(tuple(max(a, b) for a, b in zip(tup1, tup2))\n   for tup1, tup2 in zip(test_tup1, test_tup2))\n  return (res) ", "test_imports": [], "test_list": ["assert maximize_elements(((1, 3), (4, 5), (2, 9), (1, 10)), ((6, 7), (3, 9), (1, 1), (7, 3))) == ((6, 7), (4, 9), (2, 9), (7, 10))", "assert maximize_elements(((2, 4), (5, 6), (3, 10), (2, 11)), ((7, 8), (4, 10), (2, 2), (8, 4))) == ((7, 8), (5, 10), (3, 10), (8, 11))", "assert maximize_elements(((3, 5), (6, 7), (4, 11), (3, 12)), ((8, 9), (5, 11), (3, 3), (9, 5))) == ((8, 9), (6, 11), (4, 11), (9, 12))"], "entry_point": "maximize_elements", "test": "def check():\n    assert maximize_elements(((1, 3), (4, 5), (2, 9), (1, 10)), ((6, 7), (3, 9), (1, 1), (7, 3))) == ((6, 7), (4, 9), (2, 9), (7, 10))\n    assert maximize_elements(((2, 4), (5, 6), (3, 10), (2, 11)), ((7, 8), (4, 10), (2, 2), (8, 4))) == ((7, 8), (5, 10), (3, 10), (8, 11))\n    assert maximize_elements(((3, 5), (6, 7), (4, 11), (3, 12)), ((8, 9), (5, 11), (3, 3), (9, 5))) == ((8, 9), (6, 11), (4, 11), (9, 12))\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 606, "prompt": "Write a function to convert degrees to radians.\nimport math\ndef radian_degree(degree):", "code": "import math\ndef radian_degree(degree):\n radian = degree*(math.pi/180)\n return radian", "test_imports": [], "test_list": ["assert radian_degree(90)==1.5707963267948966", "assert radian_degree(60)==1.0471975511965976", "assert radian_degree(120)==2.0943951023931953"], "entry_point": "radian_degree", "test": "def check():\n    assert radian_degree(90)==1.5707963267948966\n    assert radian_degree(60)==1.0471975511965976\n    assert radian_degree(120)==2.0943951023931953\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 90, "prompt": "Write a python function to find the length of the longest word.\n\ndef len_log(list1):", "code": "def len_log(list1):\n    max=len(list1[0])\n    for i in list1:\n        if len(i)>max:\n            max=len(i)\n    return max", "test_imports": [], "test_list": ["assert len_log([\"python\",\"PHP\",\"bigdata\"]) == 7", "assert len_log([\"a\",\"ab\",\"abc\"]) == 3", "assert len_log([\"small\",\"big\",\"tall\"]) == 5"], "entry_point": "len_log", "test": "def check():\n    assert len_log([\"python\",\"PHP\",\"bigdata\"]) == 7\n    assert len_log([\"a\",\"ab\",\"abc\"]) == 3\n    assert len_log([\"small\",\"big\",\"tall\"]) == 5\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 773, "prompt": "Write a function to find the occurrence and position of the substrings within a string. Return None if there is no match.\nimport re\ndef occurance_substring(text,pattern):", "code": "import re\ndef occurance_substring(text,pattern):\n for match in re.finditer(pattern, text):\n    s = match.start()\n    e = match.end()\n    return (text[s:e], s, e)", "test_imports": [], "test_list": ["assert occurance_substring('python programming, python language','python')==('python', 0, 6)", "assert occurance_substring('python programming,programming language','programming')==('programming', 7, 18)", "assert occurance_substring('python programming,programming language','language')==('language', 31, 39)", "assert occurance_substring('c++ programming, c++ language','python')==None"], "entry_point": "occurance_substring", "test": "def check():\n    assert occurance_substring('python programming, python language','python')==('python', 0, 6)\n    assert occurance_substring('python programming,programming language','programming')==('programming', 7, 18)\n    assert occurance_substring('python programming,programming language','language')==('language', 31, 39)\n    assert occurance_substring('c++ programming, c++ language','python')==None\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 115, "prompt": "Write a function to check whether all dictionaries in a list are empty or not.\n\ndef empty_dit(list1):", "code": "def empty_dit(list1):\n empty_dit=all(not d for d in list1)\n return empty_dit", "test_imports": [], "test_list": ["assert empty_dit([{},{},{}])==True", "assert empty_dit([{1,2},{},{}])==False", "assert empty_dit({})==True"], "entry_point": "empty_dit", "test": "def check():\n    assert empty_dit([{},{},{}])==True\n    assert empty_dit([{1,2},{},{}])==False\n    assert empty_dit({})==True\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 139, "prompt": "Write a function to find the circumference of a circle.\n\ndef circle_circumference(r):", "code": "def circle_circumference(r):\n  perimeter=2*3.1415*r\n  return perimeter", "test_imports": ["import math"], "test_list": ["assert math.isclose(circle_circumference(10), 62.830000000000005, rel_tol=0.001)", "assert math.isclose(circle_circumference(5), 31.415000000000003, rel_tol=0.001)", "assert math.isclose(circle_circumference(4), 25.132, rel_tol=0.001)"], "entry_point": "circle_circumference", "test": "def check():\n    assert math.isclose(circle_circumference(10), 62.830000000000005, rel_tol=0.001)\n    assert math.isclose(circle_circumference(5), 31.415000000000003, rel_tol=0.001)\n    assert math.isclose(circle_circumference(4), 25.132, rel_tol=0.001)\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 455, "prompt": "Write a function to check whether the given month number contains 31 days or not.\n\ndef check_monthnumb_number(monthnum2):", "code": "def check_monthnumb_number(monthnum2):\n  if(monthnum2==1 or monthnum2==3 or monthnum2==5 or monthnum2==7 or monthnum2==8 or monthnum2==10 or monthnum2==12):\n    return True\n  else:\n    return False", "test_imports": [], "test_list": ["assert check_monthnumb_number(5)==True", "assert check_monthnumb_number(2)==False", "assert check_monthnumb_number(6)==False"], "entry_point": "check_monthnumb_number", "test": "def check():\n    assert check_monthnumb_number(5)==True\n    assert check_monthnumb_number(2)==False\n    assert check_monthnumb_number(6)==False\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 394, "prompt": "Write a function to check if given tuple contains no duplicates.\n\ndef check_distinct(test_tup):", "code": "def check_distinct(test_tup):\n  res = True\n  temp = set()\n  for ele in test_tup:\n    if ele in temp:\n      res = False\n      break\n    temp.add(ele)\n  return res ", "test_imports": [], "test_list": ["assert check_distinct((1, 4, 5, 6, 1, 4)) == False", "assert check_distinct((1, 4, 5, 6)) == True", "assert check_distinct((2, 3, 4, 5, 6)) == True"], "entry_point": "check_distinct", "test": "def check():\n    assert check_distinct((1, 4, 5, 6, 1, 4)) == False\n    assert check_distinct((1, 4, 5, 6)) == True\n    assert check_distinct((2, 3, 4, 5, 6)) == True\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 287, "prompt": "Write a python function takes in an integer n and returns the sum of squares of first n even natural numbers.\n\ndef square_Sum(n):  ", "code": "def square_Sum(n):  \n    return int(2*n*(n+1)*(2*n+1)/3)", "test_imports": [], "test_list": ["assert square_Sum(2) == 20", "assert square_Sum(3) == 56", "assert square_Sum(4) == 120"], "entry_point": "square_Sum", "test": "def check():\n    assert square_Sum(2) == 20\n    assert square_Sum(3) == 56\n    assert square_Sum(4) == 120\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 18, "prompt": "Write a function to remove characters from the first string which are present in the second string.\n\ndef remove_dirty_chars(string, second_string): ", "code": "NO_OF_CHARS = 256\ndef str_to_list(string): \n\ttemp = [] \n\tfor x in string: \n\t\ttemp.append(x) \n\treturn temp \ndef lst_to_string(List): \n\treturn ''.join(List) \ndef get_char_count_array(string): \n\tcount = [0] * NO_OF_CHARS \n\tfor i in string: \n\t\tcount[ord(i)] += 1\n\treturn count \ndef remove_dirty_chars(string, second_string): \n\tcount = get_char_count_array(second_string) \n\tip_ind = 0\n\tres_ind = 0\n\ttemp = '' \n\tstr_list = str_to_list(string) \n\twhile ip_ind != len(str_list): \n\t\ttemp = str_list[ip_ind] \n\t\tif count[ord(temp)] == 0: \n\t\t\tstr_list[res_ind] = str_list[ip_ind] \n\t\t\tres_ind += 1\n\t\tip_ind+=1\n\treturn lst_to_string(str_list[0:res_ind]) ", "test_imports": [], "test_list": ["assert remove_dirty_chars(\"probasscurve\", \"pros\") == 'bacuve'", "assert remove_dirty_chars(\"digitalindia\", \"talent\") == 'digiidi'", "assert remove_dirty_chars(\"exoticmiles\", \"toxic\") == 'emles'"], "entry_point": "remove_dirty_chars", "test": "def check():\n    assert remove_dirty_chars(\"probasscurve\", \"pros\") == 'bacuve'\n    assert remove_dirty_chars(\"digitalindia\", \"talent\") == 'digiidi'\n    assert remove_dirty_chars(\"exoticmiles\", \"toxic\") == 'emles'\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 80, "prompt": "Write a function to find the nth tetrahedral number.\n\ndef tetrahedral_number(n): ", "code": "def tetrahedral_number(n): \n\treturn (n * (n + 1) * (n + 2)) / 6", "test_imports": [], "test_list": ["assert tetrahedral_number(5) == 35", "assert tetrahedral_number(6) == 56", "assert tetrahedral_number(7) == 84"], "entry_point": "tetrahedral_number", "test": "def check():\n    assert tetrahedral_number(5) == 35\n    assert tetrahedral_number(6) == 56\n    assert tetrahedral_number(7) == 84\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 70, "prompt": "Write a function to find whether all the given tuples have equal length or not.\n\ndef get_equal(Input):", "code": "def find_equal_tuple(Input):\n  k = 0 if not Input else len(Input[0])\n  flag = 1\n  for tuple in Input:\n    if len(tuple) != k:\n      flag = 0\n      break\n  return flag\ndef get_equal(Input):\n  return find_equal_tuple(Input) == 1", "test_imports": [], "test_list": ["assert get_equal([(11, 22, 33), (44, 55, 66)]) == True", "assert get_equal([(1, 2, 3), (4, 5, 6, 7)]) == False", "assert get_equal([(1, 2), (3, 4)]) == True"], "entry_point": "get_equal", "test": "def check():\n    assert get_equal([(11, 22, 33), (44, 55, 66)]) == True\n    assert get_equal([(1, 2, 3), (4, 5, 6, 7)]) == False\n    assert get_equal([(1, 2), (3, 4)]) == True\n"}
{"source_file": "Ellen's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 301, "prompt": "Write a function to find the depth of a dictionary.\n\ndef dict_depth(d):", "code": "def dict_depth(d):\n    if isinstance(d, dict):\n        return 1 + (max(map(dict_depth, d.values())) if d else 0)\n    return 0", "test_imports": [], "test_list": ["assert dict_depth({'a':1, 'b': {'c': {'d': {}}}})==4", "assert dict_depth({'a':1, 'b': {'c':'python'}})==2", "assert dict_depth({1: 'Sun', 2: {3: {4:'Mon'}}})==3"], "entry_point": "dict_depth", "test": "def check():\n    assert dict_depth({'a':1, 'b': {'c': {'d': {}}}})==4\n    assert dict_depth({'a':1, 'b': {'c':'python'}})==2\n    assert dict_depth({1: 'Sun', 2: {3: {4:'Mon'}}})==3\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 269, "prompt": "Write a function to find the ascii value of a character.\n\ndef ascii_value(k):", "code": "def ascii_value(k):\n  ch=k\n  return ord(ch)", "test_imports": [], "test_list": ["assert ascii_value('A')==65", "assert ascii_value('R')==82", "assert ascii_value('S')==83"], "entry_point": "ascii_value", "test": "def check():\n    assert ascii_value('A')==65\n    assert ascii_value('R')==82\n    assert ascii_value('S')==83\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 641, "prompt": "Write a function to find the nth nonagonal number.\n\ndef is_nonagonal(n): ", "code": "def is_nonagonal(n): \n\treturn int(n * (7 * n - 5) / 2) ", "test_imports": [], "test_list": ["assert is_nonagonal(10) == 325", "assert is_nonagonal(15) == 750", "assert is_nonagonal(18) == 1089"], "entry_point": "is_nonagonal", "test": "def check():\n    assert is_nonagonal(10) == 325\n    assert is_nonagonal(15) == 750\n    assert is_nonagonal(18) == 1089\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 585, "prompt": "Write a function to find the n most expensive items in a given dataset.\nimport heapq\ndef expensive_items(items,n):", "code": "import heapq\ndef expensive_items(items,n):\n  expensive_items = heapq.nlargest(n, items, key=lambda s: s['price'])\n  return expensive_items", "test_imports": [], "test_list": ["assert expensive_items([{'name': 'Item-1', 'price': 101.1},{'name': 'Item-2', 'price': 555.22}],1)==[{'name': 'Item-2', 'price': 555.22}]", "assert expensive_items([{'name': 'Item-1', 'price': 101.1},{'name': 'Item-2', 'price': 555.22}, {'name': 'Item-3', 'price': 45.09}],2)==[{'name': 'Item-2', 'price': 555.22},{'name': 'Item-1', 'price': 101.1}]", "assert expensive_items([{'name': 'Item-1', 'price': 101.1},{'name': 'Item-2', 'price': 555.22}, {'name': 'Item-3', 'price': 45.09},{'name': 'Item-4', 'price': 22.75}],1)==[{'name': 'Item-2', 'price': 555.22}]"], "entry_point": "expensive_items", "test": "def check():\n    assert expensive_items([{'name': 'Item-1', 'price': 101.1},{'name': 'Item-2', 'price': 555.22}],1)==[{'name': 'Item-2', 'price': 555.22}]\n    assert expensive_items([{'name': 'Item-1', 'price': 101.1},{'name': 'Item-2', 'price': 555.22}, {'name': 'Item-3', 'price': 45.09}],2)==[{'name': 'Item-2', 'price': 555.22},{'name': 'Item-1', 'price': 101.1}]\n    assert expensive_items([{'name': 'Item-1', 'price': 101.1},{'name': 'Item-2', 'price': 555.22}, {'name': 'Item-3', 'price': 45.09},{'name': 'Item-4', 'price': 22.75}],1)==[{'name': 'Item-2', 'price': 555.22}]\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 433, "prompt": "Write a function to check whether the entered number is greater than the elements of the given array.\n\ndef check_greater(arr, number):", "code": "def check_greater(arr, number):\n  arr.sort()\n  return number > arr[-1]", "test_imports": [], "test_list": ["assert check_greater([1, 2, 3, 4, 5], 4) == False", "assert check_greater([2, 3, 4, 5, 6], 8) == True", "assert check_greater([9, 7, 4, 8, 6, 1], 11) == True"], "entry_point": "check_greater", "test": "def check():\n    assert check_greater([1, 2, 3, 4, 5], 4) == False\n    assert check_greater([2, 3, 4, 5, 6], 8) == True\n    assert check_greater([9, 7, 4, 8, 6, 1], 11) == True\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 164, "prompt": "Write a function to determine if the sum of the divisors of two integers are the same.\nimport math \ndef are_equivalent(num1, num2)", "code": "import math \ndef div_sum(n): \n  total = 1\n  i = 2\n\n  while i * i <= n:\n    if (n % i == 0):\n      total = (total + i + math.floor(n / i))\n    i += 1\n\n  return total\n\ndef are_equivalent(num1, num2): \n    return div_sum(num1) == div_sum(num2); ", "test_imports": [], "test_list": ["assert are_equivalent(36, 57) == False", "assert are_equivalent(2, 4) == False", "assert are_equivalent(23, 47) == True"], "entry_point": "are_equivalent", "test": "def check():\n    assert are_equivalent(36, 57) == False\n    assert are_equivalent(2, 4) == False\n    assert are_equivalent(23, 47) == True\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 97, "prompt": "Write a function to find frequency of each element in a flattened list of lists, returned in a dictionary.\n\ndef frequency_lists(list1):", "code": "def frequency_lists(list1):\n    list1 = [item for sublist in list1 for item in sublist]\n    dic_data = {}\n    for num in list1:\n        if num in dic_data.keys():\n            dic_data[num] += 1\n        else:\n            key = num\n            value = 1\n            dic_data[key] = value\n    return dic_data\n", "test_imports": [], "test_list": ["assert frequency_lists([[1, 2, 3, 2], [4, 5, 6, 2], [7, 8, 9, 5]])=={1: 1, 2: 3, 3: 1, 4: 1, 5: 2, 6: 1, 7: 1, 8: 1, 9: 1}", "assert frequency_lists([[1,2,3,4],[5,6,7,8],[9,10,11,12]])=={1: 1, 2: 1, 3: 1, 4: 1, 5: 1, 6: 1, 7: 1, 8: 1, 9: 1,10:1,11:1,12:1}", "assert frequency_lists([[20,30,40,17],[18,16,14,13],[10,20,30,40]])=={20:2,30:2,40:2,17: 1,18:1, 16: 1,14: 1,13: 1, 10: 1}"], "entry_point": "frequency_lists", "test": "def check():\n    assert frequency_lists([[1, 2, 3, 2], [4, 5, 6, 2], [7, 8, 9, 5]])=={1: 1, 2: 3, 3: 1, 4: 1, 5: 2, 6: 1, 7: 1, 8: 1, 9: 1}\n    assert frequency_lists([[1,2,3,4],[5,6,7,8],[9,10,11,12]])=={1: 1, 2: 1, 3: 1, 4: 1, 5: 1, 6: 1, 7: 1, 8: 1, 9: 1,10:1,11:1,12:1}\n    assert frequency_lists([[20,30,40,17],[18,16,14,13],[10,20,30,40]])=={20:2,30:2,40:2,17: 1,18:1, 16: 1,14: 1,13: 1, 10: 1}\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 453, "prompt": "Write a python function to find the sum of even factors of a number.\nimport math \ndef sumofFactors(n) : ", "code": "import math \ndef sumofFactors(n) : \n    if (n % 2 != 0) : \n        return 0\n    res = 1\n    for i in range(2, (int)(math.sqrt(n)) + 1) :    \n        count = 0\n        curr_sum = 1\n        curr_term = 1\n        while (n % i == 0) : \n            count= count + 1\n            n = n // i \n            if (i == 2 and count == 1) : \n                curr_sum = 0\n            curr_term = curr_term * i \n            curr_sum = curr_sum + curr_term \n        res = res * curr_sum  \n    if (n >= 2) : \n        res = res * (1 + n) \n    return res", "test_imports": [], "test_list": ["assert sumofFactors(18) == 26", "assert sumofFactors(30) == 48", "assert sumofFactors(6) == 8"], "entry_point": "sumofFactors", "test": "def check():\n    assert sumofFactors(18) == 26\n    assert sumofFactors(30) == 48\n    assert sumofFactors(6) == 8\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 763, "prompt": "Write a python function to find the minimum difference between any two elements in a given array. https://www.geeksforgeeks.org/find-minimum-difference-pair/\n\ndef find_min_diff(arr:tuple,n): ", "code": "def find_min_diff(arr,n): \n    arr = sorted(arr) \n    diff = 10**20 \n    for i in range(n-1): \n        if arr[i+1] - arr[i] < diff: \n            diff = arr[i+1] - arr[i]  \n    return diff ", "test_imports": [], "test_list": ["assert find_min_diff((1,5,3,19,18,25),6) == 1", "assert find_min_diff((4,3,2,6),4) == 1", "assert find_min_diff((30,5,20,9),4) == 4"], "entry_point": "find_min_diff", "test": "def check():\n    assert find_min_diff((1,5,3,19,18,25),6) == 1\n    assert find_min_diff((4,3,2,6),4) == 1\n    assert find_min_diff((30,5,20,9),4) == 4\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 623, "prompt": "Write a function to compute the n-th power of each number in a list.\n\ndef nth_nums(nums,n):", "code": "def nth_nums(nums,n):\n nth_nums = list(map(lambda x: x ** n, nums))\n return nth_nums", "test_imports": [], "test_list": ["assert nth_nums([1, 2, 3, 4, 5, 6, 7, 8, 9, 10],2)==[1, 4, 9, 16, 25, 36, 49, 64, 81, 100]", "assert nth_nums([10,20,30],3)==([1000, 8000, 27000])", "assert nth_nums([12,15],5)==([248832, 759375])"], "entry_point": "nth_nums", "test": "def check():\n    assert nth_nums([1, 2, 3, 4, 5, 6, 7, 8, 9, 10],2)==[1, 4, 9, 16, 25, 36, 49, 64, 81, 100]\n    assert nth_nums([10,20,30],3)==([1000, 8000, 27000])\n    assert nth_nums([12,15],5)==([248832, 759375])\n"}
{"source_file": "Ellen's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 311, "prompt": "Write a python function to set the left most unset bit.\n\ndef set_left_most_unset_bit(n): ", "code": "def set_left_most_unset_bit(n): \n    if not (n & (n + 1)): \n        return n \n    pos, temp, count = 0, n, 0 \n    while temp: \n        if not (temp & 1): \n            pos = count      \n        count += 1; temp>>=1\n    return (n | (1 << (pos))) ", "test_imports": [], "test_list": ["assert set_left_most_unset_bit(10) == 14", "assert set_left_most_unset_bit(12) == 14", "assert set_left_most_unset_bit(15) == 15"], "entry_point": "set_left_most_unset_bit", "test": "def check():\n    assert set_left_most_unset_bit(10) == 14\n    assert set_left_most_unset_bit(12) == 14\n    assert set_left_most_unset_bit(15) == 15\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 87, "prompt": "Write a function to merge three dictionaries into a single dictionary.\nimport collections as ct\ndef merge_dictionaries_three(dict1,dict2, dict3):", "code": "import collections as ct\ndef merge_dictionaries_three(dict1,dict2, dict3):\n    merged_dict = dict(ct.ChainMap({},dict1,dict2,dict3))\n    return merged_dict", "test_imports": [], "test_list": ["assert merge_dictionaries_three({ \"R\": \"Red\", \"B\": \"Black\", \"P\": \"Pink\" }, { \"G\": \"Green\", \"W\": \"White\" },{ \"O\": \"Orange\", \"W\": \"White\", \"B\": \"Black\" })=={'B': 'Black', 'R': 'Red', 'P': 'Pink', 'G': 'Green', 'W': 'White', 'O': 'Orange'}", "assert merge_dictionaries_three({ \"R\": \"Red\", \"B\": \"Black\", \"P\": \"Pink\" }, { \"G\": \"Green\", \"W\": \"White\" },{\"L\":\"lavender\",\"B\":\"Blue\"})=={'W': 'White', 'P': 'Pink', 'B': 'Black', 'R': 'Red', 'G': 'Green', 'L': 'lavender'}", "assert merge_dictionaries_three({ \"R\": \"Red\", \"B\": \"Black\", \"P\": \"Pink\" },{\"L\":\"lavender\",\"B\":\"Blue\"},{ \"G\": \"Green\", \"W\": \"White\" })=={'B': 'Black', 'P': 'Pink', 'R': 'Red', 'G': 'Green', 'L': 'lavender', 'W': 'White'}"], "entry_point": "merge_dictionaries_three", "test": "def check():\n    assert merge_dictionaries_three({ \"R\": \"Red\", \"B\": \"Black\", \"P\": \"Pink\" }, { \"G\": \"Green\", \"W\": \"White\" },{ \"O\": \"Orange\", \"W\": \"White\", \"B\": \"Black\" })=={'B': 'Black', 'R': 'Red', 'P': 'Pink', 'G': 'Green', 'W': 'White', 'O': 'Orange'}\n    assert merge_dictionaries_three({ \"R\": \"Red\", \"B\": \"Black\", \"P\": \"Pink\" }, { \"G\": \"Green\", \"W\": \"White\" },{\"L\":\"lavender\",\"B\":\"Blue\"})=={'W': 'White', 'P': 'Pink', 'B': 'Black', 'R': 'Red', 'G': 'Green', 'L': 'lavender'}\n    assert merge_dictionaries_three({ \"R\": \"Red\", \"B\": \"Black\", \"P\": \"Pink\" },{\"L\":\"lavender\",\"B\":\"Blue\"},{ \"G\": \"Green\", \"W\": \"White\" })=={'B': 'Black', 'P': 'Pink', 'R': 'Red', 'G': 'Green', 'L': 'lavender', 'W': 'White'}\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 630, "prompt": "Write a function to extract all the adjacent coordinates of the given coordinate tuple.\n\ndef get_coordinates(test_tup): ", "code": "def adjac(test_tup): \n  if not ele: \n     yield sub \n  else: \n     yield from [idx for j in range(ele[0] - 1, ele[0] + 2) \n                for idx in adjac(ele[1:], sub + [j])] \ndef get_coordinates(test_tup):\n  return list(adjac(test_tup))", "test_imports": [], "test_list": ["assert get_coordinates((3, 4)) == [[2, 3], [2, 4], [2, 5], [3, 3], [3, 4], [3, 5], [4, 3], [4, 4], [4, 5]]", "assert get_coordinates((4, 5)) ==[[3, 4], [3, 5], [3, 6], [4, 4], [4, 5], [4, 6], [5, 4], [5, 5], [5, 6]]", "assert get_coordinates((5, 6)) == [[4, 5], [4, 6], [4, 7], [5, 5], [5, 6], [5, 7], [6, 5], [6, 6], [6, 7]]"], "entry_point": "get_coordinates", "test": "def check():\n    assert get_coordinates((3, 4)) == [[2, 3], [2, 4], [2, 5], [3, 3], [3, 4], [3, 5], [4, 3], [4, 4], [4, 5]]\n    assert get_coordinates((4, 5)) ==[[3, 4], [3, 5], [3, 6], [4, 4], [4, 5], [4, 6], [5, 4], [5, 5], [5, 6]]\n    assert get_coordinates((5, 6)) == [[4, 5], [4, 6], [4, 7], [5, 5], [5, 6], [5, 7], [6, 5], [6, 6], [6, 7]]\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 417, "prompt": "Write a function to find common first element in given list of tuple.\n\ndef group_tuples(Input): ", "code": "def group_tuples(Input): \n\tout = {} \n\tfor elem in Input: \n\t\ttry: \n\t\t\tout[elem[0]].extend(elem[1:]) \n\t\texcept KeyError: \n\t\t\tout[elem[0]] = list(elem) \n\treturn [tuple(values) for values in out.values()] ", "test_imports": [], "test_list": ["assert group_tuples([('x', 'y'), ('x', 'z'), ('w', 't')]) == [('x', 'y', 'z'), ('w', 't')]", "assert group_tuples([('a', 'b'), ('a', 'c'), ('d', 'e')]) == [('a', 'b', 'c'), ('d', 'e')]", "assert group_tuples([('f', 'g'), ('f', 'g'), ('h', 'i')]) == [('f', 'g', 'g'), ('h', 'i')]"], "entry_point": "group_tuples", "test": "def check():\n    assert group_tuples([('x', 'y'), ('x', 'z'), ('w', 't')]) == [('x', 'y', 'z'), ('w', 't')]\n    assert group_tuples([('a', 'b'), ('a', 'c'), ('d', 'e')]) == [('a', 'b', 'c'), ('d', 'e')]\n    assert group_tuples([('f', 'g'), ('f', 'g'), ('h', 'i')]) == [('f', 'g', 'g'), ('h', 'i')]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 576, "prompt": "Write a python function to check whether a list is sublist of another or not.\n\ndef is_Sub_Array(A,B): ", "code": "def is_Sub_Array(A,B): \n    n = len(A)\n    m = len(B)\n    i = 0; j = 0; \n    while (i < n and j < m):  \n        if (A[i] == B[j]): \n            i += 1; \n            j += 1; \n            if (j == m): \n                return True;  \n        else: \n            i = i - j + 1; \n            j = 0;       \n    return False; ", "test_imports": [], "test_list": ["assert is_Sub_Array([1,4,3,5],[1,2]) == False", "assert is_Sub_Array([1,2,1],[1,2,1]) == True", "assert is_Sub_Array([1,0,2,2],[2,2,0]) ==False"], "entry_point": "is_Sub_Array", "test": "def check():\n    assert is_Sub_Array([1,4,3,5],[1,2]) == False\n    assert is_Sub_Array([1,2,1],[1,2,1]) == True\n    assert is_Sub_Array([1,0,2,2],[2,2,0]) ==False\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 470, "prompt": "Write a function to find the pairwise addition of the neighboring elements of the given tuple.\n\ndef add_pairwise(test_tup):", "code": "def add_pairwise(test_tup):\n  res = tuple(i + j for i, j in zip(test_tup, test_tup[1:]))\n  return (res) ", "test_imports": [], "test_list": ["assert add_pairwise((1, 5, 7, 8, 10)) == (6, 12, 15, 18)", "assert add_pairwise((2, 6, 8, 9, 11)) == (8, 14, 17, 20)", "assert add_pairwise((3, 7, 9, 10, 12)) == (10, 16, 19, 22)"], "entry_point": "add_pairwise", "test": "def check():\n    assert add_pairwise((1, 5, 7, 8, 10)) == (6, 12, 15, 18)\n    assert add_pairwise((2, 6, 8, 9, 11)) == (8, 14, 17, 20)\n    assert add_pairwise((3, 7, 9, 10, 12)) == (10, 16, 19, 22)\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 732, "prompt": "Write a function to replace all occurrences of spaces, commas, or dots with a colon.\nimport re\ndef replace_specialchar(text):", "code": "import re\ndef replace_specialchar(text):\n return (re.sub(\"[ ,.]\", \":\", text))\n", "test_imports": [], "test_list": ["assert replace_specialchar('Python language, Programming language.')==('Python:language::Programming:language:')", "assert replace_specialchar('a b c,d e f')==('a:b:c:d:e:f')", "assert replace_specialchar('ram reshma,ram rahim')==('ram:reshma:ram:rahim')"], "entry_point": "replace_specialchar", "test": "def check():\n    assert replace_specialchar('Python language, Programming language.')==('Python:language::Programming:language:')\n    assert replace_specialchar('a b c,d e f')==('a:b:c:d:e:f')\n    assert replace_specialchar('ram reshma,ram rahim')==('ram:reshma:ram:rahim')\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 7, "prompt": "Write a function to find all words which are at least 4 characters long in a string.\nimport re\ndef find_char_long(text):", "code": "import re\ndef find_char_long(text):\n  return (re.findall(r\"\\b\\w{4,}\\b\", text))", "test_imports": [], "test_list": ["assert set(find_char_long('Please move back to stream')) == set(['Please', 'move', 'back', 'stream'])", "assert set(find_char_long('Jing Eco and Tech')) == set(['Jing', 'Tech'])", "assert set(find_char_long('Jhingai wulu road Zone 3')) == set(['Jhingai', 'wulu', 'road', 'Zone'])"], "entry_point": "find_char_long", "test": "def check():\n    assert set(find_char_long('Please move back to stream')) == set(['Please', 'move', 'back', 'stream'])\n    assert set(find_char_long('Jing Eco and Tech')) == set(['Jing', 'Tech'])\n    assert set(find_char_long('Jhingai wulu road Zone 3')) == set(['Jhingai', 'wulu', 'road', 'Zone'])\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 472, "prompt": "Write a python function to check whether the given list contains consecutive numbers or not.\n\ndef check_Consecutive(l): ", "code": "def check_Consecutive(l): \n    return sorted(l) == list(range(min(l),max(l)+1)) ", "test_imports": [], "test_list": ["assert check_Consecutive([1,2,3,4,5]) == True", "assert check_Consecutive([1,2,3,5,6]) == False", "assert check_Consecutive([1,2,1]) == False"], "entry_point": "check_Consecutive", "test": "def check():\n    assert check_Consecutive([1,2,3,4,5]) == True\n    assert check_Consecutive([1,2,3,5,6]) == False\n    assert check_Consecutive([1,2,1]) == False\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 762, "prompt": "Write a function to check whether the given month number contains 30 days or not. Months are given as number from 1 to 12.\n\ndef check_monthnumber_number(monthnum3):", "code": "def check_monthnumber_number(monthnum3):\n  return monthnum3==4 or monthnum3==6 or monthnum3==9 or monthnum3==11", "test_imports": [], "test_list": ["assert check_monthnumber_number(6)==True", "assert check_monthnumber_number(2)==False", "assert check_monthnumber_number(12)==False"], "entry_point": "check_monthnumber_number", "test": "def check():\n    assert check_monthnumber_number(6)==True\n    assert check_monthnumber_number(2)==False\n    assert check_monthnumber_number(12)==False\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 170, "prompt": "Write a function to find the sum of numbers in a list within a range specified by two indices.\n\ndef sum_range_list(list1, m, n):                                                                                                                                                                                                ", "code": "def sum_range_list(list1, m, n):                                                                                                                                                                                                \n    sum_range = 0                                                                                                                                                                                                         \n    for i in range(m, n+1, 1):                                                                                                                                                                                        \n        sum_range += list1[i]                                                                                                                                                                                                  \n    return sum_range   ", "test_imports": [], "test_list": ["assert sum_range_list([2,1,5,6,8,3,4,9,10,11,8,12], 8, 10) == 29", "assert sum_range_list([2,1,5,6,8,3,4,9,10,11,8,12], 5, 7) == 16", "assert sum_range_list([2,1,5,6,8,3,4,9,10,11,8,12], 7, 10) == 38"], "entry_point": "sum_range_list", "test": "def check():\n    assert sum_range_list([2,1,5,6,8,3,4,9,10,11,8,12], 8, 10) == 29\n    assert sum_range_list([2,1,5,6,8,3,4,9,10,11,8,12], 5, 7) == 16\n    assert sum_range_list([2,1,5,6,8,3,4,9,10,11,8,12], 7, 10) == 38\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 438, "prompt": "Write a function to count bidirectional tuple pairs.\n\ndef count_bidirectional(test_list):", "code": "def count_bidirectional(test_list):\n  res = 0\n  for idx in range(0, len(test_list)):\n    for iidx in range(idx + 1, len(test_list)):\n      if test_list[iidx][0] == test_list[idx][1] and test_list[idx][1] == test_list[iidx][0]:\n        res += 1\n  return res", "test_imports": [], "test_list": ["assert count_bidirectional([(5, 6), (1, 2), (6, 5), (9, 1), (6, 5), (2, 1)] ) == 3", "assert count_bidirectional([(5, 6), (1, 3), (6, 5), (9, 1), (6, 5), (2, 1)] ) == 2", "assert count_bidirectional([(5, 6), (1, 2), (6, 5), (9, 2), (6, 5), (2, 1)] ) == 4"], "entry_point": "count_bidirectional", "test": "def check():\n    assert count_bidirectional([(5, 6), (1, 2), (6, 5), (9, 1), (6, 5), (2, 1)] ) == 3\n    assert count_bidirectional([(5, 6), (1, 3), (6, 5), (9, 1), (6, 5), (2, 1)] ) == 2\n    assert count_bidirectional([(5, 6), (1, 2), (6, 5), (9, 2), (6, 5), (2, 1)] ) == 4\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 743, "prompt": "Write a function to rotate a given list by specified number of items to the right direction. https://www.geeksforgeeks.org/python-program-right-rotate-list-n/\n\ndef rotate_right(list, m):", "code": "def rotate_right(list, m):\n  result =  list[-m:] + list[:-m]\n  return result", "test_imports": [], "test_list": ["assert rotate_right([1, 2, 3, 4, 5, 6, 7, 8, 9, 10],3)==[8, 9, 10, 1, 2, 3, 4, 5, 6, 7]", "assert rotate_right([1, 2, 3, 4, 5, 6, 7, 8, 9, 10],2)==[9, 10, 1, 2, 3, 4, 5, 6, 7, 8]", "assert rotate_right([1, 2, 3, 4, 5, 6, 7, 8, 9, 10],5)==[6, 7, 8, 9, 10, 1, 2, 3, 4, 5]"], "entry_point": "rotate_right", "test": "def check():\n    assert rotate_right([1, 2, 3, 4, 5, 6, 7, 8, 9, 10],3)==[8, 9, 10, 1, 2, 3, 4, 5, 6, 7]\n    assert rotate_right([1, 2, 3, 4, 5, 6, 7, 8, 9, 10],2)==[9, 10, 1, 2, 3, 4, 5, 6, 7, 8]\n    assert rotate_right([1, 2, 3, 4, 5, 6, 7, 8, 9, 10],5)==[6, 7, 8, 9, 10, 1, 2, 3, 4, 5]\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 425, "prompt": "Write a function to count the number of sublists containing a particular element.\n\ndef count_element_in_list(list1, x): ", "code": "def count_element_in_list(list1, x): \n    ctr = 0\n    for i in range(len(list1)): \n        if x in list1[i]: \n            ctr+= 1          \n    return ctr", "test_imports": [], "test_list": ["assert count_element_in_list([[1, 3], [5, 7], [1, 11], [1, 15, 7]],1)==3", "assert count_element_in_list([['A', 'B'], ['A', 'C'], ['A', 'D', 'E'], ['B', 'C', 'D']],'A')==3", "assert count_element_in_list([['A', 'B'], ['A', 'C'], ['A', 'D', 'E'], ['B', 'C', 'D']],'E')==1"], "entry_point": "count_element_in_list", "test": "def check():\n    assert count_element_in_list([[1, 3], [5, 7], [1, 11], [1, 15, 7]],1)==3\n    assert count_element_in_list([['A', 'B'], ['A', 'C'], ['A', 'D', 'E'], ['B', 'C', 'D']],'A')==3\n    assert count_element_in_list([['A', 'B'], ['A', 'C'], ['A', 'D', 'E'], ['B', 'C', 'D']],'E')==1\n"}
{"source_file": "Ellen's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 304, "prompt": "Write a python function to find element at a given index after number of rotations.\n\ndef find_Element(arr,ranges,rotations,index) :  ", "code": "def find_Element(arr,ranges,rotations,index) :  \n    for i in range(rotations - 1,-1,-1 ) : \n        left = ranges[i][0] \n        right = ranges[i][1] \n        if (left <= index and right >= index) : \n            if (index == left) : \n                index = right \n            else : \n                index = index - 1 \n    return arr[index] ", "test_imports": [], "test_list": ["assert find_Element([1,2,3,4,5],[[0,2],[0,3]],2,1) == 3", "assert find_Element([1,2,3,4],[[0,1],[0,2]],1,2) == 3", "assert find_Element([1,2,3,4,5,6],[[0,1],[0,2]],1,1) == 1"], "entry_point": "find_Element", "test": "def check():\n    assert find_Element([1,2,3,4,5],[[0,2],[0,3]],2,1) == 3\n    assert find_Element([1,2,3,4],[[0,1],[0,2]],1,2) == 3\n    assert find_Element([1,2,3,4,5,6],[[0,1],[0,2]],1,1) == 1\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 167, "prompt": "Write a python function to find the smallest power of 2 greater than or equal to n.\n\ndef next_power_of_2(n): ", "code": "def next_power_of_2(n): \n  if n and not n & (n - 1):\n    return n\n\n  count = 0\n  while n != 0: \n    n >>= 1\n    count += 1\n\n  return 1 << count; ", "test_imports": [], "test_list": ["assert next_power_of_2(0) == 1", "assert next_power_of_2(5) == 8", "assert next_power_of_2(17) == 32"], "entry_point": "next_power_of_2", "test": "def check():\n    assert next_power_of_2(0) == 1\n    assert next_power_of_2(5) == 8\n    assert next_power_of_2(17) == 32\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 586, "prompt": "Write a python function to split a list at the nth eelment and add the first part to the end.\n\ndef split_Arr(l, n):", "code": "def split_Arr(l, n):\n  return l[n:] + l[:n]", "test_imports": [], "test_list": ["assert split_Arr([12,10,5,6,52,36],2) == [5,6,52,36,12,10]", "assert split_Arr([1,2,3,4],1) == [2,3,4,1]", "assert split_Arr([0,1,2,3,4,5,6,7],3) == [3,4,5,6,7,0,1,2]"], "entry_point": "split_Arr", "test": "def check():\n    assert split_Arr([12,10,5,6,52,36],2) == [5,6,52,36,12,10]\n    assert split_Arr([1,2,3,4],1) == [2,3,4,1]\n    assert split_Arr([0,1,2,3,4,5,6,7],3) == [3,4,5,6,7,0,1,2]\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 427, "prompt": "Write a function to convert a date of yyyy-mm-dd format to dd-mm-yyyy format.\nimport re\ndef change_date_format(dt):", "code": "import re\ndef change_date_format(dt):\n        return re.sub(r'(\\d{4})-(\\d{1,2})-(\\d{1,2})', '\\\\3-\\\\2-\\\\1', dt)", "test_imports": [], "test_list": ["assert change_date_format(\"2026-01-02\") == '02-01-2026'", "assert change_date_format(\"2020-11-13\") == '13-11-2020'", "assert change_date_format(\"2021-04-26\") == '26-04-2021'"], "entry_point": "change_date_format", "test": "def check():\n    assert change_date_format(\"2026-01-02\") == '02-01-2026'\n    assert change_date_format(\"2020-11-13\") == '13-11-2020'\n    assert change_date_format(\"2021-04-26\") == '26-04-2021'\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 108, "prompt": "Write a function to merge three lists into a single sorted list.\nimport heapq\ndef merge_sorted_list(num1,num2,num3):", "code": "import heapq\ndef merge_sorted_list(num1,num2,num3):\n  num1=sorted(num1)\n  num2=sorted(num2)\n  num3=sorted(num3)\n  result = heapq.merge(num1,num2,num3)\n  return list(result)", "test_imports": [], "test_list": ["assert merge_sorted_list([25, 24, 15, 4, 5, 29, 110],[19, 20, 11, 56, 25, 233, 154],[24, 26, 54, 48])==[4, 5, 11, 15, 19, 20, 24, 24, 25, 25, 26, 29, 48, 54, 56, 110, 154, 233]", "assert merge_sorted_list([1, 3, 5, 6, 8, 9], [2, 5, 7, 11], [1, 4, 7, 8, 12])==[1, 1, 2, 3, 4, 5, 5, 6, 7, 7, 8, 8, 9, 11, 12]", "assert merge_sorted_list([18, 14, 10, 9, 8, 7, 9, 3, 2, 4, 1],[25, 35, 22, 85, 14, 65, 75, 25, 58],[12, 74, 9, 50, 61, 41])==[1, 2, 3, 4, 7, 8, 9, 9, 9, 10, 12, 14, 14, 18, 22, 25, 25, 35, 41, 50, 58, 61, 65, 74, 75, 85]"], "entry_point": "merge_sorted_list", "test": "def check():\n    assert merge_sorted_list([25, 24, 15, 4, 5, 29, 110],[19, 20, 11, 56, 25, 233, 154],[24, 26, 54, 48])==[4, 5, 11, 15, 19, 20, 24, 24, 25, 25, 26, 29, 48, 54, 56, 110, 154, 233]\n    assert merge_sorted_list([1, 3, 5, 6, 8, 9], [2, 5, 7, 11], [1, 4, 7, 8, 12])==[1, 1, 2, 3, 4, 5, 5, 6, 7, 7, 8, 8, 9, 11, 12]\n    assert merge_sorted_list([18, 14, 10, 9, 8, 7, 9, 3, 2, 4, 1],[25, 35, 22, 85, 14, 65, 75, 25, 58],[12, 74, 9, 50, 61, 41])==[1, 2, 3, 4, 7, 8, 9, 9, 9, 10, 12, 14, 14, 18, 22, 25, 25, 35, 41, 50, 58, 61, 65, 74, 75, 85]\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 93, "prompt": "Write a function to calculate the value of 'a' to the power 'b'.\n\ndef power(a,b):", "code": "def power(a,b):\n\tif b==0:\n\t\treturn 1\n\telif a==0:\n\t\treturn 0\n\telif b==1:\n\t\treturn a\n\telse:\n\t\treturn a*power(a,b-1)", "test_imports": [], "test_list": ["assert power(3,4) == 81", "assert power(2,3) == 8", "assert power(5,5) == 3125"], "entry_point": "power", "test": "def check():\n    assert power(3,4) == 81\n    assert power(2,3) == 8\n    assert power(5,5) == 3125\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 75, "prompt": "Write a function to find tuples which have all elements divisible by k from the given list of tuples.\n\ndef find_tuples(test_list, K):", "code": "def find_tuples(test_list, K):\n  res = [sub for sub in test_list if all(ele % K == 0 for ele in sub)]\n  return res", "test_imports": [], "test_list": ["assert find_tuples([(6, 24, 12), (7, 9, 6), (12, 18, 21)], 6) == [(6, 24, 12)]", "assert find_tuples([(5, 25, 30), (4, 2, 3), (7, 8, 9)], 5) == [(5, 25, 30)]", "assert find_tuples([(7, 9, 16), (8, 16, 4), (19, 17, 18)], 4) == [(8, 16, 4)]"], "entry_point": "find_tuples", "test": "def check():\n    assert find_tuples([(6, 24, 12), (7, 9, 6), (12, 18, 21)], 6) == [(6, 24, 12)]\n    assert find_tuples([(5, 25, 30), (4, 2, 3), (7, 8, 9)], 5) == [(5, 25, 30)]\n    assert find_tuples([(7, 9, 16), (8, 16, 4), (19, 17, 18)], 4) == [(8, 16, 4)]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 557, "prompt": "Write a function to toggle the case of all characters in a string.\n\ndef toggle_string(string):", "code": "def toggle_string(string):\n string1 = string.swapcase()\n return string1", "test_imports": [], "test_list": ["assert toggle_string(\"Python\")==(\"pYTHON\")", "assert toggle_string(\"Pangram\")==(\"pANGRAM\")", "assert toggle_string(\"LIttLE\")==(\"liTTle\")"], "entry_point": "toggle_string", "test": "def check():\n    assert toggle_string(\"Python\")==(\"pYTHON\")\n    assert toggle_string(\"Pangram\")==(\"pANGRAM\")\n    assert toggle_string(\"LIttLE\")==(\"liTTle\")\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 410, "prompt": "Write a function to find the minimum value in a given heterogeneous list.\n\ndef min_val(listval):", "code": "def min_val(listval):\n     min_val = min(i for i in listval if isinstance(i, int))\n     return min_val", "test_imports": [], "test_list": ["assert min_val(['Python', 3, 2, 4, 5, 'version'])==2", "assert min_val(['Python', 15, 20, 25])==15", "assert min_val(['Python', 30, 20, 40, 50, 'version'])==20"], "entry_point": "min_val", "test": "def check():\n    assert min_val(['Python', 3, 2, 4, 5, 'version'])==2\n    assert min_val(['Python', 15, 20, 25])==15\n    assert min_val(['Python', 30, 20, 40, 50, 'version'])==20\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 759, "prompt": "Write a function to check whether a given string is a decimal number with a precision of 2.\n\ndef is_decimal(num):", "code": "def is_decimal(num):\n    import re\n    dnumre = re.compile(r\"\"\"^[0-9]+(\\.[0-9]{1,2})?$\"\"\")\n    result = dnumre.search(num)\n    return bool(result)", "test_imports": [], "test_list": ["assert is_decimal('123.11')==True", "assert is_decimal('e666.86')==False", "assert is_decimal('3.124587')==False", "assert is_decimal('1.11')==True", "assert is_decimal('1.1.11')==False"], "entry_point": "is_decimal", "test": "def check():\n    assert is_decimal('123.11')==True\n    assert is_decimal('e666.86')==False\n    assert is_decimal('3.124587')==False\n    assert is_decimal('1.11')==True\n    assert is_decimal('1.1.11')==False\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 69, "prompt": "Write a function to check whether a list contains the given sublist or not.\n\ndef is_sublist(l, s):", "code": "def is_sublist(l, s):\n\tsub_set = False\n\tif s == []:\n\t\tsub_set = True\n\telif s == l:\n\t\tsub_set = True\n\telif len(s) > len(l):\n\t\tsub_set = False\n\telse:\n\t\tfor i in range(len(l)):\n\t\t\tif l[i] == s[0]:\n\t\t\t\tn = 1\n\t\t\t\twhile (n < len(s)) and (l[i+n] == s[n]):\n\t\t\t\t\tn += 1\t\t\t\t\n\t\t\t\tif n == len(s):\n\t\t\t\t\tsub_set = True\n\treturn sub_set", "test_imports": [], "test_list": ["assert is_sublist([2,4,3,5,7],[3,7])==False", "assert is_sublist([2,4,3,5,7],[4,3])==True", "assert is_sublist([2,4,3,5,7],[1,6])==False"], "entry_point": "is_sublist", "test": "def check():\n    assert is_sublist([2,4,3,5,7],[3,7])==False\n    assert is_sublist([2,4,3,5,7],[4,3])==True\n    assert is_sublist([2,4,3,5,7],[1,6])==False\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 632, "prompt": "Write a python function to move all zeroes to the end of the given list.\n\ndef move_zero(num_list):", "code": "def move_zero(num_list):\n    a = [0 for i in range(num_list.count(0))]\n    x = [i for i in num_list if i != 0]\n    return x + a", "test_imports": [], "test_list": ["assert move_zero([1,0,2,0,3,4]) == [1,2,3,4,0,0]", "assert move_zero([2,3,2,0,0,4,0,5,0]) == [2,3,2,4,5,0,0,0,0]", "assert move_zero([0,1,0,1,1]) == [1,1,1,0,0]"], "entry_point": "move_zero", "test": "def check():\n    assert move_zero([1,0,2,0,3,4]) == [1,2,3,4,0,0]\n    assert move_zero([2,3,2,0,0,4,0,5,0]) == [2,3,2,4,5,0,0,0,0]\n    assert move_zero([0,1,0,1,1]) == [1,1,1,0,0]\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 442, "prompt": "Write a function to find the ration of positive numbers in an array of integers.\nfrom array import array\ndef positive_count(nums):", "code": "from array import array\ndef positive_count(nums):\n    n = len(nums)\n    n1 = 0\n    for x in nums:\n        if x > 0:\n            n1 += 1\n        else:\n          None\n    return round(n1/n,2)", "test_imports": [], "test_list": ["assert positive_count([0, 1, 2, -1, -5, 6, 0, -3, -2, 3, 4, 6, 8])==0.54", "assert positive_count([2, 1, 2, -1, -5, 6, 4, -3, -2, 3, 4, 6, 8])==0.69", "assert positive_count([2, 4, -6, -9, 11, -12, 14, -5, 17])==0.56"], "entry_point": "positive_count", "test": "def check():\n    assert positive_count([0, 1, 2, -1, -5, 6, 0, -3, -2, 3, 4, 6, 8])==0.54\n    assert positive_count([2, 1, 2, -1, -5, 6, 4, -3, -2, 3, 4, 6, 8])==0.69\n    assert positive_count([2, 4, -6, -9, 11, -12, 14, -5, 17])==0.56\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 476, "prompt": "Write a python function to find the sum of the largest and smallest value in a given array.\n\ndef big_sum(nums):", "code": "def big_sum(nums):\n      sum= max(nums)+min(nums)\n      return sum", "test_imports": [], "test_list": ["assert big_sum([1,2,3]) == 4", "assert big_sum([-1,2,3,4]) == 3", "assert big_sum([2,3,6]) == 8"], "entry_point": "big_sum", "test": "def check():\n    assert big_sum([1,2,3]) == 4\n    assert big_sum([-1,2,3,4]) == 3\n    assert big_sum([2,3,6]) == 8\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 582, "prompt": "Write a function to check if a dictionary is empty\n\ndef my_dict(dict1):", "code": "def my_dict(dict1):\n  if bool(dict1):\n     return False\n  else:\n     return True", "test_imports": [], "test_list": ["assert my_dict({10})==False", "assert my_dict({11})==False", "assert my_dict({})==True"], "entry_point": "my_dict", "test": "def check():\n    assert my_dict({10})==False\n    assert my_dict({11})==False\n    assert my_dict({})==True\n"}
{"source_file": "Ellen's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 292, "prompt": "Write a python function to find quotient of two numbers (rounded down to the nearest integer).\n\ndef find(n,m):  ", "code": "def find(n,m):  \n    q = n//m \n    return (q)", "test_imports": [], "test_list": ["assert find(10,3) == 3", "assert find(4,2) == 2", "assert find(20,5) == 4"], "entry_point": "find", "test": "def check():\n    assert find(10,3) == 3\n    assert find(4,2) == 2\n    assert find(20,5) == 4\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 766, "prompt": "Write a function to return a list of all pairs of consecutive items in a given list.\n\ndef pair_wise(l1):", "code": "def pair_wise(l1):\n    temp = []\n    for i in range(len(l1) - 1):\n        current_element, next_element = l1[i], l1[i + 1]\n        x = (current_element, next_element)\n        temp.append(x)\n    return temp", "test_imports": [], "test_list": ["assert pair_wise([1,1,2,3,3,4,4,5])==[(1, 1), (1, 2), (2, 3), (3, 3), (3, 4), (4, 4), (4, 5)]", "assert pair_wise([1,5,7,9,10])==[(1, 5), (5, 7), (7, 9), (9, 10)]", "assert pair_wise([5,1,9,7,10])==[(5, 1), (1, 9), (9, 7), (7, 10)]", "assert pair_wise([1,2,3,4,5,6,7,8,9,10])==[(1, 2), (2, 3), (3, 4), (4, 5), (5, 6), (6, 7), (7, 8), (8, 9), (9, 10)]"], "entry_point": "pair_wise", "test": "def check():\n    assert pair_wise([1,1,2,3,3,4,4,5])==[(1, 1), (1, 2), (2, 3), (3, 3), (3, 4), (4, 4), (4, 5)]\n    assert pair_wise([1,5,7,9,10])==[(1, 5), (5, 7), (7, 9), (9, 10)]\n    assert pair_wise([5,1,9,7,10])==[(5, 1), (1, 9), (9, 7), (7, 10)]\n    assert pair_wise([1,2,3,4,5,6,7,8,9,10])==[(1, 2), (2, 3), (3, 4), (4, 5), (5, 6), (6, 7), (7, 8), (8, 9), (9, 10)]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 265, "prompt": "Write a function that takes in a list and an integer n and splits a list for every nth element, returning a list of the resulting lists.\n\ndef list_split(S, step):", "code": "def list_split(S, step):\n    return [S[i::step] for i in range(step)]", "test_imports": [], "test_list": ["assert list_split(['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n'],3)==[['a', 'd', 'g', 'j', 'm'], ['b', 'e', 'h', 'k', 'n'], ['c', 'f', 'i', 'l']]", "assert list_split([1,2,3,4,5,6,7,8,9,10,11,12,13,14],3)==[[1,4,7,10,13], [2,5,8,11,14], [3,6,9,12]]", "assert list_split(['python','java','C','C++','DBMS','SQL'],2)==[['python', 'C', 'DBMS'], ['java', 'C++', 'SQL']]"], "entry_point": "list_split", "test": "def check():\n    assert list_split(['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n'],3)==[['a', 'd', 'g', 'j', 'm'], ['b', 'e', 'h', 'k', 'n'], ['c', 'f', 'i', 'l']]\n    assert list_split([1,2,3,4,5,6,7,8,9,10,11,12,13,14],3)==[[1,4,7,10,13], [2,5,8,11,14], [3,6,9,12]]\n    assert list_split(['python','java','C','C++','DBMS','SQL'],2)==[['python', 'C', 'DBMS'], ['java', 'C++', 'SQL']]\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 109, "prompt": "Write a python function to find the number of numbers with an odd value when rotating a binary string the given number of times.\n\ndef odd_Equivalent(s,n): ", "code": "def odd_Equivalent(s,n): \n    count=0\n    for i in range(0,n): \n        if (s[i] == '1'): \n            count = count + 1\n    return count ", "test_imports": [], "test_list": ["assert odd_Equivalent(\"011001\",6) == 3", "assert odd_Equivalent(\"11011\",5) == 4", "assert odd_Equivalent(\"1010\",4) == 2"], "entry_point": "odd_Equivalent", "test": "def check():\n    assert odd_Equivalent(\"011001\",6) == 3\n    assert odd_Equivalent(\"11011\",5) == 4\n    assert odd_Equivalent(\"1010\",4) == 2\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 261, "prompt": "Write a function that takes in two tuples and performs mathematical division operation element-wise across the given tuples.\n\ndef division_elements(test_tup1, test_tup2):", "code": "def division_elements(test_tup1, test_tup2):\n  res = tuple(ele1 // ele2 for ele1, ele2 in zip(test_tup1, test_tup2))\n  return (res) ", "test_imports": [], "test_list": ["assert division_elements((10, 4, 6, 9),(5, 2, 3, 3)) == (2, 2, 2, 3)", "assert division_elements((12, 6, 8, 16),(6, 3, 4, 4)) == (2, 2, 2, 4)", "assert division_elements((20, 14, 36, 18),(5, 7, 6, 9)) == (4, 2, 6, 2)"], "entry_point": "division_elements", "test": "def check():\n    assert division_elements((10, 4, 6, 9),(5, 2, 3, 3)) == (2, 2, 2, 3)\n    assert division_elements((12, 6, 8, 16),(6, 3, 4, 4)) == (2, 2, 2, 4)\n    assert division_elements((20, 14, 36, 18),(5, 7, 6, 9)) == (4, 2, 6, 2)\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 252, "prompt": "Write a python function to convert complex numbers to polar coordinates.\nimport cmath  \ndef convert(numbers):    ", "code": "import cmath  \ndef convert(numbers):    \n  num = cmath.polar(numbers)  \n  return (num) ", "test_imports": [], "test_list": ["assert convert(1) == (1.0, 0.0)", "assert convert(4) == (4.0,0.0)", "assert convert(5) == (5.0,0.0)"], "entry_point": "convert", "test": "def check():\n    assert convert(1) == (1.0, 0.0)\n    assert convert(4) == (4.0,0.0)\n    assert convert(5) == (5.0,0.0)\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 12, "prompt": "Write a function to sort a given matrix in ascending order according to the sum of its rows.\n\ndef sort_matrix(M):", "code": "def sort_matrix(M):\n    result = sorted(M, key=sum)\n    return result", "test_imports": [], "test_list": ["assert sort_matrix([[1, 2, 3], [2, 4, 5], [1, 1, 1]])==[[1, 1, 1], [1, 2, 3], [2, 4, 5]]", "assert sort_matrix([[1, 2, 3], [-2, 4, -5], [1, -1, 1]])==[[-2, 4, -5], [1, -1, 1], [1, 2, 3]]", "assert sort_matrix([[5,8,9],[6,4,3],[2,1,4]])==[[2, 1, 4], [6, 4, 3], [5, 8, 9]]"], "entry_point": "sort_matrix", "test": "def check():\n    assert sort_matrix([[1, 2, 3], [2, 4, 5], [1, 1, 1]])==[[1, 1, 1], [1, 2, 3], [2, 4, 5]]\n    assert sort_matrix([[1, 2, 3], [-2, 4, -5], [1, -1, 1]])==[[-2, 4, -5], [1, -1, 1], [1, 2, 3]]\n    assert sort_matrix([[5,8,9],[6,4,3],[2,1,4]])==[[2, 1, 4], [6, 4, 3], [5, 8, 9]]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 624, "prompt": "Write a python function to convert a given string to uppercase.\n\ndef is_upper(string):", "code": "def is_upper(string):\n  return (string.upper())", "test_imports": [], "test_list": ["assert is_upper(\"person\") ==\"PERSON\"", "assert is_upper(\"final\") == \"FINAL\"", "assert is_upper(\"Valid\") == \"VALID\""], "entry_point": "is_upper", "test": "def check():\n    assert is_upper(\"person\") ==\"PERSON\"\n    assert is_upper(\"final\") == \"FINAL\"\n    assert is_upper(\"Valid\") == \"VALID\"\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 113, "prompt": "Write a function to check if a string represents an integer or not.\n\ndef check_integer(text):", "code": "def check_integer(text):\n text = text.strip()\n if len(text) < 1:\n    return None\n else:\n     if all(text[i] in \"0123456789\" for i in range(len(text))):\n          return True\n     elif (text[0] in \"+-\") and \\\n         all(text[i] in \"0123456789\" for i in range(1,len(text))):\n         return True\n     else:\n        return False", "test_imports": [], "test_list": ["assert check_integer(\"python\")==False", "assert check_integer(\"1\")==True", "assert check_integer(\"12345\")==True"], "entry_point": "check_integer", "test": "def check():\n    assert check_integer(\"python\")==False\n    assert check_integer(\"1\")==True\n    assert check_integer(\"12345\")==True\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 594, "prompt": "Write a function to find the difference of the first even and first odd number of a given list.\n\ndef diff_even_odd(list1):", "code": "def diff_even_odd(list1):\n    first_even = next((el for el in list1 if el%2==0),-1)\n    first_odd = next((el for el in list1 if el%2!=0),-1)\n    return (first_even-first_odd)", "test_imports": [], "test_list": ["assert diff_even_odd([1,3,5,7,4,1,6,8])==3", "assert diff_even_odd([1,2,3,4,5,6,7,8,9,10])==1", "assert diff_even_odd([1,5,7,9,10])==9"], "entry_point": "diff_even_odd", "test": "def check():\n    assert diff_even_odd([1,3,5,7,4,1,6,8])==3\n    assert diff_even_odd([1,2,3,4,5,6,7,8,9,10])==1\n    assert diff_even_odd([1,5,7,9,10])==9\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 20, "prompt": "Write a function to check if the given number is woodball or not.\n\ndef is_woodall(x): ", "code": "def is_woodall(x): \n\tif (x % 2 == 0): \n\t\treturn False\n\tif (x == 1): \n\t\treturn True\n\tx = x + 1 \n\tp = 0\n\twhile (x % 2 == 0): \n\t\tx = x/2\n\t\tp = p + 1\n\t\tif (p == x): \n\t\t\treturn True\n\treturn False", "test_imports": [], "test_list": ["assert is_woodall(383) == True", "assert is_woodall(254) == False", "assert is_woodall(200) == False"], "entry_point": "is_woodall", "test": "def check():\n    assert is_woodall(383) == True\n    assert is_woodall(254) == False\n    assert is_woodall(200) == False\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 284, "prompt": "Write a function that takes in a list and element and checks whether all items in the list are equal to the given element.\n\ndef check_element(list,element):", "code": "def check_element(list,element):\n  check_element=all(v== element for v in list)\n  return check_element", "test_imports": [], "test_list": ["assert check_element([\"green\", \"orange\", \"black\", \"white\"],'blue')==False", "assert check_element([1,2,3,4],7)==False", "assert check_element([\"green\", \"green\", \"green\", \"green\"],'green')==True"], "entry_point": "check_element", "test": "def check():\n    assert check_element([\"green\", \"orange\", \"black\", \"white\"],'blue')==False\n    assert check_element([1,2,3,4],7)==False\n    assert check_element([\"green\", \"green\", \"green\", \"green\"],'green')==True\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 86, "prompt": "Write a function to find nth centered hexagonal number.\n\ndef centered_hexagonal_number(n):", "code": "def centered_hexagonal_number(n):\n  return 3 * n * (n - 1) + 1", "test_imports": [], "test_list": ["assert centered_hexagonal_number(10) == 271", "assert centered_hexagonal_number(2) == 7", "assert centered_hexagonal_number(9) == 217"], "entry_point": "centered_hexagonal_number", "test": "def check():\n    assert centered_hexagonal_number(10) == 271\n    assert centered_hexagonal_number(2) == 7\n    assert centered_hexagonal_number(9) == 217\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 772, "prompt": "Write a function to remove all the words with k length in the given string.\n\ndef remove_length(test_str, K):", "code": "def remove_length(test_str, K):\n  temp = test_str.split()\n  res = [ele for ele in temp if len(ele) != K]\n  res = ' '.join(res)\n  return (res) ", "test_imports": [], "test_list": ["assert remove_length('The person is most value tet', 3) == 'person is most value'", "assert remove_length('If you told me about this ok', 4) == 'If you me about ok'", "assert remove_length('Forces of darkeness is come into the play', 4) == 'Forces of darkeness is the'"], "entry_point": "remove_length", "test": "def check():\n    assert remove_length('The person is most value tet', 3) == 'person is most value'\n    assert remove_length('If you told me about this ok', 4) == 'If you me about ok'\n    assert remove_length('Forces of darkeness is come into the play', 4) == 'Forces of darkeness is the'\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 722, "prompt": "The input is given as - a dictionary with a student name as a key and a tuple of float (student_height, student_weight) as a value, - minimal height, - minimal weight. Write a function to filter students that have height and weight above the minimum.\n\ndef filter_data(students,h,w):", "code": "def filter_data(students,h,w):\n    result = {k: s for k, s in students.items() if s[0] >=h and s[1] >=w}\n    return result    ", "test_imports": [], "test_list": ["assert filter_data({'Cierra Vega': (6.2, 70), 'Alden Cantrell': (5.9, 65), 'Kierra Gentry': (6.0, 68), 'Pierre Cox': (5.8, 66)},6.0,70)=={'Cierra Vega': (6.2, 70)}", "assert filter_data({'Cierra Vega': (6.2, 70), 'Alden Cantrell': (5.9, 65), 'Kierra Gentry': (6.0, 68), 'Pierre Cox': (5.8, 66)},5.9,67)=={'Cierra Vega': (6.2, 70),'Kierra Gentry': (6.0, 68)}", "assert filter_data({'Cierra Vega': (6.2, 70), 'Alden Cantrell': (5.9, 65), 'Kierra Gentry': (6.0, 68), 'Pierre Cox': (5.8, 66)},5.7,64)=={'Cierra Vega': (6.2, 70),'Alden Cantrell': (5.9, 65),'Kierra Gentry': (6.0, 68),'Pierre Cox': (5.8, 66)}"], "entry_point": "filter_data", "test": "def check():\n    assert filter_data({'Cierra Vega': (6.2, 70), 'Alden Cantrell': (5.9, 65), 'Kierra Gentry': (6.0, 68), 'Pierre Cox': (5.8, 66)},6.0,70)=={'Cierra Vega': (6.2, 70)}\n    assert filter_data({'Cierra Vega': (6.2, 70), 'Alden Cantrell': (5.9, 65), 'Kierra Gentry': (6.0, 68), 'Pierre Cox': (5.8, 66)},5.9,67)=={'Cierra Vega': (6.2, 70),'Kierra Gentry': (6.0, 68)}\n    assert filter_data({'Cierra Vega': (6.2, 70), 'Alden Cantrell': (5.9, 65), 'Kierra Gentry': (6.0, 68), 'Pierre Cox': (5.8, 66)},5.7,64)=={'Cierra Vega': (6.2, 70),'Alden Cantrell': (5.9, 65),'Kierra Gentry': (6.0, 68),'Pierre Cox': (5.8, 66)}\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 428, "prompt": "Write a function to sort the given array by using shell sort.\n\ndef shell_sort(my_list):", "code": "def shell_sort(my_list):\n    gap = len(my_list) // 2\n    while gap > 0:\n        for i in range(gap, len(my_list)):\n            current_item = my_list[i]\n            j = i\n            while j >= gap and my_list[j - gap] > current_item:\n                my_list[j] = my_list[j - gap]\n                j -= gap\n            my_list[j] = current_item\n        gap //= 2\n\n    return my_list", "test_imports": [], "test_list": ["assert shell_sort([12, 23, 4, 5, 3, 2, 12, 81, 56, 95]) == [2, 3, 4, 5, 12, 12, 23, 56, 81, 95]", "assert shell_sort([24, 22, 39, 34, 87, 73, 68]) == [22, 24, 34, 39, 68, 73, 87]", "assert shell_sort([32, 30, 16, 96, 82, 83, 74]) == [16, 30, 32, 74, 82, 83, 96]"], "entry_point": "shell_sort", "test": "def check():\n    assert shell_sort([12, 23, 4, 5, 3, 2, 12, 81, 56, 95]) == [2, 3, 4, 5, 12, 12, 23, 56, 81, 95]\n    assert shell_sort([24, 22, 39, 34, 87, 73, 68]) == [22, 24, 34, 39, 68, 73, 87]\n    assert shell_sort([32, 30, 16, 96, 82, 83, 74]) == [16, 30, 32, 74, 82, 83, 96]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 572, "prompt": "Write a python function to remove duplicate numbers from a given number of lists.\n\ndef two_unique_nums(nums):", "code": "def two_unique_nums(nums):\n  return [i for i in nums if nums.count(i)==1]", "test_imports": [], "test_list": ["assert two_unique_nums([1,2,3,2,3,4,5]) == [1, 4, 5]", "assert two_unique_nums([1,2,3,2,4,5]) == [1, 3, 4, 5]", "assert two_unique_nums([1,2,3,4,5]) == [1, 2, 3, 4, 5]"], "entry_point": "two_unique_nums", "test": "def check():\n    assert two_unique_nums([1,2,3,2,3,4,5]) == [1, 4, 5]\n    assert two_unique_nums([1,2,3,2,4,5]) == [1, 3, 4, 5]\n    assert two_unique_nums([1,2,3,4,5]) == [1, 2, 3, 4, 5]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 748, "prompt": "Write a function to put spaces between words starting with capital letters in a given string.\nimport re\ndef capital_words_spaces(str1):", "code": "import re\ndef capital_words_spaces(str1):\n  return re.sub(r\"(\\w)([A-Z])\", r\"\\1 \\2\", str1)", "test_imports": [], "test_list": ["assert capital_words_spaces(\"Python\") == 'Python'", "assert capital_words_spaces(\"PythonProgrammingExamples\") == 'Python Programming Examples'", "assert capital_words_spaces(\"GetReadyToBeCodingFreak\") == 'Get Ready To Be Coding Freak'"], "entry_point": "capital_words_spaces", "test": "def check():\n    assert capital_words_spaces(\"Python\") == 'Python'\n    assert capital_words_spaces(\"PythonProgrammingExamples\") == 'Python Programming Examples'\n    assert capital_words_spaces(\"GetReadyToBeCodingFreak\") == 'Get Ready To Be Coding Freak'\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 637, "prompt": "Write a function to check whether the given amount has no profit and no loss\n\ndef noprofit_noloss(actual_cost,sale_amount): ", "code": "def noprofit_noloss(actual_cost,sale_amount): \n  if(sale_amount == actual_cost):\n    return True\n  else:\n    return False", "test_imports": [], "test_list": ["assert noprofit_noloss(1500,1200)==False", "assert noprofit_noloss(100,100)==True", "assert noprofit_noloss(2000,5000)==False"], "entry_point": "noprofit_noloss", "test": "def check():\n    assert noprofit_noloss(1500,1200)==False\n    assert noprofit_noloss(100,100)==True\n    assert noprofit_noloss(2000,5000)==False\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 448, "prompt": "Write a function to calculate the sum of perrin numbers.\n\ndef cal_sum(n): ", "code": "def cal_sum(n): \n\ta = 3\n\tb = 0\n\tc = 2\n\tif (n == 0): \n\t\treturn 3\n\tif (n == 1): \n\t\treturn 3\n\tif (n == 2): \n\t\treturn 5\n\tsum = 5\n\twhile (n > 2): \n\t\td = a + b \n\t\tsum = sum + d \n\t\ta = b \n\t\tb = c \n\t\tc = d \n\t\tn = n-1\n\treturn sum", "test_imports": [], "test_list": ["assert cal_sum(9) == 49", "assert cal_sum(10) == 66", "assert cal_sum(11) == 88"], "entry_point": "cal_sum", "test": "def check():\n    assert cal_sum(9) == 49\n    assert cal_sum(10) == 66\n    assert cal_sum(11) == 88\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 418, "prompt": "Write a python function to find the element of a list having maximum length.\n\ndef Find_Max(lst): ", "code": "def Find_Max(lst): \n    maxList = max((x) for x in lst) \n    return maxList", "test_imports": [], "test_list": ["assert Find_Max([['A'],['A','B'],['A','B','C']]) == ['A','B','C']", "assert Find_Max([[1],[1,2],[1,2,3]]) == [1,2,3]", "assert Find_Max([[1,1],[1,2,3],[1,5,6,1]]) == [1,5,6,1]"], "entry_point": "Find_Max", "test": "def check():\n    assert Find_Max([['A'],['A','B'],['A','B','C']]) == ['A','B','C']\n    assert Find_Max([[1],[1,2],[1,2,3]]) == [1,2,3]\n    assert Find_Max([[1,1],[1,2,3],[1,5,6,1]]) == [1,5,6,1]\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 424, "prompt": "Write a function to extract only the rear index element of each string in the given tuple.\n\ndef extract_rear(test_tuple):", "code": "def extract_rear(test_tuple):\n  res = list(sub[len(sub) - 1] for sub in test_tuple)\n  return (res) ", "test_imports": [], "test_list": ["assert extract_rear(('Mers', 'for', 'Vers') ) == ['s', 'r', 's']", "assert extract_rear(('Avenge', 'for', 'People') ) == ['e', 'r', 'e']", "assert extract_rear(('Gotta', 'get', 'go') ) == ['a', 't', 'o']"], "entry_point": "extract_rear", "test": "def check():\n    assert extract_rear(('Mers', 'for', 'Vers') ) == ['s', 'r', 's']\n    assert extract_rear(('Avenge', 'for', 'People') ) == ['e', 'r', 'e']\n    assert extract_rear(('Gotta', 'get', 'go') ) == ['a', 't', 'o']\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 589, "prompt": "Write a function to find perfect squares between two given numbers.\n\ndef perfect_squares(a, b):", "code": "def perfect_squares(a, b):\n    lists=[]\n    for i in range (a,b+1):\n        j = 1;\n        while j*j <= i:\n            if j*j == i:\n                 lists.append(i)  \n            j = j+1\n        i = i+1\n    return lists", "test_imports": [], "test_list": ["assert perfect_squares(1,30)==[1, 4, 9, 16, 25]", "assert perfect_squares(50,100)==[64, 81, 100]", "assert perfect_squares(100,200)==[100, 121, 144, 169, 196]"], "entry_point": "perfect_squares", "test": "def check():\n    assert perfect_squares(1,30)==[1, 4, 9, 16, 25]\n    assert perfect_squares(50,100)==[64, 81, 100]\n    assert perfect_squares(100,200)==[100, 121, 144, 169, 196]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 770, "prompt": "Write a python function to find the sum of fourth power of first n odd natural numbers.\n\ndef odd_num_sum(n) : ", "code": "def odd_num_sum(n) : \n    j = 0\n    sm = 0\n    for i in range(1,n + 1) : \n        j = (2*i-1) \n        sm = sm + (j*j*j*j)   \n    return sm ", "test_imports": [], "test_list": ["assert odd_num_sum(2) == 82", "assert odd_num_sum(3) == 707", "assert odd_num_sum(4) == 3108"], "entry_point": "odd_num_sum", "test": "def check():\n    assert odd_num_sum(2) == 82\n    assert odd_num_sum(3) == 707\n    assert odd_num_sum(4) == 3108\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 464, "prompt": "Write a function to check if all values are same in a dictionary.\n\ndef check_value(dict, n):", "code": "def check_value(dict, n):\n    result = all(x == n for x in dict.values()) \n    return result", "test_imports": [], "test_list": ["assert check_value({'Cierra Vega': 12, 'Alden Cantrell': 12, 'Kierra Gentry': 12, 'Pierre Cox': 12},10)==False", "assert check_value({'Cierra Vega': 12, 'Alden Cantrell': 12, 'Kierra Gentry': 12, 'Pierre Cox': 12},12)==True", "assert check_value({'Cierra Vega': 12, 'Alden Cantrell': 12, 'Kierra Gentry': 12, 'Pierre Cox': 12},5)==False"], "entry_point": "check_value", "test": "def check():\n    assert check_value({'Cierra Vega': 12, 'Alden Cantrell': 12, 'Kierra Gentry': 12, 'Pierre Cox': 12},10)==False\n    assert check_value({'Cierra Vega': 12, 'Alden Cantrell': 12, 'Kierra Gentry': 12, 'Pierre Cox': 12},12)==True\n    assert check_value({'Cierra Vega': 12, 'Alden Cantrell': 12, 'Kierra Gentry': 12, 'Pierre Cox': 12},5)==False\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 800, "prompt": "Write a function to remove all whitespaces from a string.\nimport re\ndef remove_all_spaces(text):", "code": "import re\ndef remove_all_spaces(text):\n return (re.sub(r'\\s+', '',text))", "test_imports": [], "test_list": ["assert remove_all_spaces('python  program')==('pythonprogram')", "assert remove_all_spaces('python   programming    language')==('pythonprogramminglanguage')", "assert remove_all_spaces('python                     program')==('pythonprogram')", "assert remove_all_spaces('   python                     program')=='pythonprogram'"], "entry_point": "remove_all_spaces", "test": "def check():\n    assert remove_all_spaces('python  program')==('pythonprogram')\n    assert remove_all_spaces('python   programming    language')==('pythonprogramminglanguage')\n    assert remove_all_spaces('python                     program')==('pythonprogram')\n    assert remove_all_spaces('   python                     program')=='pythonprogram'\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 771, "prompt": "Write a function to check if the given expression is balanced or not. https://www.geeksforgeeks.org/check-for-balanced-parentheses-in-an-expression/\nfrom collections import deque\ndef check_expression(exp):", "code": "from collections import deque\ndef check_expression(exp):\n    if len(exp) & 1:\n        return False\n    stack = deque()\n    for ch in exp:\n        if ch == '(' or ch == '{' or ch == '[':\n            stack.append(ch)\n        if ch == ')' or ch == '}' or ch == ']':\n            if not stack:\n                return False\n            top = stack.pop()\n            if (top == '(' and ch != ')') or (top == '{' and ch != '}' or (top == '[' and ch != ']')):\n                return False\n    return not stack", "test_imports": [], "test_list": ["assert check_expression(\"{()}[{}]\") == True", "assert check_expression(\"{()}[{]\") == False", "assert check_expression(\"{()}[{}][]({})\") == True"], "entry_point": "check_expression", "test": "def check():\n    assert check_expression(\"{()}[{}]\") == True\n    assert check_expression(\"{()}[{]\") == False\n    assert check_expression(\"{()}[{}][]({})\") == True\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 597, "prompt": "Write a function to find kth element from the given two sorted arrays.\n\ndef find_kth(arr1, arr2, k):", "code": "def find_kth(arr1, arr2, k):\n\tm = len(arr1)\n\tn = len(arr2)\n\tsorted1 = [0] * (m + n)\n\ti = 0\n\tj = 0\n\td = 0\n\twhile (i < m and j < n):\n\t\tif (arr1[i] < arr2[j]):\n\t\t\tsorted1[d] = arr1[i]\n\t\t\ti += 1\n\t\telse:\n\t\t\tsorted1[d] = arr2[j]\n\t\t\tj += 1\n\t\td += 1\n\twhile (i < m):\n\t\tsorted1[d] = arr1[i]\n\t\td += 1\n\t\ti += 1\n\twhile (j < n):\n\t\tsorted1[d] = arr2[j]\n\t\td += 1\n\t\tj += 1\n\treturn sorted1[k - 1]", "test_imports": [], "test_list": ["assert find_kth([2, 3, 6, 7, 9], [1, 4, 8, 10], 5) == 6", "assert find_kth([100, 112, 256, 349, 770], [72, 86, 113, 119, 265, 445, 892], 7) == 256", "assert find_kth([3, 4, 7, 8, 10], [2, 5, 9, 11], 6) == 8"], "entry_point": "find_kth", "test": "def check():\n    assert find_kth([2, 3, 6, 7, 9], [1, 4, 8, 10], 5) == 6\n    assert find_kth([100, 112, 256, 349, 770], [72, 86, 113, 119, 265, 445, 892], 7) == 256\n    assert find_kth([3, 4, 7, 8, 10], [2, 5, 9, 11], 6) == 8\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 559, "prompt": "Write a function to find the sum of the largest contiguous sublist in the given list.\n\ndef max_sub_array_sum(a, size):", "code": "def max_sub_array_sum(a, size):\n  max_so_far = 0\n  max_ending_here = 0\n  for i in range(0, size):\n    max_ending_here = max_ending_here + a[i]\n    if max_ending_here < 0:\n      max_ending_here = 0\n    elif (max_so_far < max_ending_here):\n      max_so_far = max_ending_here\n  return max_so_far", "test_imports": [], "test_list": ["assert max_sub_array_sum([-2, -3, 4, -1, -2, 1, 5, -3], 8) == 7", "assert max_sub_array_sum([-3, -4, 5, -2, -3, 2, 6, -4], 8) == 8", "assert max_sub_array_sum([-4, -5, 6, -3, -4, 3, 7, -5], 8) == 10"], "entry_point": "max_sub_array_sum", "test": "def check():\n    assert max_sub_array_sum([-2, -3, 4, -1, -2, 1, 5, -3], 8) == 7\n    assert max_sub_array_sum([-3, -4, 5, -2, -3, 2, 6, -4], 8) == 8\n    assert max_sub_array_sum([-4, -5, 6, -3, -4, 3, 7, -5], 8) == 10\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 411, "prompt": "Write a function to convert the given snake case string to camel case string.\nimport re\ndef snake_to_camel(word):", "code": "import re\ndef snake_to_camel(word):\n  return ''.join(x.capitalize() or '_' for x in word.split('_'))", "test_imports": [], "test_list": ["assert snake_to_camel('android_tv') == 'AndroidTv'", "assert snake_to_camel('google_pixel') == 'GooglePixel'", "assert snake_to_camel('apple_watch') == 'AppleWatch'"], "entry_point": "snake_to_camel", "test": "def check():\n    assert snake_to_camel('android_tv') == 'AndroidTv'\n    assert snake_to_camel('google_pixel') == 'GooglePixel'\n    assert snake_to_camel('apple_watch') == 'AppleWatch'\n"}
{"source_file": "Ellen's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 291, "prompt": "Write a function to find out the number of ways of painting the fence such that at most 2 adjacent posts have the same color for the given fence with n posts and k colors.\n\ndef count_no_of_ways(n, k): ", "code": "def count_no_of_ways(n, k): \n\tdp = [0] * (n + 1) \n\ttotal = k \n\tmod = 1000000007\n\tdp[1] = k \n\tdp[2] = k * k\t \n\tfor i in range(3,n+1): \n\t\tdp[i] = ((k - 1) * (dp[i - 1] + dp[i - 2])) % mod \n\treturn dp[n]", "test_imports": [], "test_list": ["assert count_no_of_ways(2, 4) == 16", "assert count_no_of_ways(3, 2) == 6", "assert count_no_of_ways(4, 4) == 228"], "entry_point": "count_no_of_ways", "test": "def check():\n    assert count_no_of_ways(2, 4) == 16\n    assert count_no_of_ways(3, 2) == 6\n    assert count_no_of_ways(4, 4) == 228\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 89, "prompt": "Write a function to find the closest smaller number than n.\n\ndef closest_num(N):", "code": "def closest_num(N):\n  return (N - 1)", "test_imports": [], "test_list": ["assert closest_num(11) == 10", "assert closest_num(7) == 6", "assert closest_num(12) == 11"], "entry_point": "closest_num", "test": "def check():\n    assert closest_num(11) == 10\n    assert closest_num(7) == 6\n    assert closest_num(12) == 11\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 429, "prompt": "Write a function to extract the elementwise and tuples from the given two tuples.\n\ndef and_tuples(test_tup1, test_tup2):", "code": "def and_tuples(test_tup1, test_tup2):\n  res = tuple(ele1 & ele2 for ele1, ele2 in zip(test_tup1, test_tup2))\n  return (res) ", "test_imports": [], "test_list": ["assert and_tuples((10, 4, 6, 9), (5, 2, 3, 3)) == (0, 0, 2, 1)", "assert and_tuples((1, 2, 3, 4), (5, 6, 7, 8)) == (1, 2, 3, 0)", "assert and_tuples((8, 9, 11, 12), (7, 13, 14, 17)) == (0, 9, 10, 0)"], "entry_point": "and_tuples", "test": "def check():\n    assert and_tuples((10, 4, 6, 9), (5, 2, 3, 3)) == (0, 0, 2, 1)\n    assert and_tuples((1, 2, 3, 4), (5, 6, 7, 8)) == (1, 2, 3, 0)\n    assert and_tuples((8, 9, 11, 12), (7, 13, 14, 17)) == (0, 9, 10, 0)\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 398, "prompt": "Write a function to compute the sum of digits of each number of a given list.\n\ndef sum_of_digits(nums):", "code": "def sum_of_digits(nums):\n    return sum(int(el) for n in nums for el in str(n) if el.isdigit())", "test_imports": [], "test_list": ["assert sum_of_digits([10,2,56])==14", "assert sum_of_digits([[10,20,4,5,'b',70,'a']])==19", "assert sum_of_digits([10,20,-4,5,-70])==19"], "entry_point": "sum_of_digits", "test": "def check():\n    assert sum_of_digits([10,2,56])==14\n    assert sum_of_digits([[10,20,4,5,'b',70,'a']])==19\n    assert sum_of_digits([10,20,-4,5,-70])==19\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 604, "prompt": "Write a function to reverse words seperated by spaces in a given string.\n\ndef reverse_words(s):", "code": "def reverse_words(s):\n        return ' '.join(reversed(s.split()))", "test_imports": [], "test_list": ["assert reverse_words(\"python program\")==(\"program python\")", "assert reverse_words(\"java language\")==(\"language java\")", "assert reverse_words(\"indian man\")==(\"man indian\")"], "entry_point": "reverse_words", "test": "def check():\n    assert reverse_words(\"python program\")==(\"program python\")\n    assert reverse_words(\"java language\")==(\"language java\")\n    assert reverse_words(\"indian man\")==(\"man indian\")\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 475, "prompt": "Write a function to sort a dictionary by value.\nfrom collections import Counter\ndef sort_counter(dict1):", "code": "from collections import Counter\ndef sort_counter(dict1):\n x = Counter(dict1)\n sort_counter=x.most_common()\n return sort_counter", "test_imports": [], "test_list": ["assert sort_counter({'Math':81, 'Physics':83, 'Chemistry':87})==[('Chemistry', 87), ('Physics', 83), ('Math', 81)]", "assert sort_counter({'Math':400, 'Physics':300, 'Chemistry':250})==[('Math', 400), ('Physics', 300), ('Chemistry', 250)]", "assert sort_counter({'Math':900, 'Physics':1000, 'Chemistry':1250})==[('Chemistry', 1250), ('Physics', 1000), ('Math', 900)]"], "entry_point": "sort_counter", "test": "def check():\n    assert sort_counter({'Math':81, 'Physics':83, 'Chemistry':87})==[('Chemistry', 87), ('Physics', 83), ('Math', 81)]\n    assert sort_counter({'Math':400, 'Physics':300, 'Chemistry':250})==[('Math', 400), ('Physics', 300), ('Chemistry', 250)]\n    assert sort_counter({'Math':900, 'Physics':1000, 'Chemistry':1250})==[('Chemistry', 1250), ('Physics', 1000), ('Math', 900)]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 224, "prompt": "Write a python function to count the number of set bits (binary digits with value 1) in a given number.\n\ndef count_Set_Bits(n): ", "code": "def count_Set_Bits(n): \n    count = 0\n    while (n): \n        count += n & 1\n        n >>= 1\n    return count ", "test_imports": [], "test_list": ["assert count_Set_Bits(2) == 1", "assert count_Set_Bits(4) == 1", "assert count_Set_Bits(6) == 2"], "entry_point": "count_Set_Bits", "test": "def check():\n    assert count_Set_Bits(2) == 1\n    assert count_Set_Bits(4) == 1\n    assert count_Set_Bits(6) == 2\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 99, "prompt": "Write a function to convert the given decimal number to its binary equivalent, represented as a string with no leading zeros.\n\ndef decimal_to_binary(n): ", "code": "def decimal_to_binary(n): \n    return bin(n).replace(\"0b\",\"\") ", "test_imports": [], "test_list": ["assert decimal_to_binary(8) == '1000'", "assert decimal_to_binary(18) == '10010'", "assert decimal_to_binary(7) == '111'"], "entry_point": "decimal_to_binary", "test": "def check():\n    assert decimal_to_binary(8) == '1000'\n    assert decimal_to_binary(18) == '10010'\n    assert decimal_to_binary(7) == '111'\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 3, "prompt": "Write a python function to identify non-prime numbers.\nimport math\ndef is_not_prime(n):", "code": "import math\ndef is_not_prime(n):\n    result = False\n    for i in range(2,int(math.sqrt(n)) + 1):\n        if n % i == 0:\n            result = True\n    return result", "test_imports": [], "test_list": ["assert is_not_prime(2) == False", "assert is_not_prime(10) == True", "assert is_not_prime(35) == True", "assert is_not_prime(37) == False"], "entry_point": "is_not_prime", "test": "def check():\n    assert is_not_prime(2) == False\n    assert is_not_prime(10) == True\n    assert is_not_prime(35) == True\n    assert is_not_prime(37) == False\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 794, "prompt": "Write a function that matches a string that has an 'a' followed by anything, ending in 'b'.\nimport re\ndef text_starta_endb(text):", "code": "import re\ndef text_starta_endb(text):\n        patterns = 'a.*?b$'\n        return re.search(patterns,  text)", "test_imports": [], "test_list": ["assert text_starta_endb(\"aabbbb\")", "assert not text_starta_endb(\"aabAbbbc\")", "assert not text_starta_endb(\"accddbbjjj\")"], "entry_point": "text_starta_endb", "test": "def check():\n    assert text_starta_endb(\"aabbbb\")\n    assert not text_starta_endb(\"aabAbbbc\")\n    assert not text_starta_endb(\"accddbbjjj\")\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 95, "prompt": "Write a python function to find the length of the smallest list in a list of lists.\n\ndef Find_Min_Length(lst):  ", "code": "def Find_Min_Length(lst):  \n    minLength = min(len(x) for x in lst )\n    return minLength ", "test_imports": [], "test_list": ["assert Find_Min_Length([[1],[1,2]]) == 1", "assert Find_Min_Length([[1,2],[1,2,3],[1,2,3,4]]) == 2", "assert Find_Min_Length([[3,3,3],[4,4,4,4]]) == 3"], "entry_point": "Find_Min_Length", "test": "def check():\n    assert Find_Min_Length([[1],[1,2]]) == 1\n    assert Find_Min_Length([[1,2],[1,2,3],[1,2,3,4]]) == 2\n    assert Find_Min_Length([[3,3,3],[4,4,4,4]]) == 3\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 132, "prompt": "Write a function to convert a tuple to a string.\n\ndef tup_string(tup1):", "code": "def tup_string(tup1):\n  str =  ''.join(tup1)\n  return str", "test_imports": [], "test_list": ["assert tup_string(('e', 'x', 'e', 'r', 'c', 'i', 's', 'e', 's'))==(\"exercises\")", "assert tup_string(('p','y','t','h','o','n'))==(\"python\")", "assert tup_string(('p','r','o','g','r','a','m'))==(\"program\")"], "entry_point": "tup_string", "test": "def check():\n    assert tup_string(('e', 'x', 'e', 'r', 'c', 'i', 's', 'e', 's'))==(\"exercises\")\n    assert tup_string(('p','y','t','h','o','n'))==(\"python\")\n    assert tup_string(('p','r','o','g','r','a','m'))==(\"program\")\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 415, "prompt": "Write a python function to find a pair with highest product from a given array of integers.\n\ndef max_Product(arr): ", "code": "def max_Product(arr): \n    arr_len = len(arr) \n    if (arr_len < 2): \n        return (\"No pairs exists\")           \n    x = arr[0]; y = arr[1]      \n    for i in range(0,arr_len): \n        for j in range(i + 1,arr_len): \n            if (arr[i] * arr[j] > x * y): \n                x = arr[i]; y = arr[j] \n    return x,y    ", "test_imports": [], "test_list": ["assert max_Product([1,2,3,4,7,0,8,4]) == (7,8)", "assert max_Product([0,-1,-2,-4,5,0,-6]) == (-4,-6)", "assert max_Product([1,2,3]) == (2,3)"], "entry_point": "max_Product", "test": "def check():\n    assert max_Product([1,2,3,4,7,0,8,4]) == (7,8)\n    assert max_Product([0,-1,-2,-4,5,0,-6]) == (-4,-6)\n    assert max_Product([1,2,3]) == (2,3)\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 79, "prompt": "Write a python function to check whether the length of the word is odd or not.\n\ndef word_len(s): ", "code": "def word_len(s): \n    s = s.split(' ')   \n    for word in s:    \n        if len(word)%2!=0: \n            return True  \n        else:\n          return False", "test_imports": [], "test_list": ["assert word_len(\"Hadoop\") == False", "assert word_len(\"great\") == True", "assert word_len(\"structure\") == True"], "entry_point": "word_len", "test": "def check():\n    assert word_len(\"Hadoop\") == False\n    assert word_len(\"great\") == True\n    assert word_len(\"structure\") == True\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 479, "prompt": "Write a python function to find the first digit of a given number.\n\ndef first_Digit(n) :  ", "code": "def first_Digit(n) :  \n    while n >= 10:  \n        n = n / 10 \n    return int(n) ", "test_imports": [], "test_list": ["assert first_Digit(123) == 1", "assert first_Digit(456) == 4", "assert first_Digit(12) == 1"], "entry_point": "first_Digit", "test": "def check():\n    assert first_Digit(123) == 1\n    assert first_Digit(456) == 4\n    assert first_Digit(12) == 1\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 744, "prompt": "Write a function to check if the given tuple has any none value or not.\n\ndef check_none(test_tup):", "code": "def check_none(test_tup):\n  res = any(map(lambda ele: ele is None, test_tup))\n  return res ", "test_imports": [], "test_list": ["assert check_none((10, 4, 5, 6, None)) == True", "assert check_none((7, 8, 9, 11, 14)) == False", "assert check_none((1, 2, 3, 4, None)) == True"], "entry_point": "check_none", "test": "def check():\n    assert check_none((10, 4, 5, 6, None)) == True\n    assert check_none((7, 8, 9, 11, 14)) == False\n    assert check_none((1, 2, 3, 4, None)) == True\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 160, "prompt": "Write a function that returns integers x and y that satisfy ax + by = n as a tuple, or return None if no solution exists.\n\ndef find_solution(a, b, n):", "code": "def find_solution(a, b, n):\n\ti = 0\n\twhile i * a <= n:\n\t\tif (n - (i * a)) % b == 0: \n\t\t\treturn (i, (n - (i * a)) // b)\n\t\ti = i + 1\n\treturn None", "test_imports": [], "test_list": ["assert find_solution(2, 3, 7) == (2, 1)", "assert find_solution(4, 2, 7) == None", "assert find_solution(1, 13, 17) == (4, 1)"], "entry_point": "find_solution", "test": "def check():\n    assert find_solution(2, 3, 7) == (2, 1)\n    assert find_solution(4, 2, 7) == None\n    assert find_solution(1, 13, 17) == (4, 1)\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 639, "prompt": "Write a function to sum the length of the names of a given list of names after removing the names that start with a lowercase letter.\n\ndef sample_nam(sample_names):", "code": "def sample_nam(sample_names):\n  sample_names=list(filter(lambda el:el[0].isupper() and el[1:].islower(),sample_names))\n  return len(''.join(sample_names))", "test_imports": [], "test_list": ["assert sample_nam(['sally', 'Dylan', 'rebecca', 'Diana', 'Joanne', 'keith'])==16", "assert sample_nam([\"php\", \"res\", \"Python\", \"abcd\", \"Java\", \"aaa\"])==10", "assert sample_nam([\"abcd\", \"Python\", \"abba\", \"aba\"])==6"], "entry_point": "sample_nam", "test": "def check():\n    assert sample_nam(['sally', 'Dylan', 'rebecca', 'Diana', 'Joanne', 'keith'])==16\n    assert sample_nam([\"php\", \"res\", \"Python\", \"abcd\", \"Java\", \"aaa\"])==10\n    assert sample_nam([\"abcd\", \"Python\", \"abba\", \"aba\"])==6\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 98, "prompt": "Write a function to multiply all the numbers in a list and divide with the length of the list.\n\ndef multiply_num(numbers):  ", "code": "def multiply_num(numbers):  \n    total = 1\n    for x in numbers:\n        total *= x  \n    return total/len(numbers) ", "test_imports": ["import math"], "test_list": ["assert math.isclose(multiply_num((8, 2, 3, -1, 7)), -67.2, rel_tol=0.001)", "assert math.isclose(multiply_num((-10,-20,-30)), -2000.0, rel_tol=0.001)", "assert math.isclose(multiply_num((19,15,18)), 1710.0, rel_tol=0.001)"], "entry_point": "multiply_num", "test": "def check():\n    assert math.isclose(multiply_num((8, 2, 3, -1, 7)), -67.2, rel_tol=0.001)\n    assert math.isclose(multiply_num((-10,-20,-30)), -2000.0, rel_tol=0.001)\n    assert math.isclose(multiply_num((19,15,18)), 1710.0, rel_tol=0.001)\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 721, "prompt": "Given a square matrix of size N*N given as a list of lists, where each cell is associated with a specific cost. A path is defined as a specific sequence of cells that starts from the top-left cell move only right or down and ends on bottom right cell. We want to find a path with the maximum average over all existing paths. Average is computed as total cost divided by the number of cells visited in the path.\n\ndef maxAverageOfPath(cost):", "code": "def maxAverageOfPath(cost):\n  N = len(cost)\n  dp = [[0 for i in range(N + 1)] for j in range(N + 1)]\n  dp[0][0] = cost[0][0]\n  for i in range(1, N):\n    dp[i][0] = dp[i - 1][0] + cost[i][0]\n  for j in range(1, N):\n    dp[0][j] = dp[0][j - 1] + cost[0][j]\n  for i in range(1, N):\n    for j in range(1, N):\n      dp[i][j] = max(dp[i - 1][j],\n                     dp[i][j - 1]) + cost[i][j]\n  return dp[N - 1][N - 1] / (2 * N - 1)", "test_imports": [], "test_list": ["assert maxAverageOfPath([[1, 2, 3], [6, 5, 4], [7, 3, 9]]) == 5.2", "assert maxAverageOfPath([[2, 3, 4], [7, 6, 5], [8, 4, 10]]) == 6.2", "assert maxAverageOfPath([[3, 4, 5], [8, 7, 6], [9, 5, 11]]) == 7.2", "assert maxAverageOfPath([[1, 2, 3], [4, 5, 6], [7, 8, 9]]) == 5.8"], "entry_point": "maxAverageOfPath", "test": "def check():\n    assert maxAverageOfPath([[1, 2, 3], [6, 5, 4], [7, 3, 9]]) == 5.2\n    assert maxAverageOfPath([[2, 3, 4], [7, 6, 5], [8, 4, 10]]) == 6.2\n    assert maxAverageOfPath([[3, 4, 5], [8, 7, 6], [9, 5, 11]]) == 7.2\n    assert maxAverageOfPath([[1, 2, 3], [4, 5, 6], [7, 8, 9]]) == 5.8\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 554, "prompt": "Write a python function which takes a list of integers and only returns the odd ones.\n\ndef Split(list): ", "code": "def Split(list): \n    od_li = [] \n    for i in list: \n        if (i % 2 != 0): \n            od_li.append(i)  \n    return od_li", "test_imports": [], "test_list": ["assert Split([1,2,3,4,5,6]) == [1,3,5]", "assert Split([10,11,12,13]) == [11,13]", "assert Split([7,8,9,1]) == [7,9,1]"], "entry_point": "Split", "test": "def check():\n    assert Split([1,2,3,4,5,6]) == [1,3,5]\n    assert Split([10,11,12,13]) == [11,13]\n    assert Split([7,8,9,1]) == [7,9,1]\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 454, "prompt": "Write a function that matches a word containing 'z'.\nimport re\ndef text_match_wordz(text):", "code": "import re\ndef text_match_wordz(text):\n        patterns = '\\w*z.\\w*'\n        if re.search(patterns,  text):\n                return True\n        else:\n                return False", "test_imports": [], "test_list": ["assert text_match_wordz(\"pythonz.\")==True", "assert text_match_wordz(\"xyz.\")==True", "assert text_match_wordz(\"  lang  .\")==False"], "entry_point": "text_match_wordz", "test": "def check():\n    assert text_match_wordz(\"pythonz.\")==True\n    assert text_match_wordz(\"xyz.\")==True\n    assert text_match_wordz(\"  lang  .\")==False\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 19, "prompt": "Write a function to find whether a given array of integers contains any duplicate element.\n\ndef test_duplicate(arraynums):", "code": "def test_duplicate(arraynums):\n    nums_set = set(arraynums)    \n    return len(arraynums) != len(nums_set)     ", "test_imports": [], "test_list": ["assert test_duplicate(([1,2,3,4,5]))==False", "assert test_duplicate(([1,2,3,4, 4]))==True", "assert test_duplicate([1,1,2,2,3,3,4,4,5])==True"], "entry_point": "test_duplicate", "test": "def check():\n    assert test_duplicate(([1,2,3,4,5]))==False\n    assert test_duplicate(([1,2,3,4, 4]))==True\n    assert test_duplicate([1,1,2,2,3,3,4,4,5])==True\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 611, "prompt": "Write a function which given a matrix represented as a list of lists returns the max of the n'th column.\n\ndef max_of_nth(test_list, N):", "code": "def max_of_nth(test_list, N):\n  res = max([sub[N] for sub in test_list])\n  return (res) ", "test_imports": [], "test_list": ["assert max_of_nth([[5, 6, 7], [1, 3, 5], [8, 9, 19]], 2) == 19", "assert max_of_nth([[6, 7, 8], [2, 4, 6], [9, 10, 20]], 1) == 10", "assert max_of_nth([[7, 8, 9], [3, 5, 7], [10, 11, 21]], 1) == 11"], "entry_point": "max_of_nth", "test": "def check():\n    assert max_of_nth([[5, 6, 7], [1, 3, 5], [8, 9, 19]], 2) == 19\n    assert max_of_nth([[6, 7, 8], [2, 4, 6], [9, 10, 20]], 1) == 10\n    assert max_of_nth([[7, 8, 9], [3, 5, 7], [10, 11, 21]], 1) == 11\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 142, "prompt": "Write a function to count number items that are identical in the same position of three given lists.\n\ndef count_samepair(list1,list2,list3):", "code": "def count_samepair(list1,list2,list3):\n    result = sum(m == n == o for m, n, o in zip(list1,list2,list3))\n    return result", "test_imports": [], "test_list": ["assert count_samepair([1,2,3,4,5,6,7,8],[2,2,3,1,2,6,7,9],[2,1,3,1,2,6,7,9])==3", "assert count_samepair([1,2,3,4,5,6,7,8],[2,2,3,1,2,6,7,8],[2,1,3,1,2,6,7,8])==4", "assert count_samepair([1,2,3,4,2,6,7,8],[2,2,3,1,2,6,7,8],[2,1,3,1,2,6,7,8])==5"], "entry_point": "count_samepair", "test": "def check():\n    assert count_samepair([1,2,3,4,5,6,7,8],[2,2,3,1,2,6,7,9],[2,1,3,1,2,6,7,9])==3\n    assert count_samepair([1,2,3,4,5,6,7,8],[2,2,3,1,2,6,7,8],[2,1,3,1,2,6,7,8])==4\n    assert count_samepair([1,2,3,4,2,6,7,8],[2,2,3,1,2,6,7,8],[2,1,3,1,2,6,7,8])==5\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 564, "prompt": "Write a python function which takes a list of integers and counts the number of possible unordered pairs where both elements are unequal.\n\ndef count_Pairs(arr,n): ", "code": "def count_Pairs(arr,n): \n    cnt = 0; \n    for i in range(n): \n        for j in range(i + 1,n): \n            if (arr[i] != arr[j]): \n                cnt += 1; \n    return cnt; ", "test_imports": [], "test_list": ["assert count_Pairs([1,2,1],3) == 2", "assert count_Pairs([1,1,1,1],4) == 0", "assert count_Pairs([1,2,3,4,5],5) == 10"], "entry_point": "count_Pairs", "test": "def check():\n    assert count_Pairs([1,2,1],3) == 2\n    assert count_Pairs([1,1,1,1],4) == 0\n    assert count_Pairs([1,2,3,4,5],5) == 10\n"}
{"source_file": "Ellen's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 296, "prompt": "Write a python function to count inversions in an array.\n\ndef get_Inv_Count(arr): ", "code": "def get_Inv_Count(arr): \n    inv_count = 0\n    for i in range(len(arr)): \n        for j in range(i + 1, len(arr)): \n            if (arr[i] > arr[j]): \n                inv_count += 1\n    return inv_count ", "test_imports": [], "test_list": ["assert get_Inv_Count([1,20,6,4,5]) == 5", "assert get_Inv_Count([1,2,1]) == 1", "assert get_Inv_Count([1,2,5,6,1]) == 3"], "entry_point": "get_Inv_Count", "test": "def check():\n    assert get_Inv_Count([1,20,6,4,5]) == 5\n    assert get_Inv_Count([1,2,1]) == 1\n    assert get_Inv_Count([1,2,5,6,1]) == 3\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 626, "prompt": "Write a python function to find the area of the largest triangle that can be inscribed in a semicircle with a given radius.\n\ndef triangle_area(r) :  ", "code": "def triangle_area(r) :  \n    if r < 0 : \n        return None\n    return r * r ", "test_imports": [], "test_list": ["assert triangle_area(-1) == None", "assert triangle_area(0) == 0", "assert triangle_area(2) == 4"], "entry_point": "triangle_area", "test": "def check():\n    assert triangle_area(-1) == None\n    assert triangle_area(0) == 0\n    assert triangle_area(2) == 4\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 803, "prompt": "Write a function to check whether the given number is a perfect square or not. https://www.geeksforgeeks.org/check-if-given-number-is-perfect-square-in-cpp/\n\ndef is_perfect_square(n) :", "code": "def is_perfect_square(n) :\n    i = 1\n    while (i * i<= n):\n        if ((n % i == 0) and (n / i == i)):\n            return True     \n        i = i + 1\n    return False", "test_imports": [], "test_list": ["assert not is_perfect_square(10)", "assert is_perfect_square(36)", "assert not is_perfect_square(14)", "assert is_perfect_square(14*14)", "assert not is_perfect_square(125)", "assert is_perfect_square(125*125)"], "entry_point": "is_perfect_square", "test": "def check():\n    assert not is_perfect_square(10)\n    assert is_perfect_square(36)\n    assert not is_perfect_square(14)\n    assert is_perfect_square(14*14)\n    assert not is_perfect_square(125)\n    assert is_perfect_square(125*125)\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 260, "prompt": "Write a function to find the nth newman\u2013shanks\u2013williams prime number.\n\ndef newman_prime(n): ", "code": "def newman_prime(n): \n\tif n == 0 or n == 1: \n\t\treturn 1\n\treturn 2 * newman_prime(n - 1) + newman_prime(n - 2)", "test_imports": [], "test_list": ["assert newman_prime(3) == 7", "assert newman_prime(4) == 17", "assert newman_prime(5) == 41"], "entry_point": "newman_prime", "test": "def check():\n    assert newman_prime(3) == 7\n    assert newman_prime(4) == 17\n    assert newman_prime(5) == 41\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 600, "prompt": "Write a python function to check whether the given number is even or not.\n\ndef is_Even(n) : ", "code": "def is_Even(n) : \n    if (n^1 == n+1) :\n        return True; \n    else :\n        return False; ", "test_imports": [], "test_list": ["assert is_Even(1) == False", "assert is_Even(2) == True", "assert is_Even(3) == False"], "entry_point": "is_Even", "test": "def check():\n    assert is_Even(1) == False\n    assert is_Even(2) == True\n    assert is_Even(3) == False\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 792, "prompt": "Write a python function to count the number of lists in a given number of lists.\n\ndef count_list(input_list): ", "code": "def count_list(input_list): \n    return len(input_list)", "test_imports": [], "test_list": ["assert count_list([[1, 3], [5, 7], [9, 11], [13, 15, 17]]) == 4", "assert count_list([[1,2],[2,3],[4,5]]) == 3", "assert count_list([[1,0],[2,0]]) == 2"], "entry_point": "count_list", "test": "def check():\n    assert count_list([[1, 3], [5, 7], [9, 11], [13, 15, 17]]) == 4\n    assert count_list([[1,2],[2,3],[4,5]]) == 3\n    assert count_list([[1,0],[2,0]]) == 2\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 616, "prompt": "Write a function which takes two tuples of the same length and performs the element wise modulo.\n\ndef tuple_modulo(test_tup1, test_tup2):", "code": "def tuple_modulo(test_tup1, test_tup2):\n  res = tuple(ele1 % ele2 for ele1, ele2 in zip(test_tup1, test_tup2)) \n  return (res) ", "test_imports": [], "test_list": ["assert tuple_modulo((10, 4, 5, 6), (5, 6, 7, 5)) == (0, 4, 5, 1)", "assert tuple_modulo((11, 5, 6, 7), (6, 7, 8, 6)) == (5, 5, 6, 1)", "assert tuple_modulo((12, 6, 7, 8), (7, 8, 9, 7)) == (5, 6, 7, 1)"], "entry_point": "tuple_modulo", "test": "def check():\n    assert tuple_modulo((10, 4, 5, 6), (5, 6, 7, 5)) == (0, 4, 5, 1)\n    assert tuple_modulo((11, 5, 6, 7), (6, 7, 8, 6)) == (5, 5, 6, 1)\n    assert tuple_modulo((12, 6, 7, 8), (7, 8, 9, 7)) == (5, 6, 7, 1)\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 456, "prompt": "Write a function to reverse each string in a given list of string values.\n\ndef reverse_string_list(stringlist):", "code": "def reverse_string_list(stringlist):\n    result = [x[::-1] for x in stringlist]\n    return result", "test_imports": [], "test_list": ["assert reverse_string_list(['Red', 'Green', 'Blue', 'White', 'Black'])==['deR', 'neerG', 'eulB', 'etihW', 'kcalB']", "assert reverse_string_list(['john','amal','joel','george'])==['nhoj','lama','leoj','egroeg']", "assert reverse_string_list(['jack','john','mary'])==['kcaj','nhoj','yram']"], "entry_point": "reverse_string_list", "test": "def check():\n    assert reverse_string_list(['Red', 'Green', 'Blue', 'White', 'Black'])==['deR', 'neerG', 'eulB', 'etihW', 'kcalB']\n    assert reverse_string_list(['john','amal','joel','george'])==['nhoj','lama','leoj','egroeg']\n    assert reverse_string_list(['jack','john','mary'])==['kcaj','nhoj','yram']\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 100, "prompt": "Write a function to find the next smallest palindrome of a specified integer, returned as an integer.\nimport sys\ndef next_smallest_palindrome(num):", "code": "import sys\ndef next_smallest_palindrome(num):\n    numstr = str(num)\n    for i in range(num+1,sys.maxsize):\n        if str(i) == str(i)[::-1]:\n            return i", "test_imports": [], "test_list": ["assert next_smallest_palindrome(99)==101", "assert next_smallest_palindrome(1221)==1331", "assert next_smallest_palindrome(120)==121"], "entry_point": "next_smallest_palindrome", "test": "def check():\n    assert next_smallest_palindrome(99)==101\n    assert next_smallest_palindrome(1221)==1331\n    assert next_smallest_palindrome(120)==121\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 741, "prompt": "Write a python function to check whether all the characters are same or not.\n\ndef all_Characters_Same(s) :", "code": "def all_Characters_Same(s) :\n    n = len(s)\n    for i in range(1,n) :\n        if s[i] != s[0] :\n            return False\n    return True", "test_imports": [], "test_list": ["assert all_Characters_Same(\"python\") == False", "assert all_Characters_Same(\"aaa\") == True", "assert all_Characters_Same(\"data\") == False"], "entry_point": "all_Characters_Same", "test": "def check():\n    assert all_Characters_Same(\"python\") == False\n    assert all_Characters_Same(\"aaa\") == True\n    assert all_Characters_Same(\"data\") == False\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 96, "prompt": "Write a python function to find the number of divisors of a given integer.\n\ndef divisor(n):", "code": "def divisor(n):\n  for i in range(n):\n    x = len([i for i in range(1,n+1) if not n % i])\n  return x", "test_imports": [], "test_list": ["assert divisor(15) == 4", "assert divisor(12) == 6", "assert divisor(9) == 3"], "entry_point": "divisor", "test": "def check():\n    assert divisor(15) == 4\n    assert divisor(12) == 6\n    assert divisor(9) == 3\n"}
{"source_file": "Ellen's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 307, "prompt": "Write a function to get a colon of a tuple.\nfrom copy import deepcopy\ndef colon_tuplex(tuplex,m,n):", "code": "from copy import deepcopy\ndef colon_tuplex(tuplex,m,n):\n  tuplex_colon = deepcopy(tuplex)\n  tuplex_colon[m].append(n)\n  return tuplex_colon", "test_imports": [], "test_list": ["assert colon_tuplex((\"HELLO\", 5, [], True) ,2,50)==(\"HELLO\", 5, [50], True)", "assert colon_tuplex((\"HELLO\", 5, [], True) ,2,100)==((\"HELLO\", 5, [100],True))", "assert colon_tuplex((\"HELLO\", 5, [], True) ,2,500)==(\"HELLO\", 5, [500], True)"], "entry_point": "colon_tuplex", "test": "def check():\n    assert colon_tuplex((\"HELLO\", 5, [], True) ,2,50)==(\"HELLO\", 5, [50], True)\n    assert colon_tuplex((\"HELLO\", 5, [], True) ,2,100)==((\"HELLO\", 5, [100],True))\n    assert colon_tuplex((\"HELLO\", 5, [], True) ,2,500)==(\"HELLO\", 5, [500], True)\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 397, "prompt": "Write a function to find the median of three numbers.\n\ndef median_numbers(a,b,c):", "code": "def median_numbers(a,b,c):\n if a > b:\n    if a < c:\n        median = a\n    elif b > c:\n        median = b\n    else:\n        median = c\n else:\n    if a > c:\n        median = a\n    elif b < c:\n        median = b\n    else:\n        median = c\n return median", "test_imports": [], "test_list": ["assert median_numbers(25,55,65)==55.0", "assert median_numbers(20,10,30)==20.0", "assert median_numbers(15,45,75)==45.0"], "entry_point": "median_numbers", "test": "def check():\n    assert median_numbers(25,55,65)==55.0\n    assert median_numbers(20,10,30)==20.0\n    assert median_numbers(15,45,75)==45.0\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 807, "prompt": "Write a python function to find the first odd number in a given list of numbers.\n\ndef first_odd(nums):", "code": "def first_odd(nums):\n  first_odd = next((el for el in nums if el%2!=0),-1)\n  return first_odd", "test_imports": [], "test_list": ["assert first_odd([1,3,5]) == 1", "assert first_odd([2,4,1,3]) == 1", "assert first_odd ([8,9,1]) == 9"], "entry_point": "first_odd", "test": "def check():\n    assert first_odd([1,3,5]) == 1\n    assert first_odd([2,4,1,3]) == 1\n    assert first_odd ([8,9,1]) == 9\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 395, "prompt": "Write a python function to find the first non-repeated character in a given string.\n\ndef first_non_repeating_character(str1):", "code": "def first_non_repeating_character(str1):\n  char_order = []\n  ctr = {}\n  for c in str1:\n    if c in ctr:\n      ctr[c] += 1\n    else:\n      ctr[c] = 1 \n      char_order.append(c)\n  for c in char_order:\n    if ctr[c] == 1:\n      return c\n  return None", "test_imports": [], "test_list": ["assert first_non_repeating_character(\"abcabc\") == None", "assert first_non_repeating_character(\"abc\") == \"a\"", "assert first_non_repeating_character(\"ababc\") == \"c\""], "entry_point": "first_non_repeating_character", "test": "def check():\n    assert first_non_repeating_character(\"abcabc\") == None\n    assert first_non_repeating_character(\"abc\") == \"a\"\n    assert first_non_repeating_character(\"ababc\") == \"c\"\n"}
{"source_file": "Ellen's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 300, "prompt": "Write a function to find the count of all binary sequences of length 2n such that sum of first n bits is same as sum of last n bits.\n\ndef count_binary_seq(n): ", "code": "def count_binary_seq(n): \n\tnCr = 1\n\tres = 1\n\tfor r in range(1, n + 1): \n\t\tnCr = (nCr * (n + 1 - r)) / r \n\t\tres += nCr * nCr \n\treturn res ", "test_imports": ["import math"], "test_list": ["assert math.isclose(count_binary_seq(1), 2.0, rel_tol=0.001)", "assert math.isclose(count_binary_seq(2), 6.0, rel_tol=0.001)", "assert math.isclose(count_binary_seq(3), 20.0, rel_tol=0.001)"], "entry_point": "count_binary_seq", "test": "def check():\n    assert math.isclose(count_binary_seq(1), 2.0, rel_tol=0.001)\n    assert math.isclose(count_binary_seq(2), 6.0, rel_tol=0.001)\n    assert math.isclose(count_binary_seq(3), 20.0, rel_tol=0.001)\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 104, "prompt": "Write a function to sort each sublist of strings in a given list of lists.\n\ndef sort_sublists(input_list):", "code": "def sort_sublists(input_list):\n    result = [sorted(x, key = lambda x:x[0]) for x in input_list] \n    return result\n", "test_imports": [], "test_list": ["assert sort_sublists(([\"green\", \"orange\"], [\"black\", \"white\"], [\"white\", \"black\", \"orange\"]))==[['green', 'orange'], ['black', 'white'], ['black', 'orange', 'white']]", "assert sort_sublists(([\" red \",\"green\" ],[\"blue \",\" black\"],[\" orange\",\"brown\"]))==[[' red ', 'green'], [' black', 'blue '], [' orange', 'brown']]", "assert sort_sublists(([\"zilver\",\"gold\"], [\"magnesium\",\"aluminium\"], [\"steel\", \"bronze\"]))==[['gold', 'zilver'],['aluminium', 'magnesium'], ['bronze', 'steel']]"], "entry_point": "sort_sublists", "test": "def check():\n    assert sort_sublists(([\"green\", \"orange\"], [\"black\", \"white\"], [\"white\", \"black\", \"orange\"]))==[['green', 'orange'], ['black', 'white'], ['black', 'orange', 'white']]\n    assert sort_sublists(([\" red \",\"green\" ],[\"blue \",\" black\"],[\" orange\",\"brown\"]))==[[' red ', 'green'], [' black', 'blue '], [' orange', 'brown']]\n    assert sort_sublists(([\"zilver\",\"gold\"], [\"magnesium\",\"aluminium\"], [\"steel\", \"bronze\"]))==[['gold', 'zilver'],['aluminium', 'magnesium'], ['bronze', 'steel']]\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 94, "prompt": "Given a list of tuples, write a function that returns the first value of the tuple with the smallest second value.\nfrom operator import itemgetter \ndef index_minimum(test_list):", "code": "from operator import itemgetter \ndef index_minimum(test_list):\n  res = min(test_list, key = itemgetter(1))[0]\n  return (res) ", "test_imports": [], "test_list": ["assert index_minimum([('Rash', 143), ('Manjeet', 200), ('Varsha', 100)]) == 'Varsha'", "assert index_minimum([('Yash', 185), ('Dawood', 125), ('Sanya', 175)]) == 'Dawood'", "assert index_minimum([('Sai', 345), ('Salman', 145), ('Ayesha', 96)]) == 'Ayesha'"], "entry_point": "index_minimum", "test": "def check():\n    assert index_minimum([('Rash', 143), ('Manjeet', 200), ('Varsha', 100)]) == 'Varsha'\n    assert index_minimum([('Yash', 185), ('Dawood', 125), ('Sanya', 175)]) == 'Dawood'\n    assert index_minimum([('Sai', 345), ('Salman', 145), ('Ayesha', 96)]) == 'Ayesha'\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 720, "prompt": "Write a function to add a dictionary to the tuple. The output should be a tuple.\n\ndef add_dict_to_tuple(test_tup, test_dict):", "code": "def add_dict_to_tuple(test_tup, test_dict):\n  test_tup = list(test_tup)\n  test_tup.append(test_dict)\n  test_tup = tuple(test_tup)\n  return (test_tup) ", "test_imports": [], "test_list": ["assert add_dict_to_tuple((4, 5, 6), {\"MSAM\" : 1, \"is\" : 2, \"best\" : 3} ) == (4, 5, 6, {'MSAM': 1, 'is': 2, 'best': 3})", "assert add_dict_to_tuple((1, 2, 3), {\"UTS\" : 2, \"is\" : 3, \"Worst\" : 4} ) == (1, 2, 3, {'UTS': 2, 'is': 3, 'Worst': 4})", "assert add_dict_to_tuple((8, 9, 10), {\"POS\" : 3, \"is\" : 4, \"Okay\" : 5} ) == (8, 9, 10, {'POS': 3, 'is': 4, 'Okay': 5})"], "entry_point": "add_dict_to_tuple", "test": "def check():\n    assert add_dict_to_tuple((4, 5, 6), {\"MSAM\" : 1, \"is\" : 2, \"best\" : 3} ) == (4, 5, 6, {'MSAM': 1, 'is': 2, 'best': 3})\n    assert add_dict_to_tuple((1, 2, 3), {\"UTS\" : 2, \"is\" : 3, \"Worst\" : 4} ) == (1, 2, 3, {'UTS': 2, 'is': 3, 'Worst': 4})\n    assert add_dict_to_tuple((8, 9, 10), {\"POS\" : 3, \"is\" : 4, \"Okay\" : 5} ) == (8, 9, 10, {'POS': 3, 'is': 4, 'Okay': 5})\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 447, "prompt": "Write a function to find cubes of individual elements in a list.\n\ndef cube_nums(nums):", "code": "def cube_nums(nums):\n cube_nums = list(map(lambda x: x ** 3, nums))\n return cube_nums", "test_imports": [], "test_list": ["assert cube_nums([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])==[1, 8, 27, 64, 125, 216, 343, 512, 729, 1000]", "assert cube_nums([10,20,30])==([1000, 8000, 27000])", "assert cube_nums([12,15])==([1728, 3375])"], "entry_point": "cube_nums", "test": "def check():\n    assert cube_nums([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])==[1, 8, 27, 64, 125, 216, 343, 512, 729, 1000]\n    assert cube_nums([10,20,30])==([1000, 8000, 27000])\n    assert cube_nums([12,15])==([1728, 3375])\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 465, "prompt": "Write a function to drop empty items from a given dictionary.\n\ndef drop_empty(dict1):", "code": "def drop_empty(dict1):\n  dict1 = {key:value for (key, value) in dict1.items() if value is not None}\n  return dict1", "test_imports": [], "test_list": ["assert drop_empty({'c1': 'Red', 'c2': 'Green', 'c3':None})=={'c1': 'Red', 'c2': 'Green'}", "assert drop_empty({'c1': 'Red', 'c2': None, 'c3':None})=={'c1': 'Red'}", "assert drop_empty({'c1': None, 'c2': 'Green', 'c3':None})=={ 'c2': 'Green'}"], "entry_point": "drop_empty", "test": "def check():\n    assert drop_empty({'c1': 'Red', 'c2': 'Green', 'c3':None})=={'c1': 'Red', 'c2': 'Green'}\n    assert drop_empty({'c1': 'Red', 'c2': None, 'c3':None})=={'c1': 'Red'}\n    assert drop_empty({'c1': None, 'c2': 'Green', 'c3':None})=={ 'c2': 'Green'}\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 63, "prompt": "Write a function to find the maximum difference between available pairs in the given tuple list.\n\ndef max_difference(test_list):", "code": "def max_difference(test_list):\n  temp = [abs(b - a) for a, b in test_list]\n  res = max(temp)\n  return (res) ", "test_imports": [], "test_list": ["assert max_difference([(3, 5), (1, 7), (10, 3), (1, 2)]) == 7", "assert max_difference([(4, 6), (2, 17), (9, 13), (11, 12)]) == 15", "assert max_difference([(12, 35), (21, 27), (13, 23), (41, 22)]) == 23"], "entry_point": "max_difference", "test": "def check():\n    assert max_difference([(3, 5), (1, 7), (10, 3), (1, 2)]) == 7\n    assert max_difference([(4, 6), (2, 17), (9, 13), (11, 12)]) == 15\n    assert max_difference([(12, 35), (21, 27), (13, 23), (41, 22)]) == 23\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 608, "prompt": "Write a python function to find nth bell number.\n\ndef bell_Number(n): ", "code": "def bell_Number(n): \n    bell = [[0 for i in range(n+1)] for j in range(n+1)] \n    bell[0][0] = 1\n    for i in range(1, n+1):\n        bell[i][0] = bell[i-1][i-1]\n        for j in range(1, i+1): \n            bell[i][j] = bell[i-1][j-1] + bell[i][j-1] \n    return bell[n][0] ", "test_imports": [], "test_list": ["assert bell_Number(2) == 2", "assert bell_Number(3) == 5", "assert bell_Number(4) == 15"], "entry_point": "bell_Number", "test": "def check():\n    assert bell_Number(2) == 2\n    assert bell_Number(3) == 5\n    assert bell_Number(4) == 15\n"}
{"source_file": "Ellen's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 290, "prompt": "Write a function to find the list of maximum length in a list of lists.\n\ndef max_length(list1):", "code": "def max_length(list1):\n    max_length = max(len(x) for x in  list1 )  \n    max_list = max((x) for x in   list1)\n    return(max_length, max_list)", "test_imports": [], "test_list": ["assert max_length([[0], [1, 3], [5, 7], [9, 11], [13, 15, 17]])==(3, [13, 15, 17])", "assert max_length([[1], [5, 7], [10, 12, 14,15]])==(4, [10, 12, 14,15])", "assert max_length([[5], [15,20,25]])==(3, [15,20,25])"], "entry_point": "max_length", "test": "def check():\n    assert max_length([[0], [1, 3], [5, 7], [9, 11], [13, 15, 17]])==(3, [13, 15, 17])\n    assert max_length([[1], [5, 7], [10, 12, 14,15]])==(4, [10, 12, 14,15])\n    assert max_length([[5], [15,20,25]])==(3, [15,20,25])\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 567, "prompt": "Write a function to check whether a specified list is sorted or not.\n\ndef issort_list(list1):", "code": "def issort_list(list1):\n    result = all(list1[i] <= list1[i+1] for i in range(len(list1)-1))\n    return result", "test_imports": [], "test_list": ["assert issort_list([1,2,4,6,8,10,12,14,16,17])==True", "assert issort_list([1, 2, 4, 6, 8, 10, 12, 14, 20, 17])==False", "assert issort_list([1, 2, 4, 6, 8, 10,15,14,20])==False"], "entry_point": "issort_list", "test": "def check():\n    assert issort_list([1,2,4,6,8,10,12,14,16,17])==True\n    assert issort_list([1, 2, 4, 6, 8, 10, 12, 14, 20, 17])==False\n    assert issort_list([1, 2, 4, 6, 8, 10,15,14,20])==False\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 399, "prompt": "Write a function to perform the mathematical bitwise xor operation across the given tuples.\n\ndef bitwise_xor(test_tup1, test_tup2):", "code": "def bitwise_xor(test_tup1, test_tup2):\n  res = tuple(ele1 ^ ele2 for ele1, ele2 in zip(test_tup1, test_tup2))\n  return (res) ", "test_imports": [], "test_list": ["assert bitwise_xor((10, 4, 6, 9), (5, 2, 3, 3)) == (15, 6, 5, 10)", "assert bitwise_xor((11, 5, 7, 10), (6, 3, 4, 4)) == (13, 6, 3, 14)", "assert bitwise_xor((12, 6, 8, 11), (7, 4, 5, 6)) == (11, 2, 13, 13)"], "entry_point": "bitwise_xor", "test": "def check():\n    assert bitwise_xor((10, 4, 6, 9), (5, 2, 3, 3)) == (15, 6, 5, 10)\n    assert bitwise_xor((11, 5, 7, 10), (6, 3, 4, 4)) == (13, 6, 3, 14)\n    assert bitwise_xor((12, 6, 8, 11), (7, 4, 5, 6)) == (11, 2, 13, 13)\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 764, "prompt": "Write a python function to count number of digits in a given string.\n\ndef number_ctr(str):", "code": "def number_ctr(str):\n      number_ctr= 0\n      for i in range(len(str)):\n          if str[i] >= '0' and str[i] <= '9': number_ctr += 1     \n      return  number_ctr", "test_imports": [], "test_list": ["assert number_ctr('program2bedone') == 1", "assert number_ctr('3wonders') == 1", "assert number_ctr('123') == 3", "assert number_ctr('3wond-1ers2') == 3"], "entry_point": "number_ctr", "test": "def check():\n    assert number_ctr('program2bedone') == 1\n    assert number_ctr('3wonders') == 1\n    assert number_ctr('123') == 3\n    assert number_ctr('3wond-1ers2') == 3\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 587, "prompt": "Write a function to convert a list to a tuple.\n\ndef list_tuple(listx):", "code": "def list_tuple(listx):\n  tuplex = tuple(listx)\n  return tuplex", "test_imports": [], "test_list": ["assert list_tuple([5, 10, 7, 4, 15, 3])==(5, 10, 7, 4, 15, 3)", "assert list_tuple([2, 4, 5, 6, 2, 3, 4, 4, 7])==(2, 4, 5, 6, 2, 3, 4, 4, 7)", "assert list_tuple([58,44,56])==(58,44,56)"], "entry_point": "list_tuple", "test": "def check():\n    assert list_tuple([5, 10, 7, 4, 15, 3])==(5, 10, 7, 4, 15, 3)\n    assert list_tuple([2, 4, 5, 6, 2, 3, 4, 4, 7])==(2, 4, 5, 6, 2, 3, 4, 4, 7)\n    assert list_tuple([58,44,56])==(58,44,56)\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 644, "prompt": "Write a python function to reverse an array upto a given position.\n\ndef reverse_Array_Upto_K(input, k): ", "code": "def reverse_Array_Upto_K(input, k): \n  return (input[k-1::-1] + input[k:]) ", "test_imports": [], "test_list": ["assert reverse_Array_Upto_K([1, 2, 3, 4, 5, 6],4) == [4, 3, 2, 1, 5, 6]", "assert reverse_Array_Upto_K([4, 5, 6, 7], 2) == [5, 4, 6, 7]", "assert reverse_Array_Upto_K([9, 8, 7, 6, 5],3) == [7, 8, 9, 6, 5]"], "entry_point": "reverse_Array_Upto_K", "test": "def check():\n    assert reverse_Array_Upto_K([1, 2, 3, 4, 5, 6],4) == [4, 3, 2, 1, 5, 6]\n    assert reverse_Array_Upto_K([4, 5, 6, 7], 2) == [5, 4, 6, 7]\n    assert reverse_Array_Upto_K([9, 8, 7, 6, 5],3) == [7, 8, 9, 6, 5]\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 473, "prompt": "Write a function to find the tuple intersection of elements in the given tuple list irrespective of their order.\n\ndef tuple_intersection(test_list1, test_list2):", "code": "def tuple_intersection(test_list1, test_list2):\n  res = set([tuple(sorted(ele)) for ele in test_list1]) & set([tuple(sorted(ele)) for ele in test_list2])\n  return (res)", "test_imports": [], "test_list": ["assert tuple_intersection([(3, 4), (5, 6), (9, 10), (4, 5)] , [(5, 4), (3, 4), (6, 5), (9, 11)]) == {(4, 5), (3, 4), (5, 6)}", "assert tuple_intersection([(4, 1), (7, 4), (11, 13), (17, 14)] , [(1, 4), (7, 4), (16, 12), (10, 13)]) == {(4, 7), (1, 4)}", "assert tuple_intersection([(2, 1), (3, 2), (1, 3), (1, 4)] , [(11, 2), (2, 3), (6, 2), (1, 3)]) == {(1, 3), (2, 3)}"], "entry_point": "tuple_intersection", "test": "def check():\n    assert tuple_intersection([(3, 4), (5, 6), (9, 10), (4, 5)] , [(5, 4), (3, 4), (6, 5), (9, 11)]) == {(4, 5), (3, 4), (5, 6)}\n    assert tuple_intersection([(4, 1), (7, 4), (11, 13), (17, 14)] , [(1, 4), (7, 4), (16, 12), (10, 13)]) == {(4, 7), (1, 4)}\n    assert tuple_intersection([(2, 1), (3, 2), (1, 3), (1, 4)] , [(11, 2), (2, 3), (6, 2), (1, 3)]) == {(1, 3), (2, 3)}\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 603, "prompt": "Write a function to get all lucid numbers smaller than or equal to a given integer.\n\ndef get_ludic(n):", "code": "def get_ludic(n):\n\tludics = []\n\tfor i in range(1, n + 1):\n\t\tludics.append(i)\n\tindex = 1\n\twhile(index != len(ludics)):\n\t\tfirst_ludic = ludics[index]\n\t\tremove_index = index + first_ludic\n\t\twhile(remove_index < len(ludics)):\n\t\t\tludics.remove(ludics[remove_index])\n\t\t\tremove_index = remove_index + first_ludic - 1\n\t\tindex += 1\n\treturn ludics", "test_imports": [], "test_list": ["assert get_ludic(10) == [1, 2, 3, 5, 7]", "assert get_ludic(25) == [1, 2, 3, 5, 7, 11, 13, 17, 23, 25]", "assert get_ludic(45) == [1, 2, 3, 5, 7, 11, 13, 17, 23, 25, 29, 37, 41, 43]"], "entry_point": "get_ludic", "test": "def check():\n    assert get_ludic(10) == [1, 2, 3, 5, 7]\n    assert get_ludic(25) == [1, 2, 3, 5, 7, 11, 13, 17, 23, 25]\n    assert get_ludic(45) == [1, 2, 3, 5, 7, 11, 13, 17, 23, 25, 29, 37, 41, 43]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 277, "prompt": "Write a function that takes in a dictionary and integer n and filters the dictionary to only include entries with values greater than or equal to n.\n\ndef dict_filter(dict,n):", "code": "def dict_filter(dict,n):\n result = {key:value for (key, value) in dict.items() if value >=n}\n return result", "test_imports": [], "test_list": ["assert dict_filter({'Cierra Vega': 175, 'Alden Cantrell': 180, 'Kierra Gentry': 165, 'Pierre Cox': 190},170)=={'Cierra Vega': 175, 'Alden Cantrell': 180, 'Pierre Cox': 190}", "assert dict_filter({'Cierra Vega': 175, 'Alden Cantrell': 180, 'Kierra Gentry': 165, 'Pierre Cox': 190},180)=={ 'Alden Cantrell': 180, 'Pierre Cox': 190}", "assert dict_filter({'Cierra Vega': 175, 'Alden Cantrell': 180, 'Kierra Gentry': 165, 'Pierre Cox': 190},190)=={ 'Pierre Cox': 190}"], "entry_point": "dict_filter", "test": "def check():\n    assert dict_filter({'Cierra Vega': 175, 'Alden Cantrell': 180, 'Kierra Gentry': 165, 'Pierre Cox': 190},170)=={'Cierra Vega': 175, 'Alden Cantrell': 180, 'Pierre Cox': 190}\n    assert dict_filter({'Cierra Vega': 175, 'Alden Cantrell': 180, 'Kierra Gentry': 165, 'Pierre Cox': 190},180)=={ 'Alden Cantrell': 180, 'Pierre Cox': 190}\n    assert dict_filter({'Cierra Vega': 175, 'Alden Cantrell': 180, 'Kierra Gentry': 165, 'Pierre Cox': 190},190)=={ 'Pierre Cox': 190}\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 255, "prompt": "Write a function that takes in a list and length n, and generates all combinations (with repetition) of the elements of the list and returns a list with a tuple for each combination.\nfrom itertools import combinations_with_replacement \ndef combinations_colors(l, n):", "code": "from itertools import combinations_with_replacement \ndef combinations_colors(l, n):\n    return list(combinations_with_replacement(l,n))\n", "test_imports": [], "test_list": ["assert combinations_colors( [\"Red\",\"Green\",\"Blue\"],1)==[('Red',), ('Green',), ('Blue',)]", "assert combinations_colors( [\"Red\",\"Green\",\"Blue\"],2)==[('Red', 'Red'), ('Red', 'Green'), ('Red', 'Blue'), ('Green', 'Green'), ('Green', 'Blue'), ('Blue', 'Blue')]", "assert combinations_colors( [\"Red\",\"Green\",\"Blue\"],3)==[('Red', 'Red', 'Red'), ('Red', 'Red', 'Green'), ('Red', 'Red', 'Blue'), ('Red', 'Green', 'Green'), ('Red', 'Green', 'Blue'), ('Red', 'Blue', 'Blue'), ('Green', 'Green', 'Green'), ('Green', 'Green', 'Blue'), ('Green', 'Blue', 'Blue'), ('Blue', 'Blue', 'Blue')]"], "entry_point": "combinations_colors", "test": "def check():\n    assert combinations_colors( [\"Red\",\"Green\",\"Blue\"],1)==[('Red',), ('Green',), ('Blue',)]\n    assert combinations_colors( [\"Red\",\"Green\",\"Blue\"],2)==[('Red', 'Red'), ('Red', 'Green'), ('Red', 'Blue'), ('Green', 'Green'), ('Green', 'Blue'), ('Blue', 'Blue')]\n    assert combinations_colors( [\"Red\",\"Green\",\"Blue\"],3)==[('Red', 'Red', 'Red'), ('Red', 'Red', 'Green'), ('Red', 'Red', 'Blue'), ('Red', 'Green', 'Green'), ('Red', 'Green', 'Blue'), ('Red', 'Blue', 'Blue'), ('Green', 'Green', 'Green'), ('Green', 'Green', 'Blue'), ('Green', 'Blue', 'Blue'), ('Blue', 'Blue', 'Blue')]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 279, "prompt": "Write a function to find the nth decagonal number.\n\ndef is_num_decagonal(n): ", "code": "def is_num_decagonal(n): \n\treturn 4 * n * n - 3 * n ", "test_imports": [], "test_list": ["assert is_num_decagonal(3) == 27", "assert is_num_decagonal(7) == 175", "assert is_num_decagonal(10) == 370"], "entry_point": "is_num_decagonal", "test": "def check():\n    assert is_num_decagonal(3) == 27\n    assert is_num_decagonal(7) == 175\n    assert is_num_decagonal(10) == 370\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 737, "prompt": "Write a function to check whether the given string is starting with a vowel or not using regex.\nimport re \ndef check_str(string): ", "code": "import re \nregex = '^[aeiouAEIOU][A-Za-z0-9_]*'\ndef check_str(string): \n\treturn re.search(regex, string)", "test_imports": [], "test_list": ["assert check_str(\"annie\")", "assert not check_str(\"dawood\")", "assert check_str(\"Else\")"], "entry_point": "check_str", "test": "def check():\n    assert check_str(\"annie\")\n    assert not check_str(\"dawood\")\n    assert check_str(\"Else\")\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 168, "prompt": "Write a function to count the number of occurrences of a number in a given list.\n\ndef frequency(a,x): ", "code": "def frequency(a,x): \n    count = 0  \n    for i in a: \n      if i == x: \n        count += 1\n\n    return count ", "test_imports": [], "test_list": ["assert frequency([1,2,3], 4) == 0", "assert frequency([1,2,2,3,3,3,4], 3) == 3", "assert frequency([0,1,2,3,1,2], 1) == 2"], "entry_point": "frequency", "test": "def check():\n    assert frequency([1,2,3], 4) == 0\n    assert frequency([1,2,2,3,3,3,4], 3) == 3\n    assert frequency([0,1,2,3,1,2], 1) == 2\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 750, "prompt": "Write a function to add the given tuple to the given list.\n\ndef add_tuple(test_list, test_tup):", "code": "def add_tuple(test_list, test_tup):\n  test_list += test_tup\n  return test_list", "test_imports": [], "test_list": ["assert add_tuple([5, 6, 7], (9, 10)) == [5, 6, 7, 9, 10]", "assert add_tuple([6, 7, 8], (10, 11)) == [6, 7, 8, 10, 11]", "assert add_tuple([7, 8, 9], (11, 12)) == [7, 8, 9, 11, 12]"], "entry_point": "add_tuple", "test": "def check():\n    assert add_tuple([5, 6, 7], (9, 10)) == [5, 6, 7, 9, 10]\n    assert add_tuple([6, 7, 8], (10, 11)) == [6, 7, 8, 10, 11]\n    assert add_tuple([7, 8, 9], (11, 12)) == [7, 8, 9, 11, 12]\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 141, "prompt": "Write a function to sort a list of elements.\n\ndef pancake_sort(nums):", "code": "def pancake_sort(nums):\n    arr_len = len(nums)\n    while arr_len > 1:\n        mi = nums.index(max(nums[0:arr_len]))\n        nums = nums[mi::-1] + nums[mi+1:len(nums)]\n        nums = nums[arr_len-1::-1] + nums[arr_len:len(nums)]\n        arr_len -= 1\n    return nums", "test_imports": [], "test_list": ["assert pancake_sort([15, 79, 25, 38, 69]) == [15, 25, 38, 69, 79]", "assert pancake_sort([98, 12, 54, 36, 85]) == [12, 36, 54, 85, 98]", "assert pancake_sort([41, 42, 32, 12, 23]) == [12, 23, 32, 41, 42]"], "entry_point": "pancake_sort", "test": "def check():\n    assert pancake_sort([15, 79, 25, 38, 69]) == [15, 25, 38, 69, 79]\n    assert pancake_sort([98, 12, 54, 36, 85]) == [12, 36, 54, 85, 98]\n    assert pancake_sort([41, 42, 32, 12, 23]) == [12, 23, 32, 41, 42]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 628, "prompt": "Write a function to replace all spaces in the given string with '%20'.\n\ndef replace_spaces(string):", "code": "def replace_spaces(string):\n  return string.replace(\" \", \"%20\")", "test_imports": [], "test_list": ["assert replace_spaces(\"My Name is Dawood\") == 'My%20Name%20is%20Dawood'", "assert replace_spaces(\"I am a Programmer\") == 'I%20am%20a%20Programmer'", "assert replace_spaces(\"I love Coding\") == 'I%20love%20Coding'"], "entry_point": "replace_spaces", "test": "def check():\n    assert replace_spaces(\"My Name is Dawood\") == 'My%20Name%20is%20Dawood'\n    assert replace_spaces(\"I am a Programmer\") == 'I%20am%20a%20Programmer'\n    assert replace_spaces(\"I love Coding\") == 'I%20love%20Coding'\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 426, "prompt": "Write a function to filter odd numbers.\n\ndef filter_oddnumbers(nums):", "code": "def filter_oddnumbers(nums):\n odd_nums = list(filter(lambda x: x%2 != 0, nums))\n return odd_nums", "test_imports": [], "test_list": ["assert filter_oddnumbers([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])==[1,3,5,7,9]", "assert filter_oddnumbers([10,20,45,67,84,93])==[45,67,93]", "assert filter_oddnumbers([5,7,9,8,6,4,3])==[5,7,9,3]"], "entry_point": "filter_oddnumbers", "test": "def check():\n    assert filter_oddnumbers([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])==[1,3,5,7,9]\n    assert filter_oddnumbers([10,20,45,67,84,93])==[45,67,93]\n    assert filter_oddnumbers([5,7,9,8,6,4,3])==[5,7,9,3]\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 245, "prompt": "Write a function that takes an array and finds the maximum sum of a bitonic subsequence for the given array, where a sequence is bitonic if it is first increasing and then decreasing.\n\ndef max_sum(arr): ", "code": "def max_sum(arr): \n\tMSIBS = arr[:] \n\tfor i in range(len(arr)): \n\t\tfor j in range(0, i): \n\t\t\tif arr[i] > arr[j] and MSIBS[i] < MSIBS[j] + arr[i]: \n\t\t\t\tMSIBS[i] = MSIBS[j] + arr[i] \n\tMSDBS = arr[:] \n\tfor i in range(1, len(arr) + 1): \n\t\tfor j in range(1, i): \n\t\t\tif arr[-i] > arr[-j] and MSDBS[-i] < MSDBS[-j] + arr[-i]: \n\t\t\t\tMSDBS[-i] = MSDBS[-j] + arr[-i] \n\tmax_sum = float(\"-Inf\") \n\tfor i, j, k in zip(MSIBS, MSDBS, arr): \n\t\tmax_sum = max(max_sum, i + j - k) \n\treturn max_sum", "test_imports": [], "test_list": ["assert max_sum([1, 15, 51, 45, 33, 100, 12, 18, 9]) == 194", "assert max_sum([80, 60, 30, 40, 20, 10]) == 210", "assert max_sum([2, 3 ,14, 16, 21, 23, 29, 30]) == 138"], "entry_point": "max_sum", "test": "def check():\n    assert max_sum([1, 15, 51, 45, 33, 100, 12, 18, 9]) == 194\n    assert max_sum([80, 60, 30, 40, 20, 10]) == 210\n    assert max_sum([2, 3 ,14, 16, 21, 23, 29, 30]) == 138\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 809, "prompt": "Write a function to check if each element of second tuple is smaller than its corresponding element in the first tuple.\n\ndef check_smaller(test_tup1, test_tup2):", "code": "def check_smaller(test_tup1, test_tup2):\n  return all(x > y for x, y in zip(test_tup1, test_tup2))", "test_imports": [], "test_list": ["assert check_smaller((1, 2, 3), (2, 3, 4)) == False", "assert check_smaller((4, 5, 6), (3, 4, 5)) == True", "assert check_smaller((11, 12, 13), (10, 11, 12)) == True"], "entry_point": "check_smaller", "test": "def check():\n    assert check_smaller((1, 2, 3), (2, 3, 4)) == False\n    assert check_smaller((4, 5, 6), (3, 4, 5)) == True\n    assert check_smaller((11, 12, 13), (10, 11, 12)) == True\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 62, "prompt": "Write a python function to find smallest number in a list.\n\ndef smallest_num(xs):", "code": "def smallest_num(xs):\n  return min(xs)\n", "test_imports": [], "test_list": ["assert smallest_num([10, 20, 1, 45, 99]) == 1", "assert smallest_num([1, 2, 3]) == 1", "assert smallest_num([45, 46, 50, 60]) == 45"], "entry_point": "smallest_num", "test": "def check():\n    assert smallest_num([10, 20, 1, 45, 99]) == 1\n    assert smallest_num([1, 2, 3]) == 1\n    assert smallest_num([45, 46, 50, 60]) == 45\n"}
{"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 396, "prompt": "Write a function to check whether the given string starts and ends with the same character or not.\nimport re  \ndef check_char(string): ", "code": "import re  \nregex = r'^[a-z]$|^([a-z]).*\\1$'\ndef check_char(string): \n\tif(re.search(regex, string)): \n\t\treturn \"Valid\" \n\telse: \n\t\treturn \"Invalid\" ", "test_imports": [], "test_list": ["assert check_char(\"abba\") == \"Valid\"", "assert check_char(\"a\") == \"Valid\"", "assert check_char(\"abcd\") == \"Invalid\""], "entry_point": "check_char", "test": "def check():\n    assert check_char(\"abba\") == \"Valid\"\n    assert check_char(\"a\") == \"Valid\"\n    assert check_char(\"abcd\") == \"Invalid\"\n"}
{"source_file": "Mike's Copy of Benchmark Questions Verification V2.ipynb", "task_id": 123, "prompt": "Write a function to sum all amicable numbers from 1 to a specified number.\n\ndef amicable_numbers_sum(limit):", "code": "def amicable_numbers_sum(limit):\n    if not isinstance(limit, int):\n        return \"Input is not an integer!\"\n    if limit < 1:\n        return \"Input must be bigger than 0!\"\n    amicables = set()\n    for num in range(2, limit+1):\n        if num in amicables:\n            continue\n        sum_fact = sum([fact for fact in range(1, num) if num % fact == 0])\n        sum_fact2 = sum([fact for fact in range(1, sum_fact) if sum_fact % fact == 0])\n        if num == sum_fact2 and num != sum_fact:\n            amicables.add(num)\n            amicables.add(sum_fact2)\n    return sum(amicables)", "test_imports": [], "test_list": ["assert amicable_numbers_sum(999)==504", "assert amicable_numbers_sum(9999)==31626", "assert amicable_numbers_sum(99)==0"], "entry_point": "amicable_numbers_sum", "test": "def check():\n    assert amicable_numbers_sum(999)==504\n    assert amicable_numbers_sum(9999)==31626\n    assert amicable_numbers_sum(99)==0\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 226, "prompt": "Write a python function to remove the characters which have odd index values of a given string.\n\ndef odd_values_string(str):", "code": "def odd_values_string(str):\n  result = \"\" \n  for i in range(len(str)):\n    if i % 2 == 0:\n      result = result + str[i]\n  return result", "test_imports": [], "test_list": ["assert odd_values_string('abcdef') == 'ace'", "assert odd_values_string('python') == 'pto'", "assert odd_values_string('data') == 'dt'", "assert odd_values_string('lambs') == 'lms'"], "entry_point": "odd_values_string", "test": "def check():\n    assert odd_values_string('abcdef') == 'ace'\n    assert odd_values_string('python') == 'pto'\n    assert odd_values_string('data') == 'dt'\n    assert odd_values_string('lambs') == 'lms'\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 560, "prompt": "Write a function to find the union of the elements of two given tuples and output them in sorted order.\n\ndef union_elements(test_tup1, test_tup2):", "code": "def union_elements(test_tup1, test_tup2):\n  res = tuple(set(test_tup1 + test_tup2))\n  return (res) ", "test_imports": [], "test_list": ["assert union_elements((3, 4, 5, 6),(5, 7, 4, 10) ) == (3, 4, 5, 6, 7, 10)", "assert union_elements((1, 2, 3, 4),(3, 4, 5, 6) ) == (1, 2, 3, 4, 5, 6)", "assert union_elements((11, 12, 13, 14),(13, 15, 16, 17) ) == (11, 12, 13, 14, 15, 16, 17)"], "entry_point": "union_elements", "test": "def check():\n    assert union_elements((3, 4, 5, 6),(5, 7, 4, 10) ) == (3, 4, 5, 6, 7, 10)\n    assert union_elements((1, 2, 3, 4),(3, 4, 5, 6) ) == (1, 2, 3, 4, 5, 6)\n    assert union_elements((11, 12, 13, 14),(13, 15, 16, 17) ) == (11, 12, 13, 14, 15, 16, 17)\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 724, "prompt": "Write a function that takes base and power as arguments and calculate the sum of all digits of the base to the specified power.\n\ndef power_base_sum(base, power):", "code": "def power_base_sum(base, power):\n    return sum([int(i) for i in str(pow(base, power))])", "test_imports": [], "test_list": ["assert power_base_sum(2,100)==115", "assert power_base_sum(8,10)==37", "assert power_base_sum(8,15)==62", "assert power_base_sum(3,3)==9"], "entry_point": "power_base_sum", "test": "def check():\n    assert power_base_sum(2,100)==115\n    assert power_base_sum(8,10)==37\n    assert power_base_sum(8,15)==62\n    assert power_base_sum(3,3)==9\n"}
{"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 172, "prompt": "Write a function to count the number of occurence of the string 'std' in a given string.\n\ndef count_occurance(s):", "code": "def count_occurance(s):\n  count = 0\n  for i in range(len(s) - 2):\n    if (s[i] == 's' and s[i+1] == 't' and s[i+2] == 'd'):\n      count = count + 1\n  return count", "test_imports": [], "test_list": ["assert count_occurance(\"letstdlenstdporstd\") == 3", "assert count_occurance(\"truststdsolensporsd\") == 1", "assert count_occurance(\"makestdsostdworthit\") == 2", "assert count_occurance(\"stds\") == 1", "assert count_occurance(\"\") == 0"], "entry_point": "count_occurance", "test": "def check():\n    assert count_occurance(\"letstdlenstdporstd\") == 3\n    assert count_occurance(\"truststdsolensporsd\") == 1\n    assert count_occurance(\"makestdsostdworthit\") == 2\n    assert count_occurance(\"stds\") == 1\n    assert count_occurance(\"\") == 0\n"}
