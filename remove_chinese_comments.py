#!/usr/bin/env python3
"""
Script to remove Chinese comments from Python files
"""

import os
import re
import sys
from pathlib import Path

def has_chinese_chars(text):
    """Check if text contains Chinese characters"""
    return bool(re.search(r'[\u4e00-\u9fff]', text))

def remove_chinese_from_line(line):
    """Remove Chinese characters from a line while preserving structure"""
    # Skip if line doesn't contain Chinese
    if not has_chinese_chars(line):
        return line

    # Handle different comment patterns
    stripped = line.lstrip()
    indent = line[:len(line) - len(stripped)]

    # Full line comment with Chinese
    if stripped.startswith('#') and has_chinese_chars(stripped):
        return ""  # Remove entire line

    # Inline comment with Chinese
    if '#' in line:
        code_part, comment_part = line.split('#', 1)
        if has_chinese_chars(comment_part):
            # Remove the Chinese comment part
            return code_part.rstrip() + '\n' if code_part.strip() else ""

    # Docstring with Chinese - remove Chinese content but keep structure
    if ('"""' in line or "'''" in line) and has_chinese_chars(line):
        # If it's a single line docstring with Chinese, remove it
        if line.count('"""') == 2 or line.count("'''") == 2:
            return ""
        # If it's start/end of multiline docstring with Chinese, remove it
        elif '"""' in line or "'''" in line:
            return ""

    return line

def process_file(file_path):
    """Process a single Python file to remove Chinese comments"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        modified = False
        new_lines = []
        
        for line in lines:
            new_line = remove_chinese_from_line(line)
            if new_line != line:
                modified = True
            if new_line:  # Only add non-empty lines
                new_lines.append(new_line)
        
        if modified:
            # Write back to file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(new_lines)
            print(f"✅ Processed: {file_path}")
            return True
        else:
            print(f"⏭️  No changes: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ Error processing {file_path}: {e}")
        return False

def find_python_files_with_chinese(directory):
    """Find all Python files containing Chinese characters"""
    python_files = []
    
    for root, dirs, files in os.walk(directory):
        # Skip hidden directories and __pycache__
        dirs[:] = [d for d in dirs if not d.startswith('.') and d != '__pycache__']
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if has_chinese_chars(content):
                            python_files.append(file_path)
                except:
                    pass
    
    return python_files

def main():
    """Main function"""
    current_dir = '.'
    
    print("🔍 Finding Python files with Chinese comments...")
    files_with_chinese = find_python_files_with_chinese(current_dir)
    
    if not files_with_chinese:
        print("✅ No Python files with Chinese characters found!")
        return
    
    print(f"📝 Found {len(files_with_chinese)} files with Chinese characters:")
    for file_path in files_with_chinese:
        print(f"   - {file_path}")
    
    print("\n🚀 Processing files...")
    processed_count = 0
    
    for file_path in files_with_chinese:
        if process_file(file_path):
            processed_count += 1
    
    print(f"\n✅ Completed! Processed {processed_count}/{len(files_with_chinese)} files.")

if __name__ == "__main__":
    main()
