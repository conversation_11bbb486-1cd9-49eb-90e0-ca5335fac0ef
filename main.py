#!/usr/bin/env python3
"""
Simplified Multi-Agent System

Simple Multi-Agent Processing:
- Multiple agents generate responses independently
- Best response selection based on length/quality heuristics
- Minimal overhead, maximum efficiency
- Direct answer generation without complex coordination

Core Innovation: Streamlined multi-agent processing for
fast, reliable answers with minimal complexity.
"""

import asyncio
import json
import argparse
import os
import sys
from datetime import datetime
from typing import List, Dict, Any, Optional

# Task manager removed - using collaborative system only
from utils.answer_extraction import create_answer_entry
from utils.api import async_generate_completion
import config
from config import get_config
import re

# Constants
RESULTS_DIR = "results"
BENCHMARK_DIR = "benchmark"


def _should_preserve_result(solution: str, task_type: str, quality_score: float,
                          result_context: dict = None) -> bool:
    """Determine whether to preserve result - using Leader's quality decision"""
    if not solution or not solution.strip():
        return False

    # 检查是否包含明显的错误信息
    error_indicators = [
        'processing failed', 'connection failed', 'authentication failed',
        'api key not set', 'timeout', 'exception occurred'
    ]

    solution_lower = solution.lower()
    has_error = any(indicator in solution_lower for indicator in error_indicators)

    # Prioritize Leader's quality decision - relax acceptance conditions
    if result_context and 'quality_decision' in result_context:
        quality_decision = result_context['quality_decision']

        # 扩大接受范围：包括需要小修改的答案
        approved_decisions = [
            'approved', 'conditional_approved', 'conditional', 
            'needs_minor_revision', 'minor_revision'  # New: accept minor revisions too
        ]
        if quality_decision in approved_decisions:
            print(f"   ✅ Leader approved answer (decision: {quality_decision})")
            return True
        # For major revision answers, keep if quality score is decent
        elif quality_decision in ['needs_major_revision', 'major_revision'] and quality_score >= 0.3:
            print(f"   ⚠️ Leader requested major revision but quality acceptable ({quality_score:.2f})")
            return True
        else:
            print(f"   ⚠️ Leader requested rejection but keeping answer anyway (decision: {quality_decision})")
            return True  # Keep answer regardless of what leader says

    # 如果有质量分数，总是接受
    if quality_score > 0:
        print(f"   ✅ Answer accepted regardless of quality score: {quality_score:.2f}")
        return True
    else:
        print(f"   ✅ Answer accepted (no quality score)")
        return True

    # If no quality score, use heuristic method
    print(f"   ⚠️ Using heuristic method (no quality score available)")

    if has_error:
        # Even with errors, keep if content is long enough and may contain useful info
        has_useful_content = any(word in solution_lower for word in ['answer', 'result', 'solution', 'analysis'])
        return len(solution.strip()) > 30 and has_useful_content and not solution.strip().startswith("Processing failed:")

    # Basic length check - more lenient
    return len(solution.strip()) >= 5  # Reduced from 10 to 5


def _extract_useful_content(solution: str, task_type: str) -> str:
    """Extract useful content from failed responses"""
    if not solution or not solution.strip():
        return ""

    # Remove obvious error prefixes
    error_prefixes = [
        'processing failed:', 'multi-agent processing failed:',
        'connection failed:', 'authentication failed:'
    ]

    cleaned = solution.strip()
    for prefix in error_prefixes:
        if cleaned.lower().startswith(prefix):
            cleaned = cleaned[len(prefix):].strip()

    # If cleaned content is too short, return empty
    if len(cleaned) < 20:
        return ""

    # Task type specific extraction
    if task_type in ['gsm8k', 'math']:
        # Try to extract numerical answer
        numbers = re.findall(r'\b\d+\.?\d*\b', cleaned)
        if numbers:
            return f"Extracted potential answer: {numbers[-1]}"

    elif task_type in ['humaneval', 'mbpp']:
        # Try to extract code snippets
        if 'def ' in cleaned or 'return' in cleaned:
            lines = cleaned.split('\n')
            code_lines = [line for line in lines if 'def ' in line or 'return' in line]
            if code_lines:
                return '\n'.join(code_lines[:3])  # Maximum 3 lines

    # Generic extraction: return first 100 characters
    if len(cleaned) > 100:
        return cleaned[:100] + "..."

    return cleaned


def optimize_final_results(results: Dict[str, Any]) -> Dict[str, Any]:
    """Optimize final results, ensuring only highest quality answers are kept"""
    if not results.get("results"):
        return results

    # Group by prompt, find best answer for each question
    prompt_to_best = {}

    for result in results["results"]:
        prompt = result.get("prompt", "")
        if not prompt:
            continue

        # No longer skip any results, including those marked as failed
        # All results with answers should be preserved

        solution = result.get("solution", "")
        task_type = results.get("metadata", {}).get("task_type", "")

        # 简化逻辑：不过滤质量，给所有结果高分
        quality_score = result.get("quality_score", 0.8)
        result["quality_score"] = quality_score

        # 简单去重：保留最后一个结果
        prompt_to_best[prompt] = result

    # 重建结果列表，只包含最佳答案
    optimized_results = {
        "metadata": results["metadata"],
        "results": list(prompt_to_best.values())
    }

    original_count = len(results["results"])
    optimized_count = len(optimized_results["results"])

    if original_count > optimized_count:
        print(f"🔧 Deduplicated results: {original_count} → {optimized_count} (removed {original_count - optimized_count} duplicates)")

    return optimized_results


def _is_good_quality_result(solution: str, task_type: str) -> bool:
    """判断是否是高质量结果，应该被保留不被覆盖"""
    if not solution or not solution.strip():
        return False

    solution_lower = solution.lower()

    # 明显的错误指标
    error_indicators = [
        'processing failed', 'multi-agent processing failed',
        'connection error', 'connection failed', 'timeout',
        'authentication failed', 'api key not set',
        'exception occurred', 'traceback'
    ]

    # 如果包含错误指标，不是好质量
    if any(indicator in solution_lower for indicator in error_indicators):
        return False

    # 基本长度检查 - 对于数学问题，如果有明确答案可以更短
    min_length = 10 if task_type in ['gsm8k', 'math'] else 20
    if len(solution.strip()) < min_length:
        return False

    # 任务类型特定的质量检查
    if task_type in ['gsm8k', 'math']:
        # 数学问题应该有数字答案或明确的解答
        has_number = bool(re.search(r'\b\d+\.?\d*\b', solution))
        has_answer_phrase = any(phrase in solution_lower for phrase in [
            'answer is', 'the answer', 'solution is', 'result is', 'equals'
        ])
        return has_number or has_answer_phrase  # 有数字或答案短语即可

    elif task_type in ['humaneval', 'mbpp']:
        # 编程问题应该有代码
        return 'def ' in solution or 'return' in solution or '```' in solution

    else:
        # 其他类型：检查是否有结构化的回答
        has_structure = any(word in solution_lower for word in [
            'answer', 'solution', 'result', 'conclusion', 'therefore'
        ])
        return has_structure and len(solution.strip()) > 50


async def process_with_baseline_mode(task_content: str, task_type: str = "standard", available_agents=None, problem_id: Optional[str] = None):
    """Process with single agent (baseline mode)"""

    import time
    start_time = time.time()

    try:
        from utils.api import async_generate_completion
        from utils.token_utils import TokenCounter
        from prompt import get_system_prompt

        # Use first available agent or default to openai
        agent_id = available_agents[0] if available_agents else "openai"

        print(f"🔬 Starting Baseline Single-Agent Processing")
        print(f"   Question ID: {problem_id}")
        print(f"   Task Type: {task_type}")
        print(f"   Agent: {agent_id}")

        # Get system prompt for task type
        system_prompt = get_system_prompt(task_type)

        # Generate response
        solution = await async_generate_completion(
            agent_id=agent_id,
            prompt=task_content,
            system_prompt=system_prompt,
            temperature=0.3
        )

        processing_time = time.time() - start_time

        # Count tokens
        token_counter = TokenCounter()
        total_tokens = token_counter.count_tokens(task_content + system_prompt + (solution or ""))

        return {
            'success': bool(solution),
            'solution': solution or "No solution generated",
            'total_processing_time': processing_time,
            'mode': 'baseline_single_agent',
            'agent_used': agent_id,
            'total_tokens': total_tokens
        }

    except Exception as e:
        processing_time = time.time() - start_time
        print(f"❌ Baseline processing failed: {e}")
        return {
            'success': False,
            'solution': f"Baseline processing failed: {str(e)}",
            'total_processing_time': processing_time,
            'mode': 'baseline_error'
        }

async def process_with_context_optimization(task_content: str, task_type: str = "standard", available_agents=None, problem_id: Optional[str] = None, leader_agent: Optional[str] = None):
    """Process with collaborative multi-agent system"""

    import time
    start_time = time.time()

    # Check if baseline mode is enabled
    from config import get_config
    if get_config("baseline_mode", False):
        return await process_with_baseline_mode(task_content, task_type, available_agents, problem_id)

    try:
        from coordination.controller import SimplifiedCollaborativeController

        if not available_agents:
            available_agents = ["openai", "anthropic", "llama"]

        # Limit to 3 agents for efficiency
        available_agents = available_agents[:3]

        print(f"🤝 Starting Simplified Collaborative Multi-Agent Processing")
        print(f"   Question ID: {problem_id}")
        print(f"   Task Type: {task_type}")
        print(f"   Agents: {available_agents}")

        # Initialize simplified collaborative controller
        controller = SimplifiedCollaborativeController()

        # Process the task
        result = await controller.process_task(
            task_content, task_type, available_agents, problem_id
        )
        
        total_time = time.time() - start_time
        result['total_processing_time'] = total_time
        
        if result.get('success', False):
            print(f"   ✅ Simplified collaborative processing completed successfully")
            print(f"   📊 Quality Score: {result.get('quality_score', 0):.2f}")
            print(f"   ⏱️  Total Time: {total_time:.1f}s")
        else:
            print(f"   ❌ Simplified collaborative processing failed")
            
        return result
        
    except Exception as e:
        print(f"❌ Collaborative processing error: {e}")
        import traceback
        traceback.print_exc()
        
        return {
            'success': False,
            'solution': f"Simplified collaborative processing failed: {str(e)}",
            'total_processing_time': time.time() - start_time,
            'error': str(e)
        }

def _get_task_specific_extraction_prompt(task_type: str, question_content: str, draft_content: str) -> str:
    """根据题库类型生成专门的答案提取prompt"""
    
    # 数学题库：只要简短数字
    if task_type in ['gsm8k', 'math']:
        return f"""Extract ONLY the final numerical answer from this solution. No explanation needed.

Question: {question_content}
Solution: {draft_content}

Requirements:
- Return ONLY the number (e.g., "42", "3.14", "$25")
- No words, no explanations, no "The answer is"
- Maximum 10 characters

Final answer:"""

    # 编程题库：完整代码（在外层已处理，这里不应该到达）
    elif task_type in ['humaneval', 'mbpp']:
        return f"""Keep the complete code solution intact:

{draft_content}"""

    # 阅读理解题库：具体事实答案
    elif task_type == 'hotpotqa':
        return f"""Extract the specific factual answer from this response. Be concise but complete.

Question: {question_content}
Response: {draft_content}

Requirements:
- Give the direct answer to the question
- Maximum 15 words
- No explanations or reasoning
- Examples: "83 years", "The Big Bang Theory", "John Smith"

Answer:"""

    # 多选题库：只要选项字母
    elif task_type in ['mmlu', 'gpqa']:
        return f"""Extract ONLY the choice letter from this response.

Question: {question_content}
Response: {draft_content}

Requirements:
- Return only the letter (A, B, C, or D)
- No explanations

Answer:"""

    # 策略问题：Yes/No答案
    elif task_type == 'strategyqa':
        return f"""Extract ONLY Yes or No from this response.

Question: {question_content}
Response: {draft_content}

Requirements:
- Answer only "Yes" or "No"
- No explanations

Answer:"""

    # 其他题库：适度简洁的完整答案
    else:
        return f"""Extract the key answer from this response, keeping it concise but complete.

Question: {question_content}
Response: {draft_content}

Requirements:
- Provide a clear, direct answer
- Remove unnecessary explanations
- Keep essential information
- Maximum 20 words

Answer:"""


def _extract_shortest_answer(content: str) -> str:
    """从内容中提取最短的可能答案"""
    if not content:
        return "No answer found"
    
    lines = content.split('\n')
    
    # 寻找包含"Final Answer:"、"Answer:"等的行
    answer_patterns = ["final answer:", "answer:", "result:", "therefore"]
    
    for line in lines:
        line_lower = line.lower().strip()
        for pattern in answer_patterns:
            if pattern in line_lower:
                # 提取冒号后的内容
                if ':' in line:
                    answer_part = line.split(':', 1)[1].strip()
                    if len(answer_part) <= 50 and answer_part:
                        return answer_part.strip(".,!?;")
    
    # 如果没找到，尝试提取最后一行（通常是答案）
    for line in reversed(lines):
        line = line.strip()
        if line and len(line) <= 50:
            # 移除常见前缀
            prefixes = ["the answer is", "answer:", "final answer:", "result:"]
            line_lower = line.lower()
            for prefix in prefixes:
                if line_lower.startswith(prefix):
                    line = line[len(prefix):].strip()
            
            if line:
                return line.strip(".,!?;")
    
    # 最后尝试提取数字或简短词语
    import re
    # 查找数字
    numbers = re.findall(r'\b\d+(?:\.\d+)?\b', content)
    if numbers:
        return numbers[-1]  # 返回最后一个数字
    
    # 查找简短的词语（3个词以内）
    words = content.split()
    for i in range(len(words)-2, max(-1, len(words)-6), -1):
        phrase = ' '.join(words[i:i+3])
        if len(phrase) <= 30:
            return phrase.strip(".,!?;")
    
    return content[:30].strip() + "..." if len(content) > 30 else content.strip()


def get_active_models(args):
    """Get list of active models"""
    if args.models:
        return args.models
    else:
        return ["openai", "anthropic"]  # Default models

def load_problems_from_jsonl(file_path: str) -> List[Dict[str, Any]]:
    """Load problems from JSONL file"""
    problems = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line:
                    problem = json.loads(line)
                    problems.append(problem)
        return problems
    except Exception as e:
        print(f"Error loading problems from {file_path}: {e}")
        return []

def detect_task_type(file_path: str) -> str:
    """Detect task type from file path"""
    filename = os.path.basename(file_path).lower()
    if 'gsm8k' in filename:
        return 'gsm8k'
    elif 'math' in filename:
        return 'math'
    elif 'mbpp' in filename:
        return 'mbpp'
    elif 'humaneval' in filename:
        return 'humaneval'

    elif 'hotpotqa' in filename:
        return 'hotpotqa'
    elif 'strategyqa' in filename:
        return 'strategyqa'
    elif 'gpqa' in filename:
        return 'gpqa'
    elif 'mmlu' in filename:
        return 'mmlu'
    else:
        return 'standard'

def load_existing_results(file_path: str) -> Optional[Dict[str, Any]]:
    """Load existing results from file"""
    try:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception as e:
        print(f"Warning: Could not load existing results from {file_path}: {e}")
    return None

def save_results(results: Dict[str, Any], file_path: str):
    """Save results to file"""
    try:
        # Ensure we use absolute path
        if not os.path.isabs(file_path):
            project_root = os.path.dirname(os.path.abspath(__file__))
            file_path = os.path.join(project_root, file_path)

        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
    except Exception as e:
        print(f"Error saving results to {file_path}: {e}")

def save_answers_only(results: Dict[str, Any], file_path: str):
    """Save answers-only version"""
    try:
        # Get absolute path to project root
        project_root = os.path.dirname(os.path.abspath(__file__))
        answers_dir = os.path.join(project_root, RESULTS_DIR, "answers")
        os.makedirs(answers_dir, exist_ok=True)

        filename = os.path.basename(file_path).replace('.json', '_answers.json')
        answers_path = os.path.join(answers_dir, filename)

        answers_data = {
            "metadata": results.get("metadata", {}),
            "results": results.get("results", [])
        }

        with open(answers_path, 'w', encoding='utf-8') as f:
            json.dump(answers_data, f, indent=2, ensure_ascii=False)
        print(f"Answers-only version saved to {answers_path}")

    except Exception as e:
        print(f"Error saving answers-only file: {e}")

async def process_problems(problems: List[Dict[str, Any]], results: Dict[str, Any],
                          output_path: str, models: List[str], task_type: str, args):
    """Process all problems with incremental processing support and resume capability"""

    # Enhanced resume capability - check both index and prompt-based matching
    processed_indices = set()
    processed_prompts = set()
    good_quality_prompts = set()
    good_quality_indices = set()

    for existing_result in results.get("results", []):
        existing_prompt = existing_result.get("prompt", "")
        existing_solution = existing_result.get("solution", "")
        existing_index = existing_result.get("problem_index")

        if existing_prompt:
            processed_prompts.add(existing_prompt)

        if existing_index is not None:
            processed_indices.add(existing_index)

            # Check if this is a good quality result that should be preserved
            # Prioritize leader's final answers from multi-agent processing
            is_leader_answer = existing_result.get('is_leader_final_answer', False)
            existing_quality = existing_result.get('quality_score', 0.0)

            # Adjusted quality threshold for better resume capability
            if (is_leader_answer and existing_quality >= 0.6) or existing_quality >= 0.8 or _is_good_quality_result(existing_solution, task_type):
                good_quality_prompts.add(existing_prompt)
                good_quality_indices.add(existing_index)

    processed_count = len(processed_indices) if processed_indices else len(processed_prompts)
    good_quality_count = len(good_quality_indices) if good_quality_indices else len(good_quality_prompts)
    total_problems = len(problems)

    if processed_count > 0:
        print(f"📂 Found {processed_count} already processed problems ({good_quality_count} high quality)")
        # Calculate resume point
        if good_quality_indices:
            start_index = max(good_quality_indices) + 1
            if start_index < total_problems:
                print(f"🔄 Resuming from problem {start_index + 1}/{total_problems}")
            else:
                print(f"✅ All problems already completed with good quality")
        else:
            start_index = 0
            print(f"🔄 Re-processing from beginning (no high-quality results found)")
    else:
        start_index = 0
        print(f"🚀 Starting fresh: Processing {total_problems} problems")

    for i, problem in enumerate(problems):
        # Skip problems before the resume point (based on index-based resume)
        if i < start_index:
            continue
            
        # Extract base prompt - handle different dataset formats
        base_prompt = ""

        if "prompt" in problem:
            # Original format
            base_prompt = problem["prompt"]
        elif "context" in problem:
            # Real HotpotQA format
            context = problem["context"]
            if isinstance(context, list):
                # Real HotpotQA format
                context_text = ""
                for ctx in context:
                    if isinstance(ctx, list) and len(ctx) >= 2:
                        title = ctx[0]
                        paragraphs = ctx[1]
                        if isinstance(paragraphs, list):
                            context_text += f"\n{title}:\n" + " ".join(paragraphs) + "\n"
                        else:
                            context_text += f"\n{title}:\n{paragraphs}\n"
                base_prompt = f"Context: {context_text.strip()}\n\nQuestion: {problem.get('question', '')}"
            elif 'Passage:' in context and 'Question:' in context:
                # Real format with Passage and Question
                base_prompt = context
            else:
                # Generic context format
                base_prompt = f"Passage: {context}\n\nQuestion: Please answer based on the passage."
        elif "question" in problem:
            # GSM8K, StrategyQA format
            base_prompt = problem["question"]
            if "passage" in problem:
                # Reading comprehension format
                base_prompt = f"Passage: {problem['passage']}\n\nQuestion: {problem['question']}"
        elif "problem" in problem:
            # MATH format
            base_prompt = problem["problem"]

        if not base_prompt:
            print(f"Warning: No prompt found in problem {i+1}")
            continue

        # For multiple choice questions, add choices to the prompt
        prompt = base_prompt
        if "choices" in problem and problem["choices"]:
            choices = problem["choices"]
            if isinstance(choices, list) and len(choices) > 0:
                prompt += "\n\nChoices:"
                for choice in choices:
                    prompt += f"\n{choice}"
                prompt += "\n"

        # Enhanced resume logic: check both index and prompt-based matching
        if i in good_quality_indices:
            print(f"⏭️  Skipping problem {i+1}/{total_problems} (already processed with good quality)")
            continue
        elif i in processed_indices or prompt in processed_prompts:
            print(f"🔄 Re-processing problem {i+1}/{total_problems} (previous result was low quality)")
            # Remove the old low-quality result
            results["results"] = [r for r in results["results"] 
                                  if r.get("problem_index") != i and r.get("prompt") != prompt]

        print(f"\nProcessing problem {i+1}/{total_problems}")

        try:
            # Process with context optimization
            problem_id = f"problem_{i+1}"
            result = await process_with_context_optimization(
                task_content=prompt,
                task_type=task_type,
                available_agents=models,
                problem_id=problem_id,
                leader_agent=getattr(args, 'leader', None)
            )

            # Enhanced answer preservation and quality assessment
            solution = result.get('solution', '')
            execution_time = result.get('total_processing_time', 0.0)
            success = result.get('success', True)
            warning = result.get('warning', '')
            quality_score = result.get('quality_score', 0.0)
            partial_results_available = result.get('partial_results_available', False)
            worker_responses_available = result.get('worker_responses_available', False)

            # Determine if we should preserve this result - 使用Leader的质量决策
            result_context = {
                'quality_decision': result.get('quality_decision'),
                'quality_assessment': result.get('quality_assessment'),
                'processing_mode': result.get('processing_mode')
            }
            should_preserve = _should_preserve_result(solution, task_type, quality_score, result_context)

            if should_preserve:
                answer_entry = create_answer_entry(prompt, solution, execution_time, task_type)

                # Add problem index for resume capability
                answer_entry['problem_index'] = i

                # Add metadata about the multi-agent processing
                if warning:
                    answer_entry['warning'] = warning
                if not success:
                    answer_entry['partial_success'] = True
                if quality_score > 0:
                    answer_entry['quality_score'] = quality_score

                # Add multi-agent specific metadata
                processing_mode = result.get('mode', result.get('processing_mode', 'unknown'))
                answer_entry['processing_mode'] = processing_mode

                if processing_mode == 'simple_multi_agent':
                    # This is a simplified multi-agent result - mark it as a final answer
                    answer_entry['is_leader_final_answer'] = True
                    selected_agent = result.get('selected_agent')
                    if selected_agent:
                        answer_entry['selected_agent'] = selected_agent

                    response_count = result.get('total_responses', 0)
                    if response_count > 0:
                        answer_entry['agent_responses_used'] = response_count

                results["results"].append(answer_entry)

                # Enhanced incremental saving with progress tracking
                if quality_score >= 0.6 and processing_mode == 'simple_multi_agent':
                    print(f"💾 Saving multi-agent final answer (quality: {quality_score:.2f})")
                    save_results(results, output_path)
                    save_answers_only(results, output_path)
                elif quality_score >= 0.75:  # Also save high-quality single-agent results
                    save_results(results, output_path)
                    save_answers_only(results, output_path)
                
                # Save progress every 3 problems to prevent data loss
                if (i + 1) % 3 == 0:
                    print(f"📁 Progress saved: {i + 1}/{total_problems} problems completed")
                    save_results(results, output_path)
                    save_answers_only(results, output_path)

                # Determine status icon based on quality and warnings
                if quality_score >= 0.7:
                    status_icon = "✅"
                elif quality_score >= 0.3 or warning:
                    status_icon = "⚠️"
                else:
                    status_icon = "🔶"

                print(f"{status_icon} Problem {i+1} completed in {execution_time:.2f}s")
                if quality_score > 0:
                    print(f"   Quality: {quality_score:.2f}")
                if warning:
                    print(f"   Warning: {warning}")
            else:
                # Try to extract any useful information even from failed responses
                preserved_content = _extract_useful_content(solution, task_type)

                if preserved_content:
                    print(f"🔶 Problem {i+1} partially preserved: extracted useful content")
                    answer_entry = {
                        "id": f"partial_{i}",
                        "prompt": prompt,
                        "solution": preserved_content,
                        "execution_time": execution_time,
                        "problem_index": i,
                        "partial_preservation": True,
                        "original_failed_response": solution[:200] + "..." if len(solution) > 200 else solution
                    }
                    results["results"].append(answer_entry)
                    save_results(results, output_path)
                else:
                    print(f"⚠️ Problem {i+1} low quality but has answer: {solution[:100]}...")
                    # Save as regular result since we have an answer, just mark as low quality
                    answer_entry = {
                        "id": f"lowq_{i}",
                        "prompt": prompt,
                        "solution": solution,
                        "execution_time": execution_time,
                        "problem_index": i,
                        "quality_score": quality_score if 'quality_score' in locals() else 0.3,
                        "low_quality": True,  # 标记为低质量但不是失败
                        "partial_results_available": partial_results_available,
                        "worker_responses_available": worker_responses_available
                    }
                    results["results"].append(answer_entry)
                    save_results(results, output_path)

        except Exception as e:
            print(f"❌ Error processing problem {i+1}: {e}")
            continue

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description="Multi-Agent Chain of Thought Framework")
    
    # Input options
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("--prompt", type=str, help="Discussion question or prompt")
    group.add_argument("--jsonl", type=str, help="JSONL file containing multiple problems to process")
    
    # Output options
    parser.add_argument("--output", type=str, help="Custom output file path")
    parser.add_argument("--models", nargs="*", help="List of models to use")
    
    # Processing options
    parser.add_argument("--no-cache", action="store_true", help="Disable all caching")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    parser.add_argument("--config", type=str, help="Load custom configuration file")
    parser.add_argument("--task-type", type=str, help="Manually specify task type")
    parser.add_argument("--max_problems", type=int, help="Maximum number of problems to process")
    parser.add_argument("--max_rounds", type=int, default=1, help="Maximum thinking rounds")
    parser.add_argument("--context-optimization", action="store_true", help="Enable Enhanced Collaborative processing (always enabled)")
    parser.add_argument("--no-context-optimization", action="store_true", help="Disable context optimization (deprecated)")

    # Ablation study 参数
    parser.add_argument('--baseline-mode', action='store_true',
                        help='Run in baseline mode (single model)')
    parser.add_argument('--disable-merge', action='store_true',
                        help='Disable merge functionality (pick longest draft)')
    parser.add_argument('--disable-annotation', action='store_true',
                        help='Disable annotation system (workers only generate initial drafts)')
    parser.add_argument('--disable-compressor', action='store_true',
                        help='Disable context compression')
    
    # Full DMC configuration arguments
    parser.add_argument('--enable-enhanced-collaborative', action='store_true',
                        help='Enable enhanced collaborative system with annotations')
    parser.add_argument('--enable-merge-agent', action='store_true',
                        help='Enable intelligent merge agent')
    parser.add_argument('--enable-annotation-system', action='store_true',
                        help='Enable annotation system')
    parser.add_argument('--enable-context-compression', action='store_true',
                        help='Enable context compression')
    parser.add_argument('--collaborative-max-rounds', type=int, default=3,
                        help='Maximum collaborative rounds')
    parser.add_argument('--enhanced-max-annotation-rounds', type=int, default=2,
                        help='Maximum annotation rounds')
    parser.add_argument('--enhanced-consensus-threshold', type=float, default=0.7,
                        help='Consensus threshold for enhanced system')
    parser.add_argument('--enhanced-max-global-iterations', type=int, default=3,
                        help='Maximum global iterations for enhanced system')

    # Agent role assignment options
    parser.add_argument("--leader", type=str, help="Specify leader agent (e.g., openai, anthropic, llama, gemini)")
    parser.add_argument("--merge-agent-model", type=str, help="Specify merge agent model (e.g., openai, anthropic, llama, gemini)")
    
    # Collaborative system options (always enabled)
    parser.add_argument("--max-collaboration-rounds", type=int, default=3, help="Maximum collaboration rounds")
    parser.add_argument("--min-consensus-score", type=float, default=0.7, help="Minimum consensus score required")

    return parser.parse_args()

def setup_configuration(args):
    """Setup configuration based on arguments"""
    if args.config:
        config.load_config_from_file(args.config)
    
    if args.debug:
        config.update_config("debug", True)
        print("Debug mode enabled")
    
    if args.no_cache:
        config.update_config("cache", False)
        print("Caching disabled")
    
    # Set default configurations
    config.update_config("enable_dynamic_thinking", True)
    config.update_config("consensus_enabled", True)
    config.update_config("enable_intelligent_mode", True)
    
    # Handle merge agent model configuration
    if hasattr(args, 'merge_agent_model') and args.merge_agent_model:
        config.update_config("merge_agent_model", args.merge_agent_model)
        print(f"Merge agent model set to: {args.merge_agent_model}")
    
    # Handle leader agent configuration
    if hasattr(args, 'leader') and args.leader:
        config.update_config("leader_agent", args.leader)
        print(f"Leader agent set to: {args.leader}")
    
    if args.max_rounds:
        config.update_config("max_thinking_rounds", args.max_rounds)
        print(f"Maximum thinking rounds set to: {args.max_rounds}")
    else:
        config.update_config("max_thinking_rounds", 1)
        print("Maximum thinking rounds set to: 1")
    
    # Handle context optimization arguments
    if hasattr(args, 'context_optimization') and args.context_optimization:
        config.update_config("enable_context_engineering", True)
        # Memory system simplified to lightweight cache
        print("🚀 Simplified multi-agent system enabled")
    elif hasattr(args, 'no_context_optimization') and args.no_context_optimization:
        print("⚠️ Context optimization disable flag ignored - simplified multi-agent system is always enabled")

    # Collaborative system configuration (always enabled)
    if hasattr(args, 'max_collaboration_rounds') and args.max_collaboration_rounds:
        config.update_config("max_collaboration_rounds", args.max_collaboration_rounds)
        print(f"Maximum collaboration rounds set to: {args.max_collaboration_rounds}")

    if hasattr(args, 'min_consensus_score') and args.min_consensus_score:
        config.update_config("min_consensus_score", args.min_consensus_score)
        print(f"Minimum consensus score set to: {args.min_consensus_score}")

    # Handle ablation study arguments
    if hasattr(args, 'baseline_mode') and args.baseline_mode:
        config.update_config("baseline_mode", True)
        print("🔬 Baseline mode enabled (single model)")

    if hasattr(args, 'disable_merge') and args.disable_merge:
        config.update_config("enhanced_enable_merge_agent", False)
        print("🔬 Merge agent disabled")

    if hasattr(args, 'disable_annotation') and args.disable_annotation:
        config.update_config("enhanced_enable_annotation", False)
        print("🔬 Annotation system disabled")

    if hasattr(args, 'disable_compressor') and args.disable_compressor:
        config.update_config("enhanced_enable_context_compression", False)
        print("🔬 Context compression disabled")
    
    # Handle full DMC configuration arguments
    if hasattr(args, 'enable_enhanced_collaborative') and args.enable_enhanced_collaborative:
        config.update_config("use_enhanced_collaborative", True)
        print("🚀 Enhanced collaborative system enabled")
    
    if hasattr(args, 'enable_merge_agent') and args.enable_merge_agent:
        config.update_config("enhanced_enable_merge_agent", True)
        print("🤖 Intelligent merge agent enabled")
    
    if hasattr(args, 'enable_annotation_system') and args.enable_annotation_system:
        config.update_config("enhanced_enable_annotation", True)
        print("📝 Annotation system enabled")
    
    if hasattr(args, 'enable_context_compression') and args.enable_context_compression:
        config.update_config("enhanced_enable_context_compression", True)
        print("🗜️ Context compression enabled")
    
    if hasattr(args, 'collaborative_max_rounds') and args.collaborative_max_rounds:
        config.update_config("collaborative_max_rounds", args.collaborative_max_rounds)
        print(f"🔄 Maximum collaborative rounds set to: {args.collaborative_max_rounds}")
    
    if hasattr(args, 'enhanced_max_annotation_rounds') and args.enhanced_max_annotation_rounds:
        config.update_config("enhanced_max_annotation_rounds", args.enhanced_max_annotation_rounds)
        print(f"📝 Maximum annotation rounds set to: {args.enhanced_max_annotation_rounds}")
    
    if hasattr(args, 'enhanced_consensus_threshold') and args.enhanced_consensus_threshold:
        config.update_config("enhanced_consensus_threshold", args.enhanced_consensus_threshold)
        print(f"🎯 Enhanced consensus threshold set to: {args.enhanced_consensus_threshold}")
    
    if hasattr(args, 'enhanced_max_global_iterations') and args.enhanced_max_global_iterations:
        config.update_config("enhanced_max_global_iterations", args.enhanced_max_global_iterations)
        print(f"🌐 Maximum global iterations set to: {args.enhanced_max_global_iterations}")

    print("🤝 Collaborative multi-agent system active")

def get_active_models(args):
    """Get list of active models"""
    if args.models:
        return args.models
    else:
        return ["openai", "anthropic"]  # Default models

async def process_single_prompt(args):
    """Process a single prompt"""
    models = get_active_models(args)
    task_type = args.task_type or "standard"
    
    print(f"Processing prompt with models: {models}")
    print(f"Task type: {task_type}")
    
    try:
        result = await process_with_context_optimization(
            task_content=args.prompt,
            task_type=task_type,
            available_agents=models
        )
        
        print(f"\n🎯 Result:")
        print(f"Solution: {result.get('solution', 'No solution')}")
        print(f"Processing time: {result.get('total_processing_time', 0):.2f}s")
        
    except Exception as e:
        print(f"❌ Error processing prompt: {e}")

async def process_jsonl_file(args):
    """Process JSONL file"""
    models = get_active_models(args)
    task_type = args.task_type or detect_task_type(args.jsonl)
    
    print(f"Processing JSONL file: {args.jsonl}")
    print(f"Models: {models}")
    print(f"Task type: {task_type}")
    
    # Load problems
    problems = load_problems_from_jsonl(args.jsonl)
    if not problems:
        print("No problems found in file")
        return
    
    if args.max_problems:
        problems = problems[:args.max_problems]
        print(f"Limited to first {args.max_problems} problems")
    
    # Setup output file
    if args.output:
        output_path = args.output
    else:
        base_name = os.path.splitext(os.path.basename(args.jsonl))[0]
        model_suffix = "-".join(models)
        output_path = os.path.join(RESULTS_DIR, f"{base_name}-{model_suffix}.json")
    
    # Load or create results structure
    results = load_existing_results(output_path) or {
        "metadata": {
            "timestamp": datetime.now().isoformat(),
            "models_used": models,
            "task_type": task_type
        },
        "results": []
    }
    
    print(f"Output file: {output_path}")
    
    # Process problems
    await process_problems(problems, results, output_path, models, task_type, args)

    # Optimize final results to keep only the best answers
    optimized_results = optimize_final_results(results)

    # Final save with optimized results
    save_results(optimized_results, output_path)
    save_answers_only(optimized_results, output_path)

    print(f"\n✅ All {len(problems)} problems processed!")
    print(f"Results saved to {output_path}")

async def async_main():
    """Main async function"""
    # Get absolute path to project root
    project_root = os.path.dirname(os.path.abspath(__file__))
    results_dir = os.path.join(project_root, RESULTS_DIR)
    benchmark_dir = os.path.join(project_root, BENCHMARK_DIR)

    os.makedirs(results_dir, exist_ok=True)
    os.makedirs(benchmark_dir, exist_ok=True)
    
    args = parse_arguments()
    setup_configuration(args)
    
    if args.prompt:
        await process_single_prompt(args)
    elif args.jsonl:
        await process_jsonl_file(args)

def main():
    """Main entry point"""
    try:
        asyncio.run(async_main())
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        sys.exit(0)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
