import re
from math import isclose
from typing import Any, Dict, List, Tuple, Optional

# Optional sympy import - fallback to basic math if not available
try:
    import sympy
    from sympy import N, simplify
    from sympy.parsing.sympy_parser import parse_expr
    from sympy.parsing.latex import parse_latex
    SYMPY_AVAILABLE = True
except ImportError:
    SYMPY_AVAILABLE = False
    print("Warning: sympy not available, using basic math evaluation")

from .base_evaluator import BaseEvaluator

class GSM8KEvaluator(BaseEvaluator):
    def __init__(self, log_path: str = "results"):
        super().__init__("GSM8K", log_path)

    def extract_answer(self, text: str) -> str:
        """Enhanced answer extraction combining gsm8k.py logic with robust patterns"""

        # Method 1: GSM8K standard #### format (highest priority)
        if "####" in text:
            answer = text.split("####")[1].strip()
            # Clean the answer
            answer = self._clean_answer(answer)
            return answer

        # Method 2: Skip utils.answer_extraction (has issues with comma-separated numbers)
        # The utils module doesn't handle "1,234.56" correctly, so we skip it for GSM8K

        # Method 3: Enhanced pattern matching (fixed regex patterns)
        answer_patterns = [
            # "So the answer is X" patterns
            r'So the answer is\s*\$?([+-]?\d+(?:,\d{3})*(?:\.\d+)?)',
            r'The answer is\s*\$?([+-]?\d+(?:,\d{3})*(?:\.\d+)?)',
            r'Therefore,?\s*the answer is\s*\$?([+-]?\d+(?:,\d{3})*(?:\.\d+)?)',
            r'Thus,?\s*the answer is\s*\$?([+-]?\d+(?:,\d{3})*(?:\.\d+)?)',

            # Final answer patterns
            r'final answer[:\s]*\$?([+-]?\d+(?:,\d{3})*(?:\.\d+)?)',

            # Specific context patterns (more precise)
            r'(?:total cost|cost)\s+is\s*\$?([+-]?\d+(?:,\d{3})*(?:\.\d+)?)',
            r'Total:\s*\$?([+-]?\d+(?:,\d{3})*(?:\.\d+)?)',
            r'(?:can buy|will pay|costs?|makes?|needs?)\s*\$?([+-]?\d+(?:,\d{3})*(?:\.\d+)?)',

            # Decimal preference patterns (for cases like "1/2 or 0.5")
            r'or\s+([+-]?\d+\.\d+)',  # Prefer decimal after "or"
            r'(?:result|answer)\s+is\s+\d+/\d+\s+or\s+([+-]?\d+\.\d+)',  # Specific fraction-decimal pattern

            # General answer patterns
            r'(?:answer|result|solution)\s*(?:is|=|:)\s*\$?([+-]?\d+(?:,\d{3})*(?:\.\d+)?)',

            # Context with units
            r'Therefore.*?\$?([+-]?\d+(?:,\d{3})*(?:\.\d+)?)\s*(?:dollars?|cents?|gumballs?|vlogs?|questions?|bees?|percent?|%)',
            r'Thus.*?\$?([+-]?\d+(?:,\d{3})*(?:\.\d+)?)\s*(?:dollars?|cents?|gumballs?|vlogs?|questions?|bees?|percent?|%)',

            # End of sentence patterns
            r'\$?([+-]?\d+(?:,\d{3})*(?:\.\d+)?)\s*(?:dollars?|cents?|gumballs?|vlogs?|questions?|bees?|pounds?|percent?|%)\s*\.?\s*$'
        ]

        for pattern in answer_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                extracted = self._clean_answer(matches[-1])
                return extracted  # Return immediately if pattern matches

        # Method 4: Look for numbers in conclusion context, avoiding time references
        sentences = text.split(".")
        for sentence in reversed(sentences[-3:]):  # Check last 3 sentences
            # Skip sentences with time references
            if re.search(r'\b(?:hour|day|week|month|year|minute|second)s?\b', sentence, re.IGNORECASE):
                continue

            # Look for numbers in conclusion sentences
            if re.search(r'(?:therefore|thus|hence|answer|result|total|final)', sentence, re.IGNORECASE):
                # Use improved number extraction
                numbers = re.findall(r'[+-]?\d+(?:,\d{3})*(?:\.\d+)?', sentence)
                if numbers:
                    return self._clean_answer(numbers[-1])

        # Method 5: Extract all numbers but avoid obvious time/measurement references at the end
        lines = text.strip().split('\n')
        for line in reversed(lines[-5:]):  # Check last 5 lines
            if re.search(r'\b(?:hour|day|week|month|year|minute|second)s?\s*\.?\s*$', line, re.IGNORECASE):
                continue
            numbers = re.findall(r'[+-]?\d+(?:,\d{3})*(?:\.\d+)?', line)
            if numbers:
                return self._clean_answer(numbers[-1])

        # Method 6: Fallback - return cleaned last sentence
        sentences = text.split(".")
        last_sentence = sentences[-1].strip() if sentences else text.strip()
        return self._clean_answer(last_sentence)

    def _clean_answer(self, answer: str) -> str:
        """Clean answer using gsm8k.py logic"""
        if not answer:
            return ""

        # Remove common formatting characters
        cleaned = str(answer).strip()
        cleaned = cleaned.replace(",", "")  # Remove commas
        cleaned = cleaned.replace("$", "")  # Remove dollar signs
        cleaned = cleaned.replace("%", "")  # Remove percent signs
        cleaned = cleaned.replace("(", "").replace(")", "")  # Remove parentheses

        # Remove trailing periods and spaces
        cleaned = cleaned.rstrip(". ")

        return cleaned

    def is_number(self, value: str) -> bool:
        cleaned = re.sub(r'[$,\s\(\)]', '', value)

        try:
            float(cleaned)
            return True
        except ValueError:
            if cleaned.endswith('%'):
                try:
                    float(cleaned[:-1])
                    return True
                except ValueError:
                    pass
            return False

    def normalize_number(self, value: str) -> float:
        cleaned = re.sub(r'[$,\s\(\)]', '', value)

        if cleaned.endswith('%'):
            try:
                return float(cleaned[:-1]) / 100
            except ValueError:
                pass

        if "/" in cleaned and not cleaned.startswith("-"):
            try:
                parts = cleaned.split("/")
                if len(parts) == 2:
                    return float(parts[0]) / float(parts[1])
            except ValueError:
                pass

        try:
            return float(cleaned)
        except ValueError:
            return 0.0

    def symbolic_equal(self, a: str, b: str) -> bool:
        if not SYMPY_AVAILABLE:
            # Fallback to basic numerical comparison
            try:
                if self.is_number(str(a)) and self.is_number(str(b)):
                    return isclose(self.normalize_number(str(a)), self.normalize_number(str(b)), abs_tol=1e-3)
                return str(a).strip() == str(b).strip()
            except:
                return False

        def _parse(s):
            for f in [parse_latex, parse_expr]:
                try:
                    return f(s)
                except:
                    pass
            return s

        try:
            a_parsed = _parse(a)
            b_parsed = _parse(b)

            try:
                if simplify(a_parsed - b_parsed) == 0:
                    return True
            except:
                pass

            try:
                if isclose(float(N(a_parsed)), float(N(b_parsed)), abs_tol=1e-3):
                    return True
            except:
                pass
        except:
            pass

        return False

    def calculate_score(self, expected_output: Any, prediction: Any) -> Tuple[float, Any]:
        """Enhanced scoring using gsm8k.py logic with fallbacks"""

        # Extract answers
        extracted_answer = self.extract_answer(str(prediction))

        if not self.is_number(expected_output):
            expected_extracted = self.extract_answer(str(expected_output))
        else:
            expected_extracted = str(expected_output)

        # Clean both answers
        extracted_cleaned = self._clean_answer(extracted_answer)
        expected_cleaned = self._clean_answer(expected_extracted)

        # Method 1: Direct string comparison (gsm8k.py style)
        if extracted_cleaned == expected_cleaned:
            return 1.0, extracted_answer

        # Method 2: Extract numbers and compare (gsm8k.py style)
        extracted_number = self._extract_number_from_string(extracted_cleaned)
        if extracted_number and str(extracted_number) == expected_cleaned:
            return 1.0, extracted_answer

        # Method 3: Float comparison (gsm8k.py style)
        try:
            if extracted_number and float(extracted_number) == float(expected_cleaned):
                return 1.0, extracted_answer
        except (ValueError, TypeError):
            pass

        # Method 4: Fallback to original robust comparison
        if self.is_number(extracted_cleaned) and self.is_number(expected_cleaned):
            extracted_num = self.normalize_number(extracted_cleaned)
            expected_num = self.normalize_number(expected_cleaned)

            if isclose(extracted_num, expected_num, abs_tol=1e-3):
                return 1.0, extracted_answer

        # Method 5: Symbolic comparison (if available)
        try:
            if self.symbolic_equal(extracted_cleaned, expected_cleaned):
                return 1.0, extracted_answer
        except:
            pass

        return 0.0, extracted_answer

    def _extract_number_from_string(self, text: str) -> str:
        """Extract number from string (gsm8k.py style)"""
        if not text:
            return ""

        # Clean the text
        text = str(text).strip()

        # Find numbers in the text
        numbers = re.findall(r'[+-]?\d+(?:\.\d+)?', text)

        if numbers:
            return numbers[-1]  # Return the last number found

        return ""

    async def evaluate_problem(self, problem: Dict[str, Any], results: List[Dict[str, Any]]) -> Tuple[str, str, str, float, float]:
        question = problem.get("question", "")
        answer = problem.get("answer", "")

        matched_result = self.find_matching_result(problem, results, "question", "prompt")

        if matched_result:
            prediction = (matched_result.get("solution", "") or
                         matched_result.get("consensus", "") or
                         matched_result.get("answer", ""))  # Support baseline answer field
            score, extracted_prediction = self.calculate_score(answer, prediction)

            if score < 1.0:
                self.log_mismatch(question, answer, prediction, extracted_prediction)

            cost = self.get_execution_cost(matched_result)
            return question, prediction, answer, score, cost

        return question, "", answer, 0.0, 0.0

    def get_result_columns(self) -> List[str]:
        return ["question", "prediction", "expected_answer", "score", "cost"]