#!/usr/bin/env python3
"""
GPQA (Graduate-Level Google-Proof Q&A) Evaluator

GPQA is a challenging dataset of graduate-level multiple-choice questions
in biology, chemistry, and physics that are designed to be difficult
even for experts with access to Google.
"""

import re
from typing import Dict, Any, List, Optional, Tuple
from .base_evaluator import BaseEvaluator


class GPQAEvaluator(BaseEvaluator):
    """Evaluator for GPQA dataset"""

    def __init__(self, log_path: str = "results"):
        super().__init__("GPQA", log_path)
        
    def extract_answer(self, response: str) -> str:
        """
        Extract answer from model response for GPQA
        GPQA uses multiple choice format (A, B, C, D)
        """
        if not response or not response.strip():
            return ""
            
        response = response.strip()
        
        # Pattern 1: "So the answer is X" format
        answer_patterns = [
            r'(?:so\s+)?(?:the\s+)?answer\s+is\s+([A-D])',
            r'(?:therefore|thus|hence),?\s+(?:the\s+)?answer\s+is\s+([A-D])',
            r'(?:the\s+)?(?:correct\s+)?answer\s+is\s+([A-D])',
            r'(?:the\s+)?(?:final\s+)?answer:\s*([A-D])',
        ]
        
        for pattern in answer_patterns:
            matches = re.findall(pattern, response, re.IGNORECASE)
            if matches:
                return matches[-1].upper()  # Take the last match
        
        # Pattern 2: Look for standalone letter choices
        # Find all occurrences of A, B, C, D that might be answers
        letter_pattern = r'\b([A-D])\b'
        letters = re.findall(letter_pattern, response)
        
        if letters:
            # Take the last occurrence as it's likely the final answer
            return letters[-1].upper()
        
        # Pattern 3: Look for "option X" or "choice X"
        option_patterns = [
            r'(?:option|choice)\s+([A-D])',
            r'([A-D])\s+is\s+(?:the\s+)?(?:correct|right)',
            r'select\s+([A-D])',
            r'choose\s+([A-D])'
        ]
        
        for pattern in option_patterns:
            matches = re.findall(pattern, response, re.IGNORECASE)
            if matches:
                return matches[-1].upper()
        
        return ""
    
    def normalize_answer(self, answer: str) -> str:
        """Normalize answer for comparison"""
        if not answer:
            return ""
        
        # For GPQA, answers should be single letters A, B, C, or D
        answer = answer.strip().upper()
        
        # Ensure it's a valid choice
        if answer in ['A', 'B', 'C', 'D']:
            return answer
        
        return ""
    
    def is_correct(self, predicted: str, ground_truth: str) -> bool:
        """Check if predicted answer matches ground truth"""
        pred_normalized = self.normalize_answer(predicted)
        gt_normalized = self.normalize_answer(ground_truth)
        
        if not pred_normalized or not gt_normalized:
            return False
        
        return pred_normalized == gt_normalized
    
    def evaluate_single(self, predicted: str, ground_truth: str, 
                       problem_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Evaluate a single GPQA prediction"""
        
        extracted_answer = self.extract_answer(predicted)
        is_correct = self.is_correct(extracted_answer, ground_truth)
        
        result = {
            'predicted_raw': predicted,
            'predicted_answer': extracted_answer,
            'ground_truth': ground_truth,
            'correct': is_correct,
            'extraction_successful': bool(extracted_answer),
        }
        
        # Add problem metadata if available
        if problem_data:
            result.update({
                'problem_id': problem_data.get('id', ''),
                'subject': problem_data.get('subject', ''),
                'difficulty': problem_data.get('difficulty', ''),
                'question': problem_data.get('question', '')[:100] + '...' if problem_data.get('question') else ''
            })
        
        return result
    
    def get_evaluation_metrics(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate evaluation metrics for GPQA"""
        
        if not results:
            return {
                'accuracy': 0.0,
                'total_problems': 0,
                'correct_answers': 0,
                'extraction_rate': 0.0,
                'subject_breakdown': {},
                'difficulty_breakdown': {}
            }
        
        total = len(results)
        correct = sum(1 for r in results if r.get('correct', False))
        extracted = sum(1 for r in results if r.get('extraction_successful', False))
        
        # Subject-wise breakdown
        subject_stats = {}
        for result in results:
            subject = result.get('subject', 'unknown')
            if subject not in subject_stats:
                subject_stats[subject] = {'total': 0, 'correct': 0}
            subject_stats[subject]['total'] += 1
            if result.get('correct', False):
                subject_stats[subject]['correct'] += 1
        
        # Calculate subject accuracies
        subject_breakdown = {}
        for subject, stats in subject_stats.items():
            subject_breakdown[subject] = {
                'accuracy': stats['correct'] / stats['total'] if stats['total'] > 0 else 0.0,
                'correct': stats['correct'],
                'total': stats['total']
            }
        
        # Difficulty-wise breakdown
        difficulty_stats = {}
        for result in results:
            difficulty = result.get('difficulty', 'unknown')
            if difficulty not in difficulty_stats:
                difficulty_stats[difficulty] = {'total': 0, 'correct': 0}
            difficulty_stats[difficulty]['total'] += 1
            if result.get('correct', False):
                difficulty_stats[difficulty]['correct'] += 1
        
        difficulty_breakdown = {}
        for difficulty, stats in difficulty_stats.items():
            difficulty_breakdown[difficulty] = {
                'accuracy': stats['correct'] / stats['total'] if stats['total'] > 0 else 0.0,
                'correct': stats['correct'],
                'total': stats['total']
            }
        
        return {
            'accuracy': correct / total,
            'total_problems': total,
            'correct_answers': correct,
            'extraction_rate': extracted / total,
            'subject_breakdown': subject_breakdown,
            'difficulty_breakdown': difficulty_breakdown
        }
    
    def format_results(self, metrics: Dict[str, Any]) -> str:
        """Format evaluation results for display"""
        
        lines = [
            f"📊 GPQA Evaluation Results",
            f"=" * 50,
            f"Overall Accuracy: {metrics['accuracy']:.1%}",
            f"Correct Answers: {metrics['correct_answers']}/{metrics['total_problems']}",
            f"Answer Extraction Rate: {metrics['extraction_rate']:.1%}",
            ""
        ]
        
        # Subject breakdown
        if metrics.get('subject_breakdown'):
            lines.append("📚 Subject Breakdown:")
            for subject, stats in metrics['subject_breakdown'].items():
                lines.append(f"  {subject}: {stats['accuracy']:.1%} ({stats['correct']}/{stats['total']})")
            lines.append("")
        
        # Difficulty breakdown
        if metrics.get('difficulty_breakdown'):
            lines.append("🎯 Difficulty Breakdown:")
            for difficulty, stats in metrics['difficulty_breakdown'].items():
                lines.append(f"  {difficulty}: {stats['accuracy']:.1%} ({stats['correct']}/{stats['total']})")
        
        return "\n".join(lines)

    async def evaluate_problem(self, problem: Dict[str, Any], results: Any) -> Tuple[Any, ...]:
        """Evaluate a single GPQA problem (required by BaseEvaluator)"""

        # Handle both dict and list formats for results
        if isinstance(results, dict):
            results_list = results.get("results", [])
        elif isinstance(results, list):
            results_list = results
        else:
            results_list = []

        # Find the matching result for this problem
        # GPQA uses "question" field in benchmark, "prompt" field in results
        matching_result = self.find_matching_result(problem, results_list, problem_key="question", result_key="prompt")

        if not matching_result:
            return (
                problem.get("question", "")[:50] + "...",
                problem.get("answer", ""),
                "",
                "",
                "NO_RESULT",
                0.0,
                problem.get("subject", ""),
                problem.get("difficulty", "")
            )

        predicted_text = matching_result.get("solution", "")
        ground_truth = problem.get("answer", "")

        extracted_answer = self.extract_answer(predicted_text)
        is_correct = self.is_correct(extracted_answer, ground_truth)

        return (
            problem.get("question", "")[:50] + "...",
            ground_truth,
            predicted_text[:100] + "..." if len(predicted_text) > 100 else predicted_text,
            extracted_answer,
            "PASS" if is_correct else "FAIL",
            1.0 if is_correct else 0.0,
            problem.get("subject", ""),
            problem.get("difficulty", "")
        )

    def calculate_score(self, expected_output: Any, prediction: Any) -> Tuple[float, Any]:
        """Calculate score for a prediction (required by BaseEvaluator)"""

        if isinstance(prediction, str):
            extracted = self.extract_answer(prediction)
        else:
            extracted = str(prediction)

        is_correct = self.is_correct(extracted, str(expected_output))
        score = 1.0 if is_correct else 0.0

        return score, extracted

    def get_result_columns(self) -> List[str]:
        """Get column names for results CSV (required by BaseEvaluator)"""
        return [
            "question_preview",
            "ground_truth",
            "prediction_preview",
            "extracted_answer",
            "result",
            "score",
            "subject",
            "difficulty"
        ]
