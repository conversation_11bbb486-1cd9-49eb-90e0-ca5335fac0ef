import json
import os
import asyncio
from abc import ABC, abstractmethod
from datetime import datetime
from pathlib import Path
from typing import Any, Callable, Dict, List, Tuple, Optional

import pandas as pd
from tqdm.asyncio import tqdm_asyncio

class BaseEvaluator(ABC):
    def __init__(self, name: str, log_path: str = "results"):
        self.name = name
        self.log_path = log_path
        os.makedirs(self.log_path, exist_ok=True)

    PASS = "PASS"
    FAIL = "FAIL"

    def log_warning(self, message: str):
        print(f"[WARNING] {message}")

        log_file = os.path.join(self.log_path, "log.txt")
        with open(log_file, "a", encoding="utf-8") as f:
            f.write(f"WARNING: {message}\n")

    def load_results(self, file_path: str) -> List[Dict[str, Any]]:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if "results" in data:
                    return data["results"]
                return data
        except Exception as e:
            print(f"Error loading results from {file_path}: {e}")
            return []

    def save_results_to_csv(self, results: List[Tuple[Any, ...]], columns: List[str], prefix: str = ""):
        df = pd.DataFrame(results, columns=columns)

        # Always use filename without timestamp
        filename = f"{prefix}_{self.name}.csv"

        output_file = os.path.join(self.log_path, filename)

        df.to_csv(output_file, index=False)
        print(f"Results saved to {output_file}")

    def log_mismatch(self, problem: str, expected_output: Any, prediction: str, extracted_output: Any):
        # Disable mismatches.json generation
        pass

    def calculate_accuracy_for_answered(self, eval_results: List[Tuple[Any, ...]]) -> Tuple[float, int, int]:
        columns = self.get_result_columns()
        score_idx = columns.index("score") if "score" in columns else 3
        prediction_idx = 1

        answered = 0
        correct = 0

        for result in eval_results:
            if len(result) > prediction_idx and result[prediction_idx]:
                answered += 1
                if result[score_idx] == 1.0:
                    correct += 1

        accuracy = 0.0
        if answered > 0:
            accuracy = correct / answered

        return accuracy, answered, correct

    @abstractmethod
    async def evaluate_problem(self, problem: Dict[str, Any], results: Dict[str, Any]) -> Tuple[Any, ...]:
        pass

    @abstractmethod
    def calculate_score(self, expected_output: Any, prediction: Any) -> Tuple[float, Any]:
        pass

    @abstractmethod
    def get_result_columns(self) -> List[str]:
        pass

    def find_matching_result(self, problem: Dict[str, Any], results: List[Dict[str, Any]],
                           problem_key: str = "prompt", result_key: str = "prompt") -> Optional[Dict[str, Any]]:
        problem_text = problem.get(problem_key, "")
        problem_id = problem.get("id", "")

        for result in results:
            if problem_id and result.get("id") == problem_id:
                return result

        for result in results:
            if result.get(result_key, "") == problem_text:
                return result

        if len(problem_text) > 30:
            clean_problem = ' '.join(problem_text.split())
            for result in results:
                result_text = result.get(result_key, "")
                clean_result = ' '.join(result_text.split())

                if clean_problem == clean_result:
                    return result

                if problem_text in result_text or result_text in problem_text:
                    return result

                if len(clean_problem) > 100 and clean_problem[:100] in clean_result:
                    return result
                if len(clean_result) > 100 and clean_result[:100] in clean_problem:
                    return result

        return None

    def get_execution_cost(self, result: Dict[str, Any]) -> float:
        if "execution_time_seconds" in result:
            return result.get("execution_time_seconds", 0.0)
        return result.get("execution_time", 0.0)

    async def evaluate_problems(self, problems: List[Dict[str, Any]], results: Dict[str, Any],
                               max_concurrent_tasks: int = 50) -> List[Tuple[Any, ...]]:
        semaphore = asyncio.Semaphore(max_concurrent_tasks)

        async def sem_evaluate(problem):
            async with semaphore:
                return await self.evaluate_problem(problem, results)

        tasks = [sem_evaluate(problem) for problem in problems]
        return await tqdm_asyncio.gather(*tasks, desc=f"Evaluating {self.name} problems", total=len(problems))

    async def run_evaluation(self, problems: List[Dict[str, Any]], results: Dict[str, Any],
                           max_concurrent_tasks: int = 50, prefix: str = ""):
        processed_problems = []
        result_ids = {}
        result_prompts = {}

        for result in results:
            if "id" in result:
                result_ids[result["id"]] = result
            if "prompt" in result:
                result_prompts[result["prompt"]] = result

        max_problems_to_match = len(results)
        problems_matched = 0

        dataset_type = self.name.lower()

        for problem in problems:
            if problems_matched >= max_problems_to_match:
                break

            problem_id = problem.get("id", "")
            if problem_id and problem_id in result_ids:
                processed_problems.append(problem)
                problems_matched += 1
                continue

            question = problem.get("question", "")
            context = problem.get("context", "")

            if dataset_type == "hotpotqa":
                if question:
                    for result in results:
                        result_prompt = result.get("prompt", "")
                        if question in result_prompt:
                            processed_problems.append(problem)
                            problems_matched += 1
                            break
                    continue

            elif dataset_type == "math":
                problem_text = problem.get("problem", "").strip()
                if problem_text:
                    for result in results:
                        result_prompt = result.get("prompt", "")
                        normalized_problem = ' '.join(problem_text.split()).lower()
                        normalized_prompt = ' '.join(result_prompt.split()).lower()

                        if normalized_problem == normalized_prompt:
                            processed_problems.append(problem)
                            problems_matched += 1
                            break

                        if normalized_problem in normalized_prompt or normalized_prompt in normalized_problem:
                            processed_problems.append(problem)
                            problems_matched += 1
                            break

                        if len(normalized_problem) > 30 and len(normalized_prompt) > 30:
                            problem_start = normalized_problem[:100]
                            prompt_start = normalized_prompt[:100]
                            if problem_start in prompt_start or prompt_start in problem_start:
                                processed_problems.append(problem)
                                problems_matched += 1
                                break
                    continue

            # Generate test prompts for other types of problems
            if dataset_type == "hotpotqa":
                if isinstance(context, list) or (isinstance(context, str) and (context.startswith('[') or context.startswith('{'))):
                    test_prompt = f"Context:\n{context}\n\nQuestion: {question}"
                else:
                    if not question and isinstance(context, str) and "Question:" in context:
                        parts = context.split("Question:")
                        context = parts[0].strip()
                        question = parts[1].strip()
                    test_prompt = f"Context:\n{context}\n\nQuestion: {question}"

            else:
                test_prompt = problem.get("prompt", "")
                if not test_prompt:
                    test_prompt = question

            found_match = False

            if test_prompt in result_prompts:
                processed_problems.append(problem)
                found_match = True
                problems_matched += 1
                continue

            if not found_match:
                normalized_test = ' '.join(test_prompt.split())
                for result_prompt in result_prompts.keys():
                    normalized_result = ' '.join(result_prompt.split())
                    if normalized_test == normalized_result:
                        processed_problems.append(problem)
                        found_match = True
                        problems_matched += 1
                        break

                    # For shorter prompts, check for substring matches
                    if len(normalized_test) > 30 and normalized_test in normalized_result:
                        processed_problems.append(problem)
                        found_match = True
                        problems_matched += 1
                        break

        if not processed_problems:
            self.log_warning(f"For {self.name}, no matching problems found in results. Check prompt formats or problem IDs.")
            return

        eval_results = await self.evaluate_problems(processed_problems, results, max_concurrent_tasks)
        columns = self.get_result_columns()

        accuracy, answered, correct = self.calculate_accuracy_for_answered(eval_results)
        print(f"\n{self.name} Evaluation Results:")
        print(f"Total Problems: {len(processed_problems)}")
        print(f"Answered Problems: {answered}")
        print(f"Correct Answers: {correct}")
        print(f"Accuracy: {accuracy:.2%} ({correct}/{answered} problems)")

        self.save_results_to_csv(eval_results, columns, prefix)