from typing import Any, Dict, List, Tuple

from .humaneval_evaluator import HumanEvalEvaluator

class MBPPEvaluator(HumanEvalEvaluator):
    def __init__(self, log_path: str = "results"):
        super().__init__(log_path)
        self.name = "MB<PERSON>"



    def _get_test_cases_for_mbpp(self, problem: Dict[str, Any]) -> str:
        test_cases = problem.get("test_list", [])
        if not test_cases and "test_case" in problem:
            test_cases = [problem["test_case"]]

        test_cases = [str(tc) for tc in test_cases if tc is not None]

        if not test_cases:
            return ""

        indented_test_case_block = "\n".join([f"        {tc}" for tc in test_cases])
        return f"""
def check(func):
    try:
        # Test cases
{indented_test_case_block}
        return True
    except AssertionError as e:
        return f"Assertion failed: {{e}}"
    except Exception as e:
        return f"Error: {{e}}"
"""

    async def evaluate_problem(self, problem: Dict[str, Any], results: List[Dict[str, Any]]) -> Tuple[str, str, str, float, float]:
        problem["test"] = self._get_test_cases_for_mbpp(problem)
        problem["canonical_solution"] = problem.get("code", "")
        problem["id"] = problem.get("task_id", "")

        return await super().evaluate_problem(problem, results)

    def get_result_columns(self) -> List[str]:
        return ["prompt", "prediction", "expected_output_details", "score", "cost"]