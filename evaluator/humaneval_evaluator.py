import asyncio
import re
import threading
from typing import Any, Callable, Dict, List, Optional, Tuple

from .base_evaluator import BaseEvaluator


class HumanEvalEvaluator(BaseEvaluator):
    class TimeoutError(Exception):
        pass

    def __init__(self, log_path: str = "results"):
        super().__init__("humaneval", log_path)

    def extract_code(self, text: str) -> str:
        # First try to extract from markdown code blocks
        code_block_pattern = r"```(?:python)?\s*(.*?)```"
        matches = re.findall(code_block_pattern, text, re.DOTALL)

        if matches:
            code = max(matches, key=len).strip()
            return code

        # Look for function definitions with better pattern matching
        func_patterns = [
            # Match complete function definitions
            r"(def\s+\w+\s*\([^)]*\)\s*:.*?)(?=\n\ndef|\n\nclass|\n\n[A-Z]|\Z)",
            # Match function definition until end of text or double newline
            r"(def\s+\w+\s*\([^)]*\)\s*:.*?)(?=\n\n|\Z)",
            # Match function definition line by line
            r"(def\s+\w+\s*\([^)]*\)\s*:\s*(?:\n(?:    .*|\s*))*)",
        ]

        for pattern in func_patterns:
            matches = re.findall(pattern, text, re.DOTALL)
            if matches:
                code = max(matches, key=len).strip()
                if code and 'def ' in code:
                    return code

        # If no code blocks, check if text starts with def (raw code)
        if text.strip().startswith("def "):
            return text.strip()

        # Fallback: try to find any def statement and take everything after it
        func_start = re.search(r"def\s+\w+\s*\(", text)
        if func_start:
            code_from_def = text[func_start.start():].strip()
            # Try to find the end of the function more intelligently
            lines = code_from_def.split('\n')
            if len(lines) > 1:
                # Keep lines that are indented or are the def line
                function_lines = [lines[0]]  # Always keep the def line
                for line in lines[1:]:
                    if line.strip() == "":
                        function_lines.append(line)
                    elif line.startswith("    ") or line.startswith("\t"):
                        function_lines.append(line)
                    elif line.strip().startswith("def ") or line.strip().startswith("class "):
                        break  # Stop at next function/class
                    elif not line.startswith(" ") and line.strip():
                        # Stop at non-indented non-empty line that's not a comment
                        if not line.strip().startswith("#"):
                            break
                        function_lines.append(line)
                    else:
                        function_lines.append(line)
                
                return '\n'.join(function_lines).strip()
            else:
                return code_from_def

        return text

    def run_with_timeout(self, func: Callable, args: tuple, timeout: int) -> Any:
        result = []
        stop_event = threading.Event()

        def target():
            try:
                result.append(func(*args))
            except Exception as e:
                result.append(e)
            finally:
                stop_event.set()

        thread = threading.Thread(target=target)
        thread.daemon = True
        thread.start()

        is_timeout = not stop_event.wait(timeout)

        if is_timeout:
            raise self.TimeoutError("Function execution timed out")

        if not result:
            return None

        if isinstance(result[0], Exception):
            raise result[0]

        return result[0]

    def sanitize_code(self, code: str, entrypoint: str) -> str:
        code = code.strip()

        if f"def {entrypoint}" not in code:
            func_match = re.search(r"def\s+(\w+)\s*\(", code)
            if func_match:
                actual_name = func_match.group(1)
                if actual_name != entrypoint:
                    code = re.sub(
                        f"def\\s+{actual_name}\\s*\\(",
                        f"def {entrypoint}(",
                        code
                    )
                    code = re.sub(
                        f"\\b{actual_name}\\(",
                        f"{entrypoint}(",
                        code
                    )
            else:
                if not code.startswith("def ") and ("\n    " in code or "return " in code):
                    indented_code = "\n    ".join(code.split("\n"))
                    code = f"def {entrypoint}():\n    {indented_code}"

        return code

    def check_solution(self, solution: str, test: str, entry_point: str) -> Tuple[str, str]:
        solution = self.sanitize_code(solution, entry_point)

        try:
            global_dict = {
                "math": __import__("math"),
                "hashlib": __import__("hashlib"),
                "re": __import__("re"),
                "time": __import__("time"),
                "random": __import__("random"),
                "string": __import__("string"),
                "itertools": __import__("itertools"),
                "collections": __import__("collections"),
                "functools": __import__("functools"),
            }

            global_dict.update({
                "List": List,
                "Dict": Dict,
                "Tuple": Tuple,
                "Optional": Optional,
                "Any": Any,
            })

            try:
                exec(solution, global_dict)
            except Exception as e:
                # Try removing non-ASCII characters and retry
                solution = re.sub(r'[^\x00-\x7F]+', '', solution)
                exec(solution, global_dict)

            if entry_point not in global_dict:
                return self.FAIL, f"Function '{entry_point}' is not defined in the solution."

            exec(test, global_dict)

            check = global_dict.get("check")
            if not check:
                return self.FAIL, "Test code does not include a 'check' function."

            from config import get_config
            timeout = get_config("humaneval_execution_timeout", 30)
            result = self.run_with_timeout(check, (global_dict[entry_point],), timeout)

            if result is None:
                return self.PASS, "The solution passed all test cases."
            else:
                return self.PASS, f"Test result: {result}"

        except self.TimeoutError:
            return self.FAIL, "Execution timed out. Please check if the solution contains infinite loops."
        except Exception as e:
            return self.FAIL, f"Error: {str(e)}."

    def calculate_score(self, expected_output: Any, prediction: Any) -> Tuple[float, Any]:
        return 0.0, prediction

    async def evaluate_problem(self, problem: Dict[str, Any], results: List[Dict[str, Any]]) -> Tuple[str, str, str, float, float]:
        prompt = problem.get("prompt", "")
        problem_id = problem.get("id", "")
        func_name = None

        func_match = re.search(r"def\s+(\w+)\s*\(", prompt)
        if func_match:
            func_name = func_match.group(1)

        if not func_name:
            func_name = problem.get("entry_point", "")

        if not func_name and "canonical_solution" in problem:
            func_match = re.search(r"def\s+(\w+)\s*\(", problem["canonical_solution"])
            if func_match:
                func_name = func_match.group(1)

        if not func_name:
            func_name = "solution"

        test_cases = None
        if "test" in problem:
            test_cases = problem["test"]
        elif "test_list" in problem:
            test_cases = problem.get("test_list", [])
            if len(test_cases) > 0:
                test_cases = "\n".join(test_cases)

        expected_solution = problem.get("canonical_solution", "")
        entry_point = func_name

        expected_display = (
            f"Function: {entry_point}\n\n"
            f"Expected solution:\n{expected_solution}\n\n"
            f"Test cases:\n{test_cases}"
        )

        matched_result = self.find_matching_result(problem, results)
        if matched_result:
            # Try different field names for the prediction
            prediction = (matched_result.get("consensus", "") or
                         matched_result.get("solution", "") or
                         matched_result.get("response", ""))
        else:
            prediction = None

        if prediction:
            extracted_code = self.extract_code(prediction)

            status, message = self.check_solution(extracted_code, test_cases, entry_point)

            score = 1.0 if status == self.PASS else 0.0

            if score < 1.0:
                self.log_mismatch(prompt, expected_display, prediction, f"{status}: {message}")

            cost = self.get_execution_cost(matched_result) if matched_result else 0.0
            return prompt, prediction, f"{message}\n\n{expected_display}", score, cost
        else:
            print(f"No matching result found for HumanEval problem: {problem_id}: {prompt[:100]}...")

        return prompt, "", expected_display, 0.0, 0.0

    def get_result_columns(self) -> List[str]:
        return ["prompt", "prediction", "expected_output", "score", "cost"]