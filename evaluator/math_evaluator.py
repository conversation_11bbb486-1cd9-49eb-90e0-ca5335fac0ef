import re
from math import isclose
from typing import Any, Dict, List, Tuple, Optional

# Optional sympy import - fallback to basic math if not available
try:
    import sympy
    from sympy import N, simplify
    from sympy.parsing.sympy_parser import parse_expr
    from sympy.parsing.latex import parse_latex
    SYMPY_AVAILABLE = True
except ImportError:
    SYMPY_AVAILABLE = False

from .base_evaluator import BaseEvaluator

class MathEvaluator(BaseEvaluator):
    def __init__(self, log_path: str = "results"):
        super().__init__("MATH", log_path)

    def normalize_text(self, text: str) -> str:
        if not text:
            return ""
        text = re.sub(r'\s+', ' ', text)
        text = text.strip()
        text = text.lower()
        return text

    def extract_model_answer(self, text: str) -> str:
        if not text:
            return ""

        # Handle multiple boxed answer formats
        boxed_patterns = [
            r"\$\\boxed\{([^}]+)\}\$",  # $\boxed{answer}$
            r"\\boxed\{([^}]+)\}",      # \boxed{answer}
            r"\$\\boxed\{([^}]+)\}",     # $\boxed{answer}
            r"boxed\{([^}]+)\}",        # boxed{answer}
        ]
        
        for pattern in boxed_patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(1).strip()
        
        # Look for "Final Answer:" patterns (highest priority)
        final_answer_patterns = [
            r"Final Answer:[\s]*(.+?)(?:\.|$|\n)",
            r"Final answer:[\s]*(.+?)(?:\.|$|\n)",
            r"Answer:[\s]*(.+?)(?:\.|$|\n)",
        ]
        
        for pattern in final_answer_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                answer = match.group(1).strip()
                # Clean common formatting
                answer = re.sub(r'^[\*\$]+|[\*\$]+$', '', answer)
                answer = answer.strip()
                return answer
        
        # Look for "So the answer is ..." patterns 
        answer_patterns = [
            r"So the answer is[:\s]*(.+?)(?:\.|$|\n)",
            r"Therefore,? the answer is[:\s]*(.+?)(?:\.|$|\n)",
            r"Thus,? the answer is[:\s]*(.+?)(?:\.|$|\n)",
        ]
        
        for pattern in answer_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                answer = match.group(1).strip()
                # Clean the answer
                answer = re.sub(r'^[^\w\-\+\.\$\\(\/]+', '', answer)  # Remove leading non-math characters
                answer = re.sub(r'[^\w\-\+\.\$\\)\/]+$', '', answer)  # Remove trailing non-math characters
                return answer

        from utils.answer_extraction import extract_math_answer

        if isinstance(text, dict) and "extracted_answer" in text:
            return text["extracted_answer"]

        result = extract_math_answer(text)
        if result:
            return result

        sentence_end_pattern = r"(?<!\d)[.!?]\s+"
        sentences = re.split(sentence_end_pattern, text)
        sentences = [s.strip() for s in sentences if s.strip()]

        if sentences:
            last_sentence = sentences[-1]
            if "answer" in last_sentence.lower() or "equal" in last_sentence.lower():
                parts = re.split(r"(?:answer|equal)[s]?(?:\s+is|\s*=\s*)", last_sentence, flags=re.IGNORECASE)
                if len(parts) > 1:
                    return parts[-1].strip()

        return sentences[-1] if sentences else ""

    def is_digit(self, num: str) -> bool:
        num_str = str(num).replace(",", "")
        try:
            float(num_str)
            return True
        except ValueError:
            if num_str.endswith("%"):
                try:
                    float(num_str[:-1])
                    return True
                except ValueError:
                    pass
            return False

    def parse_digits(self, num: str) -> Optional[float]:
        num_str = str(num).replace(",", "")
        try:
            return float(num_str)
        except ValueError:
            if num_str.endswith("%"):
                num_str = num_str[:-1]
                if num_str.endswith("\\"):
                    num_str = num_str[:-1]
                try:
                    return float(num_str) / 100
                except ValueError:
                    pass
        return None

    def symbolic_equal(self, a: str, b: str) -> bool:
        if not SYMPY_AVAILABLE:
            # Fallback to basic comparison
            try:
                return str(a).strip() == str(b).strip()
            except:
                return False

        def _parse(s):
            for f in [parse_latex, parse_expr]:
                try:
                    return f(s)
                except:
                    pass
            return s

        try:
            a_parsed = _parse(a)
            b_parsed = _parse(b)

            try:
                if simplify(a_parsed - b_parsed) == 0:
                    return True
            except:
                pass

            try:
                if isclose(float(N(a_parsed)), float(N(b_parsed)), abs_tol=1e-3):
                    return True
            except:
                pass
        except:
            pass

        return False

    def math_equal(self, prediction: str, reference: str) -> bool:
        # Normalize symbols and common formatting
        prediction = str(prediction).replace('π', 'pi').replace('\\pi', 'pi')
        reference = str(reference).replace('π', 'pi').replace('\\pi', 'pi')
        
        # Remove common formatting differences
        prediction = re.sub(r'^[\$\*]+|[\$\*]+$', '', prediction).strip()
        reference = re.sub(r'^[\$\*]+|[\$\*]+$', '', reference).strip()
        
        # Direct string comparison
        if prediction == reference:
            return True
        
        # Case insensitive comparison
        if prediction.lower() == reference.lower():
            return True

        try:
            if self.is_digit(prediction) and self.is_digit(reference):
                pred_num = self.parse_digits(prediction)
                ref_num = self.parse_digits(reference)
                if pred_num is not None and ref_num is not None:
                    return isclose(pred_num, ref_num, abs_tol=1e-3)
        except:
            pass

        try:
            return self.symbolic_equal(prediction, reference)
        except:
            pass

        return False

    def calculate_score(self, expected_output: str, prediction: str) -> Tuple[float, str]:
        expected_answer = self.extract_model_answer(expected_output)
        predicted_answer = self.extract_model_answer(prediction)

        if self.math_equal(predicted_answer, expected_answer):
            return 1.0, predicted_answer
        else:
            return 0.0, predicted_answer

    async def evaluate_problem(self, problem: Dict[str, Any], results: List[Dict[str, Any]]) -> Tuple[str, str, str, float, float]:
        problem_text = problem.get("problem", "")
        solution = problem.get("solution", "")

        matched_result = self.find_matching_result(problem, results, "problem", "prompt")

        if matched_result:
            # Try different field names for the prediction
            prediction = (matched_result.get("consensus", "") or
                         matched_result.get("solution", "") or
                         matched_result.get("response", ""))
            score, extracted_prediction = self.calculate_score(solution, prediction)

            if score < 1.0:
                self.log_mismatch(problem_text, solution, prediction, extracted_prediction)

            cost = self.get_execution_cost(matched_result)
            return problem_text, prediction, solution, score, cost

        self.log_warning(f"No matching result found for MATH problem: {problem_text[:100]}...")
        return problem_text, "", solution, 0.0, 0.0

    def get_result_columns(self) -> List[str]:
        return ["problem", "prediction", "solution", "score", "cost"]