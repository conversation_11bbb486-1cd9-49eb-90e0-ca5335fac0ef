#!/usr/bin/env python3
"""
MMLU (Massive Multitask Language Understanding) Evaluator

MMLU is a comprehensive benchmark covering 57 subjects across
STEM, humanities, social sciences, and other areas.
"""

import re
from typing import Dict, Any, List, Optional, Tuple
from .base_evaluator import BaseEvaluator


class MMLUEvaluator(BaseEvaluator):
    """Evaluator for MMLU dataset"""

    def __init__(self, log_path: str = "results"):
        super().__init__("MMLU", log_path)
        
    def extract_answer(self, response: str, choices: List[str] = None) -> str:
        """
        Extract answer from model response for MMLU
        MMLU uses multiple choice format (A, B, C, D)
        """
        if not response or not response.strip():
            return ""
            
        response = response.strip()
        
        # Pattern 1: "So the answer is X" format
        answer_patterns = [
            r'(?:so\s+)?(?:the\s+)?answer\s+is\s+([A-D])',
            r'(?:therefore|thus|hence),?\s+(?:the\s+)?answer\s+is\s+([A-D])',
            r'(?:the\s+)?(?:correct\s+)?answer\s+is\s+([A-D])',
            r'(?:the\s+)?(?:final\s+)?answer:\s*([A-D])',
            r'\\boxed\{([A-D])\}',  # LaTeX boxed format
        ]
        
        for pattern in answer_patterns:
            matches = re.findall(pattern, response, re.IGNORECASE)
            if matches:
                return matches[-1].upper()  # Take the last match
        
        # Pattern 2: Look for boxed numbers and convert to letters
        # Many math problems give numerical answers that need to be mapped to choices
        boxed_number_pattern = r'\\boxed\{([^}]+)\}'
        boxed_matches = re.findall(boxed_number_pattern, response)
        if boxed_matches and choices:
            answer_value = boxed_matches[-1].strip()
            # Try to find which choice contains this answer
            for i, choice in enumerate(choices):
                if answer_value in choice:
                    return chr(65 + i)  # Convert 0,1,2,3 to A,B,C,D

        # Pattern 2b: Look for standalone numbers that might match choices
        if choices:
            # Extract all numbers from the response
            number_pattern = r'\b(\d+(?:\.\d+)?)\b'
            numbers = re.findall(number_pattern, response)
            if numbers:
                answer_value = numbers[-1]  # Take the last number
                # Try to find which choice contains this number
                for i, choice in enumerate(choices):
                    if answer_value in choice:
                        return chr(65 + i)  # Convert 0,1,2,3 to A,B,C,D

        # Pattern 3: Look for standalone letter choices
        # Find all occurrences of A, B, C, D that might be answers
        letter_pattern = r'\b([A-D])\b'
        letters = re.findall(letter_pattern, response)

        if letters:
            # Take the last occurrence as it's likely the final answer
            return letters[-1].upper()
        
        # Pattern 3: Look for "option X" or "choice X"
        option_patterns = [
            r'(?:option|choice)\s+([A-D])',
            r'([A-D])\s+is\s+(?:the\s+)?(?:correct|right)',
            r'select\s+([A-D])',
            r'choose\s+([A-D])'
        ]
        
        for pattern in option_patterns:
            matches = re.findall(pattern, response, re.IGNORECASE)
            if matches:
                return matches[-1].upper()
        
        return ""
    
    def normalize_answer(self, answer: str) -> str:
        """Normalize answer for comparison"""
        if not answer:
            return ""
        
        # For MMLU, answers should be single letters A, B, C, or D
        answer = answer.strip().upper()
        
        # Ensure it's a valid choice
        if answer in ['A', 'B', 'C', 'D']:
            return answer
        
        return ""
    
    def is_correct(self, predicted: str, ground_truth: str) -> bool:
        """Check if predicted answer matches ground truth"""
        pred_normalized = self.normalize_answer(predicted)
        gt_normalized = self.normalize_answer(ground_truth)
        
        if not pred_normalized or not gt_normalized:
            return False
        
        return pred_normalized == gt_normalized
    
    def evaluate_single(self, predicted: str, ground_truth: str, 
                       problem_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Evaluate a single MMLU prediction"""
        
        choices = problem_data.get('choices', []) if problem_data else []
        extracted_answer = self.extract_answer(predicted, choices)
        is_correct = self.is_correct(extracted_answer, ground_truth)
        
        result = {
            'predicted_raw': predicted,
            'predicted_answer': extracted_answer,
            'ground_truth': ground_truth,
            'correct': is_correct,
            'extraction_successful': bool(extracted_answer),
        }
        
        # Add problem metadata if available
        if problem_data:
            result.update({
                'problem_id': problem_data.get('id', ''),
                'subject': problem_data.get('subject', ''),
                'question': problem_data.get('question', '')[:100] + '...' if problem_data.get('question') else ''
            })
        
        return result
    
    def get_evaluation_metrics(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate evaluation metrics for MMLU"""
        
        if not results:
            return {
                'accuracy': 0.0,
                'total_problems': 0,
                'correct_answers': 0,
                'extraction_rate': 0.0,
                'subject_breakdown': {}
            }
        
        total = len(results)
        correct = sum(1 for r in results if r.get('correct', False))
        extracted = sum(1 for r in results if r.get('extraction_successful', False))
        
        # Subject-wise breakdown
        subject_stats = {}
        for result in results:
            subject = result.get('subject', 'unknown')
            if subject not in subject_stats:
                subject_stats[subject] = {'total': 0, 'correct': 0}
            subject_stats[subject]['total'] += 1
            if result.get('correct', False):
                subject_stats[subject]['correct'] += 1
        
        # Calculate subject accuracies
        subject_breakdown = {}
        for subject, stats in subject_stats.items():
            subject_breakdown[subject] = {
                'accuracy': stats['correct'] / stats['total'] if stats['total'] > 0 else 0.0,
                'correct': stats['correct'],
                'total': stats['total']
            }
        
        return {
            'accuracy': correct / total,
            'total_problems': total,
            'correct_answers': correct,
            'extraction_rate': extracted / total,
            'subject_breakdown': subject_breakdown
        }
    
    def format_results(self, metrics: Dict[str, Any]) -> str:
        """Format evaluation results for display"""
        
        lines = [
            f"📊 MMLU Evaluation Results",
            f"=" * 50,
            f"Overall Accuracy: {metrics['accuracy']:.1%}",
            f"Correct Answers: {metrics['correct_answers']}/{metrics['total_problems']}",
            f"Answer Extraction Rate: {metrics['extraction_rate']:.1%}",
            ""
        ]
        
        # Subject breakdown
        if metrics.get('subject_breakdown'):
            lines.append("📚 Subject Breakdown:")
            for subject, stats in metrics['subject_breakdown'].items():
                lines.append(f"  {subject}: {stats['accuracy']:.1%} ({stats['correct']}/{stats['total']})")
        
        return "\n".join(lines)

    async def evaluate_problem(self, problem: Dict[str, Any], results: Any) -> Tuple[Any, ...]:
        """Evaluate a single MMLU problem (required by BaseEvaluator)"""

        # Handle both dict and list formats for results
        if isinstance(results, dict):
            results_list = results.get("results", [])
        elif isinstance(results, list):
            results_list = results
        else:
            results_list = []

        # Find the matching result for this problem
        # MMLU uses "question" field in benchmark, "prompt" field in results
        matching_result = self.find_matching_result(problem, results_list, problem_key="question", result_key="prompt")

        if not matching_result:
            return (
                problem.get("question", "")[:50] + "...",
                problem.get("answer", ""),
                "",
                "",
                "NO_RESULT",
                0.0,
                problem.get("subject", "")
            )

        predicted_text = matching_result.get("solution", "")
        ground_truth = problem.get("answer", "")

        choices = problem.get('choices', [])
        extracted_answer = self.extract_answer(predicted_text, choices)
        is_correct = self.is_correct(extracted_answer, ground_truth)

        return (
            problem.get("question", "")[:50] + "...",
            ground_truth,
            predicted_text[:100] + "..." if len(predicted_text) > 100 else predicted_text,
            extracted_answer,
            "PASS" if is_correct else "FAIL",
            1.0 if is_correct else 0.0,
            problem.get("subject", "")
        )

    def calculate_score(self, expected_output: Any, prediction: Any) -> Tuple[float, Any]:
        """Calculate score for a prediction (required by BaseEvaluator)"""

        if isinstance(prediction, str):
            extracted = self.extract_answer(prediction)
        else:
            extracted = str(prediction)

        is_correct = self.is_correct(extracted, str(expected_output))
        score = 1.0 if is_correct else 0.0

        return score, extracted

    def get_result_columns(self) -> List[str]:
        """Get column names for results CSV (required by BaseEvaluator)"""
        return [
            "question_preview",
            "ground_truth",
            "prediction_preview",
            "extracted_answer",
            "result",
            "score",
            "subject"
        ]
