"""StrategyQA evaluator for strategic reasoning questions."""

import re
from typing import Any, Dict, List, Tuple
from .base_evaluator import BaseEvaluator


class StrategyQAEvaluator(BaseEvaluator):
    """Evaluator for StrategyQA strategic reasoning dataset."""
    
    def __init__(self, log_path: str = "results"):
        super().__init__("StrategyQA", log_path)
    
    def extract_answer(self, response: str) -> str:
        """
        Extract yes/no answer from the model response.
        
        Args:
            response: The model's response text
            
        Returns:
            Extracted answer as "yes" or "no"
        """
        if not response:
            return "unknown"
        
        # Check for common error patterns first
        if "failed" in response.lower() or "error" in response.lower() or "connection" in response.lower():
            return "unknown"
        
        # Clean and normalize the response
        response_lower = response.lower().strip()
        
        # Define patterns to extract yes/no answers with higher priority for explicit answers
        answer_patterns = [
            # Highest priority - explicit final answer patterns
            r"(?:final\s+)?answer\s*:?\s*(yes|no)(?:\s|$|\.|,)",
            r"(?:therefore|thus|so),?\s*(?:the\s+)?answer\s+is\s*:?\s*(yes|no)(?:\s|$|\.|,)",
            r"(?:in\s+)?conclusion\s*:?\s*(yes|no)(?:\s|$|\.|,)",
            
            # Pattern for answers in quotes or bold
            r"[\"'*]+\s*(yes|no)\s*[\"'*]+(?:\s|$|\.|,)",
            
            # Simple yes/no at end of response
            r"(?:^|\s)(yes|no)\.?\s*$",
            
            # Patterns with reasoning indicators
            r"(?:i\s+think|i\s+believe)\s+(?:the\s+answer\s+is\s+)?(yes|no)",
            r"(?:it\s+is|this\s+is)\s+(?:a\s+)?(yes|no)",
            
            # Patterns for negation/affirmation
            r"(?:definitely|certainly|absolutely)\s+(yes|no)",
            r"(?:probably|likely)\s+(yes|no)",
        ]
        
        # Try each pattern
        for pattern in answer_patterns:
            match = re.search(pattern, response_lower)
            if match:
                answer = match.group(1).strip()
                return answer
        
        # Check for strong negative indicators first
        strong_negative_patterns = [
            r"(?:^|\s)(?:no|false|incorrect|impossible|cannot|would\s+not|could\s+not|should\s+not|will\s+not|does\s+not|is\s+not)(?:\s|$|\.|,)",
            r"(?:^|\s)(?:it\s+is\s+not\s+possible|this\s+is\s+false|this\s+cannot\s+happen)",
            r"(?:^|\s)(?:the\s+answer\s+is\s+negative|not\s+possible)",
        ]
        
        # Check for strong affirmative indicators
        strong_affirmative_patterns = [
            r"(?:^|\s)(?:yes|true|correct|possible|can)(?:\s|$|\.|,)",
            r"(?:^|\s)(?:it\s+is\s+possible|this\s+is\s+true|this\s+can\s+happen)",
            r"(?:^|\s)(?:the\s+answer\s+is\s+positive|affirmative)",
        ]
        
        # Count strong indicators
        strong_negative_count = sum(len(re.findall(pattern, response_lower)) for pattern in strong_negative_patterns)
        strong_affirmative_count = sum(len(re.findall(pattern, response_lower)) for pattern in strong_affirmative_patterns)
        
        if strong_negative_count > strong_affirmative_count:
            return "no"
        elif strong_affirmative_count > strong_negative_count:
            return "yes"
        
        # Last resort: look for any occurrence of yes/no (but be careful about context)
        yes_matches = re.findall(r'\byes\b', response_lower)
        no_matches = re.findall(r'\bno\b', response_lower)
        
        if len(no_matches) > len(yes_matches):
            return "no"
        elif len(yes_matches) > len(no_matches):
            return "yes"
        
        return "unknown"
    
    def normalize_answer(self, answer: str) -> str:
        """
        Normalize answer to standard format.
        
        Args:
            answer: The answer to normalize
            
        Returns:
            Normalized answer ("yes", "no", or "unknown")
        """
        if not answer:
            return "unknown"
        
        answer_lower = answer.lower().strip()
        
        # Handle various affirmative responses
        if answer_lower in ["yes", "true", "correct", "1", "positive", "affirmative"]:
            return "yes"
        
        # Handle various negative responses  
        if answer_lower in ["no", "false", "incorrect", "0", "negative"]:
            return "no"
        
        return "unknown"
    
    async def evaluate_problem(self, problem: Dict[str, Any], results: List[Dict[str, Any]]) -> Tuple[str, str, str, float, float]:
        """
        Evaluate a single StrategyQA problem.

        Args:
            problem: Dictionary containing problem data
            results: List of result dictionaries

        Returns:
            Tuple of (question, prediction, expected_answer, score, execution_time)
        """
        question = problem.get("question", problem.get("prompt", ""))
        expected = str(problem.get("expected_output", problem.get("answer", ""))).lower().strip()

        # Find matching result - try multiple approaches
        matched_result = self.find_matching_result(problem, results, "question", "prompt")

        # If no match found by prompt, try by extracting the core question
        if not matched_result:
            # Extract core question from problem
            problem_question = problem.get("metadata", {}).get("question", "")
            if problem_question:
                for result in results:
                    result_prompt = result.get("prompt", "")
                    if problem_question in result_prompt:
                        matched_result = result
                        break

        if matched_result:
            solution = matched_result.get("solution", "")

            # Extract the answer from the solution
            extracted_answer = self.extract_answer(solution)

            # Normalize both answers
            predicted_answer = self.normalize_answer(extracted_answer)
            expected_answer = self.normalize_answer(expected)

            # Calculate score
            if predicted_answer == "unknown":
                score = 0.0
            elif predicted_answer == expected_answer:
                score = 1.0
            else:
                score = 0.0

            # Log mismatch if score is not perfect
            if score < 1.0:
                self.log_mismatch(question, expected, solution, extracted_answer)

            execution_time = self.get_execution_cost(matched_result)
            return question, predicted_answer, expected, score, execution_time

        return question, "", expected, 0.0, 0.0
    
    def calculate_score(self, result: Dict[str, Any]) -> float:
        """
        Calculate score for a single result.
        
        Args:
            result: Dictionary containing evaluation result
            
        Returns:
            Score as float (0.0 or 1.0)
        """
        return float(result.get('score', 0.0))
    
    def get_result_columns(self) -> List[str]:
        """
        Get column names for CSV output.
        
        Returns:
            List of column names
        """
        return [
            'problem',
            'prediction',
            'expected_output',
            'score',
            'execution_time'
        ]
    
    def get_metrics_summary(self, results: list) -> Dict[str, Any]:
        """
        Calculate summary metrics for StrategyQA evaluation.
        
        Args:
            results: List of evaluation results
            
        Returns:
            Dictionary with summary metrics
        """
        if not results:
            return {
                'accuracy': 0.0,
                'total_problems': 0,
                'correct_answers': 0,
                'extraction_rate': 0.0,
                'answer_distribution': {}
            }
        
        total_problems = len(results)
        correct_answers = sum(1 for r in results if r.get('score', 0) == 1.0)
        successful_extractions = sum(1 for r in results if r.get('predicted_answer', 'unknown') != 'unknown')
        
        # Calculate answer distribution
        predicted_answers = [r.get('predicted_answer', 'unknown') for r in results]
        answer_distribution = {}
        for answer in set(predicted_answers):
            answer_distribution[answer] = predicted_answers.count(answer)
        
        return {
            'accuracy': correct_answers / total_problems if total_problems > 0 else 0.0,
            'total_problems': total_problems,
            'correct_answers': correct_answers,
            'extraction_rate': successful_extractions / total_problems if total_problems > 0 else 0.0,
            'answer_distribution': answer_distribution
        }