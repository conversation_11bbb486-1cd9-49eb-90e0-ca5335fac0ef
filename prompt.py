#!/usr/bin/env python3
"""
Redesigned Collaborative Multi-Agent Prompt System
Based on the excellent design from prompt_old.py, focusing on concise and efficient draft collaboration
"""

# ========================= Base System Prompts =========================

SYSTEM_PROMPT = "You are an expert AI assistant. Provide thoughtful, accurate responses in English."

# Dataset-specific system enhancements
DATASET_SYSTEM_ENHANCEMENTS = {
    "gsm8k": "Focus on clear arithmetic and step-by-step problem solving. Avoid overcomplicating simple math problems.",
    "math": "Apply rigorous mathematical reasoning. NEVER remove calculation steps or reasoning chains when merging drafts. Show complete mathematical work with all intermediate steps.",
    "mbpp": "Write clean, efficient Python code. Consider edge cases and follow Python conventions.",
    "humaneval": "Implement robust solutions that handle all specified requirements and edge cases.",

    "hotpotqa": "Perform systematic multi-hop reasoning: 1) Identify question type (bridge/comparison), 2) Extract key entities from ALL context passages, 3) Map entity relationships with evidence, 4) Build complete reasoning chain, 5) Verify answer against all sources.",
    "strategyqa": "Use implicit reasoning and world knowledge to analyze strategic questions. Consider multiple perspectives, real-world constraints, and logical implications. Always conclude with definitive yes/no.",
    "gpqa": "Apply graduate-level scientific knowledge with rigorous analysis. Systematically evaluate ALL options using domain expertise. Use elimination method and scientific principles to identify the single best answer.",
    "mmlu": "Draw upon comprehensive knowledge across disciplines. Eliminate incorrect options systematically."
}

# ========================= Worker Collaborative Draft Generation Prompts =========================

# Task-specific prompts for Worker to generate concise drafts
WORKER_DRAFT_PROMPTS = {
    "gsm8k": """Create a concise draft to solve this grade school math problem. 
Work step by step, keep each step brief and clear. Focus on correct arithmetic.

Format:
- Step 1: [brief reasoning]
- Step 2: [brief reasoning]
- Final: [answer]
#### [number]""",

    "math": """Create a concise draft to solve this advanced math problem.
Apply mathematical reasoning systematically, keep steps concise but rigorous.

Format:
- Method: [approach]
- Step 1: [key calculation]
- Step 2: [key calculation]
- Final: [answer]
#### [answer]""",

    "mbpp": """Create a concise draft for this Python coding problem.
Think through the requirements clearly, keep reasoning brief.

Format:
- Requirements: [what needs to be done]
- Logic: [key algorithm insight]
- Implementation: [approach]
#### [Python code starting with def]""",

    "humaneval": """Create a PERFECT, COMPLETE Python solution on the first try.
Focus on writing flawless, production-ready code that handles all requirements.

CRITICAL REQUIREMENTS:
- Write complete, syntactically correct Python code
- Handle ALL edge cases mentioned in the problem
- Ensure the function works for ALL test scenarios
- Use proper Python conventions and naming
- Include proper error handling if needed

OUTPUT FORMAT: Provide ONLY the complete Python function code.
Do NOT include any explanation, reasoning, or formatting - just pure code.

#### [Python code starting with def]""",



    "hotpotqa": """Create a structured draft for this multi-hop reasoning question.
Follow the systematic reasoning chain to connect information across sources.

Format:
- Question Type: [bridge/comparison/other]
- Key Entity 1: [first entity with key facts]
- Key Entity 2: [second entity with key facts]  
- Reasoning Chain: [step-by-step logical connections]
- Evidence Verification: [check facts from both sources]
- Final Answer: [specific factual result]
#### [answer]""",

    "strategyqa": """Create a structured draft for this strategic reasoning question requiring implicit knowledge.
Apply step-by-step logical analysis to reach a definitive yes/no answer.

Format:
- Question Analysis: [what is really being asked]
- Key Facts: [relevant background knowledge]
- Implicit Assumptions: [unstated but important considerations]
- Logical Chain: [step-by-step reasoning process]
- Reality Check: [practical constraints and real-world verification]
- Final Conclusion: [definitive yes or no with core justification]
#### [yes/no]""",

    "gpqa": """Create a systematic draft for this graduate-level scientific question.
Apply rigorous scientific reasoning and domain expertise.

Format:
- Scientific Domain: [field of study and key concepts]
- Fundamental Principles: [relevant theories, laws, formulas]
- Option Analysis: [systematically evaluate each choice A, B, C, D]
  - A: [scientific assessment]
  - B: [scientific assessment] 
  - C: [scientific assessment]
  - D: [scientific assessment]
- Elimination Process: [rule out incorrect options with scientific reasoning]
- Best Answer Justification: [why chosen option is most scientifically sound]
#### [A/B/C/D]""",

    "mmlu": """Create a concise draft for this multiple-choice question.
Use comprehensive knowledge to evaluate options.

Format:
- Topic: [subject area]
- Key knowledge: [relevant facts]
- Eliminate: [wrong options]
- Answer: [correct choice]
#### [A/B/C/D]"""
}

# Worker collaborative discussion prompts
WORKER_COLLABORATION_PROMPTS = {
    "discuss": """You are participating in a multi-agent collaborative discussion to solve this problem.

PROBLEM: {question}
TASK TYPE: {task_type}

OTHER AGENTS' DRAFTS:
{other_drafts}

Your task: Create your own draft or improve existing ones. Focus on:
1. Accuracy and correctness
2. Clear reasoning steps
3. Proper format for the task type

{task_prompt}

Generate your collaborative draft:""",

    "improve": """You are improving a collaborative draft based on discussion.

PROBLEM: {question}
TASK TYPE: {task_type}

CURRENT DRAFT:
{current_draft}

FEEDBACK/SUGGESTIONS:
{feedback}

Your task: Improve the draft based on feedback. Keep it concise but accurate.

{task_prompt}

Generate your improved draft:""",

    "validate": """You are validating a collaborative draft for accuracy.

PROBLEM: {question}
TASK TYPE: {task_type}

DRAFT TO VALIDATE:
{draft}

Your task: Check for errors and suggest corrections. Focus on:
1. Logical correctness
2. Calculation accuracy
3. Proper format

Provide validation feedback:
- Issues found: [list any problems]
- Corrections needed: [specific fixes]
- Overall quality: [good/needs improvement]"""
}

# ========================= Merger Agent Prompts =========================

MERGER_PROMPTS = {
    "merge_annotations": """You are a merge agent selecting and refining the best draft into a coherent final solution.

ORIGINAL PROBLEM: {question}
TASK TYPE: {task_type}

WORKER DRAFTS WITH ANNOTATIONS:
{annotated_drafts}

Your task: Select the MOST COMPLETE and CORRECT reasoning chain, then refine it into the final draft.

CRITICAL FOR PROGRAMMING TASKS (MBPP/HumanEval):
- Maintain COMPLETE function implementations - NO truncation
- Preserve ALL working code logic and syntax
- Ensure function definitions have proper closing brackets/statements
- Focus on functionality over style when merging suggestions
- Keep all import statements and type hints if present
- Verify the complete code can be executed without syntax errors

CRITICAL FOR MATH COMPETITION PROBLEMS:
- Preserve ALL mathematical reasoning chains and logical steps
- Maintain complete proofs and derivations - DO NOT truncate arguments  
- Keep ALL intermediate calculations and algebraic manipulations
- Preserve multiple solution approaches if they're all valid
- Ensure mathematical notation and formulas remain intact
- Focus on mathematical rigor over presentation style
- IMPORTANT: The complete reasoning chain is essential for evaluation

TASK-SPECIFIC STRATEGY: {merge_strategy}

GENERAL APPROACH:
1. IDENTIFY which draft has the most complete and accurate reasoning
2. SELECT that single draft as your base (don't mix multiple approaches)
3. REFINE it by incorporating any valid corrections from other drafts
4. PRESERVE the complete reasoning chain of the selected draft
5. Ensure the final answer format matches task requirements

DO NOT merge different approaches - choose the best ONE and refine it.

{task_prompt}

Generate the refined final draft based on the BEST single approach:""",

    "resolve_conflicts": """You are resolving conflicts between different draft approaches.

PROBLEM: {question}
TASK TYPE: {task_type}

CONFLICTING DRAFTS:
{conflicting_drafts}

Your task: Determine which approach is most correct and create a unified draft.

Focus on:
1. Logical soundness
2. Calculation accuracy
3. Proper methodology

{task_prompt}

Generate the resolved draft:""",

    "synthesize": """You are synthesizing multiple draft insights into a final answer.

PROBLEM: {question}
TASK TYPE: {task_type}

DRAFT INSIGHTS:
{insights}

Your task: Combine all valid insights into ONE clear, concise final draft.

{task_prompt}

Generate the synthesized draft:"""
}

# ========================= Leader Evaluation Prompts =========================

LEADER_EVALUATION_PROMPTS = {
    "evaluate_quality": """You are a leader agent evaluating the quality of a collaborative draft.

PROBLEM: {question}
TASK TYPE: {task_type}

DRAFT TO EVALUATE:
{draft}

Evaluate this draft on:
1. Accuracy (0.0-1.0)
2. Completeness (0.0-1.0)
3. Clarity (0.0-1.0)
4. Format correctness (0.0-1.0)

Provide assessment with structured feedback:
```json
{{
  "overall_score": 0.0-1.0,
  "accuracy_score": 0.0-1.0,
  "completeness_score": 0.0-1.0,
  "clarity_score": 0.0-1.0,
  "format_score": 0.0-1.0,
  "decision": "approved|needs_minor_revision|needs_major_revision|rejected",
  "feedback": "general feedback for improvement",
  "feedback_annotations": [
    {{
      "span_id": 1,
      "issue_type": "logical_gap|calculation_error|format_issue|clarity_issue",
      "comment": "specific issue description",
      "severity": "high|medium|low"
    }}
  ]
}}
```

For feedback_annotations:
- Use span_id to identify specific parts (1=beginning, 2=middle, 3=end)
- issue_type should be specific and actionable
- comment should be concise but clear
- severity helps prioritize fixes

EVALUATION CRITERIA:
- Score below 0.7: needs_major_revision or rejected
- Score 0.7-0.85: needs_minor_revision
- Score above 0.85: approved

Be strict in evaluation to ensure high quality.""",

    "provide_feedback": """You are a leader agent providing feedback for draft improvement.

PROBLEM: {question}
TASK TYPE: {task_type}

CURRENT DRAFT:
{draft}

ISSUES IDENTIFIED:
{issues}

Provide specific improvement guidance:
1. What needs to be fixed?
2. How to fix it?
3. What approach should be taken?

Your feedback should help workers create a better draft in the next iteration.

Improvement guidance:""",

    "final_decision": """You are a leader agent making the final decision on a collaborative draft.

PROBLEM: {question}
TASK TYPE: {task_type}

FINAL DRAFT:
{final_draft}

COLLABORATION HISTORY:
{history}

Make your final decision:
1. Is this draft ready for submission?
2. Does it correctly answer the question?
3. Is the format appropriate?

Decision: [APPROVE/REJECT/REVISE]
Reason: [brief explanation]

If APPROVE, output the final answer in the required format.
If REJECT/REVISE, provide specific improvement instructions."""
}

# ========================= Task-specific Answer Formats =========================

EXPECTED_ANSWER_FORMATS = {
    "gsm8k": "#### [number]",
    "math": "#### [answer]", 
    "mbpp": "[raw Python code starting with def]",
    "humaneval": "[raw Python code starting with def]",

    "hotpotqa": "#### [answer]",
    "strategyqa": "#### [yes/no]",
    "gpqa": "#### [A/B/C/D]",
    "mmlu": "#### [A/B/C/D]"
}

# ========================= Few-shot Examples =========================

DRAFT_EXAMPLES = {
    "gsm8k": {
        "question": "Jason had 20 lollipops. He gave Denny some lollipops. Now Jason has 12 lollipops. How many lollipops did Jason give to Denny?",
        "draft": """- Start: Jason had 20 lollipops
- End: Jason has 12 lollipops  
- Given away: 20 - 12 = 8
#### 8"""
    },
    
    "math": {
        "question": "Find the derivative of f(x) = x^3 + 2x^2 - 5x + 3",
        "draft": """- Apply power rule: d/dx(x^n) = nx^(n-1)
- Term 1: 3x^2
- Term 2: 4x
- Term 3: -5
- Constant: 0
#### 3x^2 + 4x - 5"""
    },
    
    "strategyqa": {
        "question": "Could a goldfish survive in space?",
        "draft": """- Fish need: water, oxygen, pressure
- Space has: vacuum, no oxygen, no pressure
- Goldfish cannot survive these conditions
#### no"""
    }
}

# ========================= Helper Functions =========================

def get_worker_draft_prompt(task_type: str) -> str:
    """Get task-specific prompt for Worker to generate draft"""
    return WORKER_DRAFT_PROMPTS.get(task_type, WORKER_DRAFT_PROMPTS["gsm8k"])

def get_worker_collaboration_prompt(prompt_type: str, **kwargs) -> str:
    """Get Worker collaboration prompt"""
    template = WORKER_COLLABORATION_PROMPTS.get(prompt_type, WORKER_COLLABORATION_PROMPTS["discuss"])
    return template.format(**kwargs)

def get_task_specific_merge_strategy(task_type: str) -> str:
    """Get task-specific merge strategy"""
    strategies = {
        'gsm8k': "Simple math problems: Select draft with clearest step-by-step calculation. Focus on correctness over complexity.",
        'math': "Complex math: Select draft with CORRECT calculations and complete step-by-step reasoning. Verify numerical results and eliminate calculation errors. Prioritize accuracy over complexity.",
        'mbpp': "Programming: Select draft with correct, complete, and simple code. Prioritize working solution over complexity. Avoid over-engineering.",
        'humaneval': "Code generation: Select draft with COMPLETE and EXECUTABLE code. Reject truncated/incomplete functions. Prioritize syntactically correct, runnable code over complexity.",

        'hotpotqa': "Multi-hop reasoning: Select draft with COMPLETE reasoning chain connecting ALL relevant facts. Ensure final answer includes ALL required elements (e.g., all professions). Preserve full logical flow.",
        'strategyqa': "Implicit reasoning: Select draft with SYSTEMATIC logical chain. Break down complex reasoning into clear steps. Explicitly state Yes/No with complete justification.",
        'gpqa': "Scientific reasoning: Select draft with RIGOROUS scientific analysis and domain expertise. Prioritize factual accuracy over complexity. Verify scientific principles and calculations.",
        'mmlu': "Knowledge questions: Select draft with COMPREHENSIVE factual accuracy. Cross-reference multiple knowledge domains. Ensure complete coverage of all aspects of the question."
    }
    return strategies.get(task_type, "Select the most complete and accurate reasoning chain.")

def get_merger_prompt(prompt_type: str, **kwargs) -> str:
    """Get Merger prompt for merging"""
    template = MERGER_PROMPTS.get(prompt_type, MERGER_PROMPTS["merge_annotations"])
    
    # Add task-specific merge strategy if task_type is provided
    if 'task_type' in kwargs:
        kwargs['merge_strategy'] = get_task_specific_merge_strategy(kwargs['task_type'])
    else:
        kwargs['merge_strategy'] = "Select the most complete and accurate reasoning chain."
    
    return template.format(**kwargs)

def get_leader_evaluation_prompt(prompt_type: str, **kwargs) -> str:
    """Get Leader evaluation prompt"""
    template = LEADER_EVALUATION_PROMPTS.get(prompt_type, LEADER_EVALUATION_PROMPTS["evaluate_quality"])
    return template.format(**kwargs)

def get_system_prompt(task_type: str = "standard") -> str:
    """Get system prompt"""
    base_prompt = SYSTEM_PROMPT
    if task_type in DATASET_SYSTEM_ENHANCEMENTS:
        base_prompt += f"\n\nFor this task: {DATASET_SYSTEM_ENHANCEMENTS[task_type]}"
    return base_prompt

def get_expected_format(task_type: str) -> str:
    """Get expected answer format"""
    return EXPECTED_ANSWER_FORMATS.get(task_type, "#### [answer]")

def get_draft_example(task_type: str) -> dict:
    """Get draft example"""
    return DRAFT_EXAMPLES.get(task_type, DRAFT_EXAMPLES["gsm8k"])

def create_complete_worker_prompt(task_type: str, question: str, context: dict = None) -> str:
    """Create complete Worker prompt including examples"""
    base_prompt = get_worker_draft_prompt(task_type)
    example = get_draft_example(task_type)
    
    prompt = f"""{base_prompt}

Example:
Question: {example['question']}
Draft: {example['draft']}

Now create your draft:
Question: {question}
Draft:"""
    
    return prompt

# ========================= Compatibility Functions =========================

class WorkerAnnotationPrompts:
    """Compatibility class to maintain existing interface"""
    
    @staticmethod
    def get_annotation_generation_prompt(context, strategy, max_annotations: int) -> str:
        """Simplified annotation generation prompt"""
        task_type = getattr(context, 'question_type', 'standard')
        question = getattr(context, 'question_content', '')
        draft = getattr(context, 'draft_content', '')
        
        return f"""You are collaborating on this problem. Analyze the current draft and provide brief, actionable suggestions.

PROBLEM: {question}
TASK TYPE: {task_type}
CURRENT DRAFT: {draft}

Provide up to {max_annotations} concise suggestions:
1. What should be improved?
2. How to fix any errors?
3. What's missing?

Focus on correctness and clarity. Keep suggestions brief and specific."""

class LeaderEvaluationPrompts:
    """Compatibility class to maintain existing interface"""
    
    @staticmethod
    def get_leader_evaluation_system_prompt() -> str:
        """Leader evaluation system prompt"""
        return "You are a leader agent responsible for evaluating collaborative drafts and providing quality assessments."

class LeaderAgentPrompts:
    """Leader Agent Prompts for final answer generation and quality assessment"""

    @staticmethod
    def get_leader_synthesis_prompt() -> str:
        """Leader synthesis prompt"""
        return "You are a leader agent synthesizing collaborative results into a final answer."
    
    @staticmethod
    def get_synthesis_prompt(question_content: str, question_type: str, current_content: str) -> str:
        """Generate final answer from collaborative draft - optimized for HotpotQA"""
        
        # Special handling for HotpotQA to ensure short, precise answers
        if question_type == "hotpotqa":
            return f"""You are synthesizing a collaborative draft into a final answer for HotpotQA.

QUESTION: {question_content}

COLLABORATIVE DRAFT:
{current_content}

CRITICAL INSTRUCTIONS for HotpotQA - FOLLOW EXACTLY:
1. Output format must be: ANSWER: [core answer]
2. Core answer must be in the shortest form:
   - Person names: name only, no titles or descriptions
   - Companies: core name or type only
   - Dates: specific date only
   - Places: place name only
3. Absolutely do not include:
   - Explanatory text
   - Reasoning process
   - Numbers or lists
   - Complete sentences
   - Punctuation marks (except necessary ones)

Examples:
- For "What year was X born?" → ANSWER: 1995
- For "Who is the CEO of X?" → ANSWER: John Smith
- For "Which company owns X?" → ANSWER: Microsoft
- For "What type of company is AIA Group?" → ANSWER: life insurance group
- For "What TV show is X a prequel to?" → ANSWER: The Big Bang Theory

From the collaborative draft above, extract only the most core answer:

ANSWER:"""

    @staticmethod
    def get_enhanced_synthesis_prompt(question_content: str, question_type: str, current_content: str) -> str:
        """Get enhanced synthesis prompt - dual channel output, force concise answers"""

        # General hard constraint instructions
        hard_constraints = """
CRITICAL INSTRUCTIONS - MUST FOLLOW EXACTLY:
1. Ignore all meta-information in the draft ("Here's an improved draft", "Key improvements", "The draft now", etc.)
2. Focus only on actual factual content and reasoning results
3. Output format must be: ANSWER: [shortest answer]
4. Answer must be one line, no more than 20 words
5. Do not add any explanations, reasoning process, or extra text
6. Only when you have determined the final answer, output ANSWER: <shortest answer>, and do not add any other text
"""

        # Special handling for HotpotQA
        if question_type == "hotpotqa":
            return f"""You are extracting the final answer for a HotpotQA question.

QUESTION: {question_content}

COLLABORATIVE DRAFT:
{current_content}

{hard_constraints}

HotpotQA special requirements:
- Person names: name only (e.g., John Smith)
- Company types: include complete description (e.g., pan-Asian life insurance group)
- Dates: specific date only (e.g., March 15, 1995)
- Places: place name only (e.g., New York)
- TV shows: show name only (e.g., The Big Bang Theory)
- Keep key modifiers from the original text (e.g., largest, independent, pan-Asian, etc.)

Extract the core answer from the draft:

ANSWER:"""

        # Special handling for Math problems
        elif question_type == "math":
            return f"""You are extracting the final answer for a Math question.

QUESTION: {question_content}

COLLABORATIVE DRAFT:
{current_content}

{hard_constraints}

Math special requirements:
- Output only numerical values, no units, explanations, or reasoning process
- Simplify fractions to their simplest form
- Keep reasonable precision for decimals
- Do not include "=" signs or other symbols

Examples:
- For "What is 2+3?" → ANSWER: 5
- For "Solve for x: 2x=10" → ANSWER: 5
- For "What is 1/2 + 1/4?" → ANSWER: 3/4

Extract the numerical answer from the draft:

ANSWER:"""

        # Special handling for GSM8K problems
        elif question_type == "gsm8k":
            return f"""You are extracting the final answer for a GSM8K question.

QUESTION: {question_content}

COLLABORATIVE DRAFT:
{current_content}

{hard_constraints}

GSM8K special requirements:
- Look for final answer in "#### number" format
- Output only the number part, not the #### symbol
- Do not include units, explanations, or reasoning process
- Integer answers do not need decimal points

Examples:
- For "#### 15" → ANSWER: 15
- For "#### 25" → ANSWER: 25
- For "#### 31" → ANSWER: 31

Extract the numerical answer from the draft (pay special attention to #### format):

ANSWER:"""

        # Special handling for coding problems
        elif question_type in ["mbpp", "humaneval"]:
            return f"""You are extracting the final code for a programming question.

QUESTION: {question_content}

COLLABORATIVE DRAFT:
{current_content}

CRITICAL INSTRUCTIONS for Coding:
1. Output complete Python function code, not single-line expressions
2. Maintain correct indentation and formatting
3. Include function definition and complete function body
4. Do not add extra explanations or comments
5. Ensure the code can be executed directly

Examples:
- For function definition → Output complete function
- For algorithm implementation → Output complete implementation

Extract the complete code from the draft:

```python
[complete function code]
```"""

        # For other question types, use enhanced synthesis
        return f"""You are extracting the final answer for a {question_type} question.

QUESTION: {question_content}

COLLABORATIVE DRAFT:
{current_content}

{hard_constraints}

General requirements:
- Person names: name only
- Yes/no questions: Yes or No only
- Dates: specific date only
- Companies: core name only

Examples:
- For person names → ANSWER: John Smith
- For yes/no questions → ANSWER: Yes
- For dates → ANSWER: March 15, 1995
- For companies → ANSWER: Microsoft Corporation

Extract the core answer from the draft:

ANSWER:"""

    @staticmethod  
    def get_direct_answer_prompt(question_content: str, question_type: str) -> str:
        """Generate answer directly from question when draft is empty"""
        
        # Special handling for HotpotQA
        if question_type == "hotpotqa":
            return f"""Answer this HotpotQA question directly and concisely.

QUESTION: {question_content}

CRITICAL INSTRUCTIONS:
1. Output only the shortest answer itself (no periods, explanations, or numbering)
2. Phrase-style answers are sufficient, no complete sentences or explanations needed
3. Output format: ANSWER: [short answer]

ANSWER:"""

        return f"""Answer this {question_type} question directly.

QUESTION: {question_content}

Provide a clear, accurate answer:"""
    
    @staticmethod
    def get_assessment_prompt(question_content: str, question_type: str, current_content: str) -> str:
        """Assess the quality of the current draft"""
        return f"""Assess the quality of this collaborative draft for a {question_type} question.

QUESTION: {question_content}

DRAFT:
{current_content}

Evaluate on a scale of 0.0 to 1.0 based on:
1. Accuracy and correctness
2. Completeness of the answer
3. Clarity and coherence
4. Relevance to the question

Provide only a numerical score (e.g., 0.85):"""
    
    @staticmethod
    def get_quality_assessment_prompt(question_content: str, question_type: str, final_answer: str) -> str:
        """Assess the quality of the final generated answer"""
        return f"""Assess the quality of this final answer for a {question_type} question.

QUESTION: {question_content}

FINAL ANSWER: {final_answer}

Rate the answer quality on a scale of 0.0 to 1.0 considering:
1. Accuracy and correctness
2. Completeness
3. Clarity and format appropriateness
4. Direct relevance to the question

Provide only a numerical score (e.g., 0.92):"""

class ConsensusPrompts:
    """Consensus mechanism related Prompt class"""

    @staticmethod
    def get_consensus_synthesis_prompt(consensus_item) -> str:
        """Get consensus synthesis prompt"""
        return f"""You are facilitating a consensus discussion among AI agents.

DISCUSSION TOPIC: {getattr(consensus_item, 'subject', 'General discussion')}
CURRENT STATUS: {getattr(consensus_item, 'status', 'in_discussion')}

Based on the discussion messages, synthesize a consensus view that:
1. Addresses the main points of agreement
2. Acknowledges areas of disagreement
3. Provides a balanced synthesis

Keep the synthesis concise and actionable."""

    @staticmethod
    def get_consensus_moderator_system_prompt() -> str:
        """Get consensus moderator system prompt"""
        return """You are a consensus moderator for AI agent discussions. Your role is to:
- Facilitate productive discussions
- Identify areas of agreement and disagreement
- Synthesize different viewpoints into coherent consensus
- Maintain objectivity and balance
- Focus on actionable outcomes"""

    @staticmethod
    def get_agent_discussion_prompt(agent_id: str, discussion_context: str) -> str:
        """Get agent discussion prompt"""
        return f"""You are agent {agent_id} participating in a collaborative discussion.

DISCUSSION CONTEXT: {discussion_context}

Provide your perspective on this topic. Be:
- Constructive and collaborative
- Specific and evidence-based
- Open to other viewpoints
- Focused on finding solutions

Your response should be concise but substantive."""

class MergeAgentPrompts:
    """Merge agent related Prompt class"""

    @staticmethod
    def get_sequential_merge_prompt() -> str:
        """Get sequential merge prompt"""
        return """You are merging annotations sequentially. Integrate each suggestion in order while maintaining coherence.

CRITICAL FOR PROGRAMMING TASKS:
- Maintain COMPLETE code without truncation
- Preserve ALL function logic and syntax
- Keep working implementations intact"""

    @staticmethod
    def get_semantic_synthesis_prompt() -> str:
        """Get semantic synthesis prompt"""
        return """You are performing semantic synthesis. Intelligently combine related suggestions and create a coherent unified response.

CRITICAL FOR PROGRAMMING TASKS:
- Ensure ALL code remains COMPLETE and FUNCTIONAL
- DO NOT truncate any function definitions, loops, or statements
- Maintain proper syntax and closing brackets/statements
- Preserve all working code logic when integrating suggestions
- Focus on functionality over style improvements"""

    @staticmethod
    def get_conflict_resolution_prompt() -> str:
        """Get conflict resolution prompt"""
        return """You are resolving conflicts between annotations. Apply systematic conflict resolution strategies:

CONFLICT RESOLUTION STRATEGIES:
1. EVIDENCE-BASED: Choose the suggestion with stronger supporting evidence
2. DOMAIN-EXPERTISE: Prioritize suggestions from domain-specific knowledge
3. LOGICAL-CONSISTENCY: Select the option that maintains logical coherence
4. SYNTHESIS: Combine compatible elements from conflicting suggestions
5. CONSERVATIVE: When uncertain, preserve the original content

RESOLUTION PROCESS:
- Identify the root cause of each conflict
- Evaluate the validity of each conflicting suggestion
- Apply the most appropriate resolution strategy
- Provide clear rationale for your decision
- Ensure the resolution maintains overall coherence

Focus on creating a solution that is both accurate and well-reasoned."""

    @staticmethod
    def get_priority_based_prompt() -> str:
        """Get priority-based prompt"""
        return """You are merging based on priority. Focus on the most important suggestions while incorporating others where appropriate."""

    @staticmethod
    def get_creative_combination_prompt() -> str:
        """Get creative combination prompt"""
        return """You are creatively combining diverse perspectives. Synthesize different viewpoints into an innovative solution."""

    @staticmethod
    def get_conflict_detection_prompt() -> str:
        """Get conflict detection prompt"""
        return """You are analyzing annotations for conflicts. Identify contradictory suggestions and categorize conflict types.

CURRENT DRAFT:
{current_draft}

ANNOTATIONS TO ANALYZE:
{annotations_text}

Analyze these annotations for conflicts and respond in JSON format:
{{
    "conflicts": [
        {{
            "type": "methodological|computational|interpretation|factual",
            "annotation_ids": ["id1", "id2"],
            "severity": "low|moderate|high",
            "description": "Brief description of the conflict",
            "target_text": "The specific text being disputed"
        }}
    ],
    "conflict_summary": {{
        "total_conflicts": 0,
        "conflict_rate": 0.0,
        "most_common_type": "type"
    }}
}}"""

    @staticmethod
    def get_annotation_analysis_prompt() -> str:
        """Get annotation analysis prompt"""
        return """You are analyzing the relationships between annotations and the current draft.

CURRENT DRAFT:
{current_draft}

ANNOTATIONS:
{annotations_text}

Analyze the annotations and respond in JSON format:
{{
    "annotation_relationships": [
        {{
            "annotation_id": "id",
            "relationship_type": "supportive|conflicting|complementary|independent",
            "confidence": 0.8,
            "reasoning": "Brief explanation"
        }}
    ],
    "overall_coherence": 0.7,
    "merge_complexity": "low|moderate|high"
}}"""

    @staticmethod
    def get_merge_validation_prompt() -> str:
        """Get merge validation prompt"""
        return """You are validating a merge result to ensure quality and coherence.

ORIGINAL DRAFT:
{original_draft}

MERGED CONTENT:
{merged_content}

MERGE RATIONALE:
{rationale}

Validate the merge and respond in JSON format:
{{
    "validation_result": {{
        "is_valid": true,
        "quality_score": 0.8,
        "coherence_score": 0.9,
        "completeness_score": 0.7,
        "issues": ["list of any issues found"],
        "recommendations": ["suggestions for improvement"]
    }}
}}"""

    @staticmethod
    def create_full_merge_prompt(strategy_prompt: str, current_draft: str,
                               annotations_text: str, analysis_text: str, conflicts_text: str) -> str:
        """Create complete merge prompt"""
        return f"""You are a merge agent combining multiple annotations into a coherent draft.

STRATEGY: {strategy_prompt}

CURRENT DRAFT:
{current_draft}

ANNOTATIONS TO MERGE:
{annotations_text}

ANALYSIS:
{analysis_text}

CONFLICTS:
{conflicts_text}

Your task: Create an improved draft that incorporates the valuable suggestions while resolving any conflicts. Maintain the original intent while improving quality."""

# ========================= Main Export Interface =========================

def get_optimized_prompt(agent_type: str, task_type: str, **kwargs) -> str:
    """Get optimized prompt"""
    if agent_type == "worker":
        return create_complete_worker_prompt(task_type, kwargs.get('question', ''), kwargs.get('context'))
    elif agent_type == "merger":
        return get_merger_prompt(kwargs.get('prompt_type', 'merge_annotations'), **kwargs)
    elif agent_type == "leader":
        return get_leader_evaluation_prompt(kwargs.get('prompt_type', 'evaluate_quality'), **kwargs)
    else:
        return get_system_prompt(task_type)