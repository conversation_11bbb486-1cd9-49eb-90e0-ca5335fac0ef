import os
import json
import threading
import multiprocessing
from typing import Dict, Any

# Note: dotenv not needed since API keys are set in system environment

_config_lock = threading.RLock()

DEBUG_MODE = False
VERBOSE_MODE = False
ENABLE_CACHE = True

CPU_COUNT = multiprocessing.cpu_count()
IS_CLOUD_ENV = os.getenv("CLOUD_ENVIRONMENT", "0") == "1"

PROVIDER_IDS = ["openai", "anthropic", "llama", "gemini"]

PROVIDER_NAMES = {
    "openai": "OpenAI",
    "anthropic": "<PERSON>",
    "llama": "<PERSON>lama",
    "gemini": "Gemini"
}

DEFAULT_MODELS = {
    "openai": "gpt-4o-mini",
    "anthropic": "claude-3-5-haiku-20241022",
    "llama": "llama-3.3-70b-versatile",
    "gemini": "gemini-2.0-flash"
}

# API Keys with correct official naming and fallback loading
def load_api_keys():
    """Load API keys with multiple fallback methods"""
    api_keys = {}

    # Method 1: Direct environment variable access
    for provider in PROVIDER_IDS:
        if provider == "llama":
            key = os.getenv("GROQ_API_KEY", "")
        elif provider == "gemini":
            key = os.getenv("GEMINI_API_KEY", "")
        else:
            key = os.getenv(f"{provider.upper()}_API_KEY", "")

        api_keys[provider] = key

    # Method 2: Try to source from shell if no keys found
    if not any(key.strip() for key in api_keys.values()):
        try:
            import subprocess
            # Try to get environment from zsh
            result = subprocess.run(
                ['zsh', '-c', 'source ~/.zshrc && env'],
                capture_output=True, text=True, timeout=5
            )

            if result.returncode == 0:
                env_vars = {}
                for line in result.stdout.split('\n'):
                    if '=' in line:
                        key, value = line.split('=', 1)
                        env_vars[key] = value

                # Update API keys from shell environment
                for provider in PROVIDER_IDS:
                    if provider == "llama":
                        key = env_vars.get("GROQ_API_KEY", "")
                    elif provider == "gemini":
                        key = env_vars.get("GEMINI_API_KEY", "")
                    else:
                        key = env_vars.get(f"{provider.upper()}_API_KEY", "")

                    if key.strip():
                        api_keys[provider] = key

        except Exception as e:
            print(f"Warning: Could not load environment from shell: {e}")

    return api_keys

API_KEYS = load_api_keys()

API_BASES = {
    "openai": os.getenv("OPENAI_API_BASE", "https://api.openai.com/v1"),
    "anthropic": os.getenv("ANTHROPIC_API_BASE", "https://api.anthropic.com"),
    "llama": os.getenv("GROQ_API_BASE", "https://api.groq.com/openai/v1"),
    "gemini": os.getenv("GEMINI_API_BASE", "https://generativelanguage.googleapis.com")
}

ADVANCED_CONFIG = {
    # Core API settings
    "retry_attempts": 6,
    "retry_base_delay": 0.3,
    "timeout": 30 if IS_CLOUD_ENV else 25,  # Reduced from 120/100 to 30/25
    "max_tokens": 4096,

    # Concurrency settings
    "max_concurrent": 20,
    "max_workers": max(64, CPU_COUNT * 6),
    "batch_size": max(64, min(CPU_COUNT * 12, 128)),

    # Cache settings
    "max_cache_size": 25000,
    "cache_ttl": 3600 * 24,

    # Multi-Agent System Settings  
    "enable_context_engineering": True,
    # Memory system simplified to lightweight cache
    "enable_draft_mode": True,

    # Context optimization
    "context_max_tokens": 2000,
    "context_compression_ratio": 0.7,
    "semantic_similarity_threshold": 0.85,

    # Memory system
    "memory_max_items": 100,
    "memory_relevance_threshold": 0.7,
    "memory_decay_factor": 0.95,
    
    # Few-shot learning
    "fewshot_max_examples_per_subtype": 3,
    "fewshot_max_subtypes_per_task": 5,
    "fewshot_enabled": True,

    # Task processing - Multi-agent draft mode
    "multi_agent_token_budget": 6000,
    
    # Task-specific Worker token limits
    "worker_token_limits": {
        "humaneval": 2048,      # Programming tasks need more tokens to generate complete code
        "mbpp": 2048,           # Programming tasks need more tokens
        "math": 1536,           # Math reasoning needs moderate tokens
        "gsm8k": 1024,          # Arithmetic problems are relatively simple
        "hotpotqa": 1536,       # Multi-hop reasoning needs more tokens
        "strategyqa": 1024,     # Strategy reasoning problems
        "gpqa": 1536,           # Scientific reasoning needs more tokens
        "mmlu": 1024,           # Knowledge Q&A problems
        "default": 1024         # Default token limit, doubled from original 512
    },
    
    # Task-specific annotation token limits
    "annotation_token_limits": {
        "humaneval": 150,       # Programming annotations need more space to describe code issues
        "mbpp": 150,           # Programming annotations need more space
        "math": 120,           # Math annotations need to explain reasoning problems
        "gsm8k": 100,          # Arithmetic annotations are relatively simple
        "hotpotqa": 120,       # Multi-hop reasoning annotations need to explain logic chains
        "strategyqa": 100,     # Strategy reasoning annotations
        "gpqa": 120,           # Scientific annotations need to explain professional concepts
        "mmlu": 100,           # Knowledge Q&A annotations
        "default": 100         # Default annotation token limit, increased from original 80
    },
    
    # Collaborative System Configuration
    "use_collaborative_system": True,  # Always use collaborative system
    "collaborative_max_rounds": 3,    # Restored from 1 to 3 for better quality
    "collaborative_max_duration": 180, # Restored from 60 to 180 seconds
    "collaborative_quality_threshold": 0.8,  # Quality threshold for early termination

    # Enhanced Collaborative System Configuration (NEW)
    "use_enhanced_collaborative": True,  # Whether to use enhanced collaborative system with annotations
    "enhanced_max_annotation_rounds": 2,  # Increased from 1 to 2
    "enhanced_consensus_threshold": 0.7,  # Restored from 0.5 to 0.7 - higher consensus
    "enhanced_max_global_iterations": 3,  # Increased from 1 to 3
    "enhanced_task_adaptive": True,      # Whether to use task-adaptive settings
    "enhanced_enable_merge_agent": True, # Enable intelligent merge agent
    "enhanced_enable_feedback_loop": True, # Enable feedback loop mechanism

    # Merge Agent Configuration
    "merge_agent_model": "gemini",      # Model to use for merge agent (openai, anthropic, llama, gemini, etc.)
    "merge_agent_max_tokens": 4096,     # Maximum tokens for merge agent responses
    "merge_agent_temperature": 0.3,     # Temperature for merge agent (lower for more deterministic merging)

    # Risk Mitigation Settings for Enhanced System
    "prevent_infinite_discussion": True,  # Prevent infinite discussion loops
    "discussion_timeout_seconds": 120,   # Increased from 30 to 120 seconds
    "max_token_budget_per_question": 12000,  # Increased from 8000 to 12000 tokens
    "enable_fallback_on_failure": True,  # Fallback to simpler system on failure
    
    # Ablation Study Settings
    "baseline_mode": False,  # Run in baseline mode (single model)
    "enhanced_enable_annotation": True,  # Enable annotation system
    "enhanced_enable_context_compression": True,  # Enable context compression
}

FORMAT_INSTRUCTIONS = {
    "gemini": "Please provide your response in plain text without using markdown formatting such as headers, bullet points, or code blocks."
}

MODEL_CONFIG = {}

def get_model_for_provider(provider_id: str) -> str:
    return DEFAULT_MODELS.get(provider_id, "")

def _init_model_config():
    global MODEL_CONFIG
    MODEL_CONFIG = {}

    for provider_id in PROVIDER_IDS:
        MODEL_CONFIG[provider_id] = {
            "name": PROVIDER_NAMES[provider_id],
            "model": get_model_for_provider(provider_id),
            "api_key": API_KEYS[provider_id],
            "api_base": API_BASES[provider_id],
            "max_tokens": ADVANCED_CONFIG["max_tokens"],
            "timeout": ADVANCED_CONFIG["timeout"]
        }

def get_model_config(agent_id: str) -> Dict[str, Any]:
    if not MODEL_CONFIG:
        _init_model_config()
    return MODEL_CONFIG.get(agent_id, {})

def get_format_instructions(agent_id: str) -> str:
    """Get format instructions for the specified agent"""
    return FORMAT_INSTRUCTIONS.get(agent_id, "")

def update_format_instructions(agent_id: str, instructions: str) -> bool:
    """Update format instructions for the specified agent"""
    with _config_lock:
        if agent_id:
            FORMAT_INSTRUCTIONS[agent_id] = instructions
            return True
        return False

def get_worker_token_limit(task_type: str) -> int:
    """Get task-specific worker token limit"""
    worker_limits = get_config("worker_token_limits", {})
    return worker_limits.get(task_type, worker_limits.get("default", 1024))

def get_annotation_token_limit(task_type: str) -> int:
    """Get task-specific annotation token limit"""
    annotation_limits = get_config("annotation_token_limits", {})
    return annotation_limits.get(task_type, annotation_limits.get("default", 100))

def get_config(key: str, default_value: Any = None) -> Any:
    with _config_lock:
        if key in ADVANCED_CONFIG:
            return ADVANCED_CONFIG[key]

        global_keys = {
            "debug": DEBUG_MODE,
            "verbose": VERBOSE_MODE,
            "cache": ENABLE_CACHE,
            "cpu_count": CPU_COUNT,
            "is_cloud_env": IS_CLOUD_ENV
        }

        if key in global_keys:
            return global_keys[key]

        return default_value

def update_config(key: str, value: Any) -> bool:
    """Update configuration value"""
    with _config_lock:
        global DEBUG_MODE, VERBOSE_MODE, ENABLE_CACHE

        if key in ADVANCED_CONFIG:
            ADVANCED_CONFIG[key] = value
            return True

        if key == "debug":
            DEBUG_MODE = bool(value)
            return True
        elif key == "verbose":
            VERBOSE_MODE = bool(value)
            return True
        elif key == "cache":
            ENABLE_CACHE = bool(value)
            return True

        return False

def update_model_config(agent_id: str, param: str, value: Any) -> bool:
    with _config_lock:
        if agent_id in MODEL_CONFIG:
            MODEL_CONFIG[agent_id][param] = value
            return True
        return False

def save_config_to_file(filename: str = "config.json") -> bool:
    """Save current configuration to file"""
    try:
        config_data = {
            "version": "2.0",
            "models": DEFAULT_MODELS,
            "debug_mode": DEBUG_MODE,
            "verbose_mode": VERBOSE_MODE,
            "enable_cache": ENABLE_CACHE,
            "advanced_config": ADVANCED_CONFIG
        }

        with open(filename, "w") as f:
            json.dump(config_data, f, indent=2)

        return True
    except Exception as e:
        print(f"Error saving config: {str(e)}")
        return False

def load_config_from_file(filename: str = "config.json") -> bool:
    """Load configuration from file"""
    global DEBUG_MODE, VERBOSE_MODE, ENABLE_CACHE
    global DEFAULT_MODELS, ADVANCED_CONFIG

    try:
        if not os.path.exists(filename):
            return False

        with open(filename, "r") as f:
            config_data = json.load(f)

        with _config_lock:
            if "models" in config_data:
                DEFAULT_MODELS.update(config_data["models"])
            if "debug_mode" in config_data:
                DEBUG_MODE = config_data["debug_mode"]
            if "verbose_mode" in config_data:
                VERBOSE_MODE = config_data["verbose_mode"]
            if "enable_cache" in config_data:
                ENABLE_CACHE = config_data["enable_cache"]
            if "advanced_config" in config_data:
                ADVANCED_CONFIG.update(config_data["advanced_config"])

        return True
    except Exception as e:
        print(f"Error loading config: {str(e)}")
        return False

_init_model_config()