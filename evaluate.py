#!/usr/bin/env python3
"""
Evaluation entry point - evaluates model outputs against benchmark datasets

This is the new entry point for evaluation. The old evaluator.py is deprecated.

Usage:
    python evaluate.py --results-file results/gsm8k_result.json --dataset gsm8k
    python evaluate.py --results-file results/full_result.json --dataset all --verbose
"""

import asyncio
import sys
import os

# Add the current directory to Python path to ensure imports work
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from evaluator import main


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nEvaluation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"Error during evaluation: {e}")
        sys.exit(1)
