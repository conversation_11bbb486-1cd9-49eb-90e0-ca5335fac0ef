# Supplementary Material: Dynamic Multi-Agent Collaboration (DMC) Framework

## Overview

This supplementary material accompanies our AAAI submission on the Dynamic Multi-Agent Collaboration (DMC) framework. The DMC framework implements a novel approach to multi-agent collaboration for complex reasoning tasks, featuring adaptive merge strategies, consensus-based annotations, and dynamic quality assessment.

## System Architecture

### Core Components

1. **Multi-Agent Collaboration Controller** (`coordination/controller.py`)
   - Orchestrates the collaborative workflow
   - Implements adaptive round determination
   - Manages consensus-based decision making

2. **Adaptive Merge Strategy Selector** (`coordination/merge_strategy.py`)
   - Automatically selects optimal merge strategies
   - Analyzes annotation consistency and conflict severity
   - Provides strategy recommendations with confidence scores

3. **Shared Draft System** (`coordination/draft.py`)
   - Maintains collaborative documents with version control
   - Supports real-time annotations and consensus building
   - Tracks evolution history for transparency

4. **Context Compression Engine** (`coordination/context_compressor.py`)
   - Intelligent context management for long conversations
   - Token-aware compression with semantic preservation
   - Adaptive compression strategies

### Agent Modules

1. **Worker Agents** (`agent/worker/`)
   - Generate initial drafts and annotations
   - Participate in collaborative discussions
   - Provide peer review and consensus responses

2. **Leader Agent** (`agent/leader/`)
   - Conducts quality evaluation with configurable criteria
   - Provides structured feedback and improvement suggestions
   - Makes final approval decisions

3. **Merger Agent** (`agent/merger/`)
   - Integrates multiple drafts using various strategies
   - Resolves conflicts and maintains coherence
   - Applies consensus-based improvements

## Key Features

### 1. Adaptive Collaboration Workflow
- **Dynamic Round Determination**: Automatically adjusts collaboration rounds based on task complexity
- **Consensus-Based Annotations**: Workers provide annotations that require peer consensus
- **Quality-Driven Termination**: Collaboration continues until quality thresholds are met

### 2. Intelligent Merge Strategies
- **Semantic Synthesis**: Deep understanding-based merging
- **Consensus Voting**: Democratic decision making
- **Authority-Based**: Leveraging agent expertise
- **Conflict Resolution**: Structured disagreement handling

### 3. Configurable Quality Assessment
- **Task-Specific Evaluation**: Customized criteria for different domains
- **Multi-Dimensional Scoring**: Accuracy, completeness, clarity metrics
- **Structured Feedback**: Actionable improvement suggestions

## Supported Benchmarks

The framework supports evaluation on 8 major benchmarks:

1. **Mathematical Reasoning**
   - GSM8K: Grade school math word problems
   - MATH: Competition-level mathematics

2. **Code Generation**
   - HumanEval: Python function synthesis
   - MBPP: Mostly Basic Python Problems

3. **Reading Comprehension**
   - HotpotQA: Multi-hop reasoning
   - StrategyQA: Strategic reasoning

4. **Knowledge & Science**
   - MMLU: Massive multitask language understanding
   - GPQA: Graduate-level science questions

## Installation and Setup

### Prerequisites
- Python 3.8+
- Required API keys (OpenAI, Anthropic, etc.)
- Dependencies listed in requirements (see config.py)

### Environment Setup
```bash
# Set API keys
export OPENAI_API_KEY="your_key_here"
export ANTHROPIC_API_KEY="your_key_here"
export GROQ_API_KEY="your_key_here"  # For Llama models
export GEMINI_API_KEY="your_key_here"

# Install dependencies
pip install openai anthropic groq google-generativeai
```

## Usage Examples

### Single Problem Processing
```bash
python main.py --prompt "What is 25 * 17?" --models openai anthropic --task-type gsm8k
```

### Benchmark Evaluation
```bash
python main.py --jsonl benchmark/gsm8k.jsonl --models openai anthropic llama --max_problems 100 --output results/gsm8k_result.json
```

### Batch Processing
```bash
bash scripts/run_all.sh
```

## Configuration

### Task-Specific Settings
The framework provides task-specific configurations in `config.py`:

- **Token Limits**: Customized for each benchmark type
- **Quality Thresholds**: Task-appropriate evaluation criteria
- **Collaboration Rounds**: Adaptive based on complexity

### Evaluation Configuration
Detailed evaluation criteria are defined in `config/evaluation_config.yaml`:

- **Multi-dimensional scoring** with customizable weights
- **Task-specific rubrics** for different domains
- **Configurable thresholds** for approval/revision decisions

## File Structure

```
DMC/
├── agent/                    # Agent implementations
│   ├── leader/              # Leader agent with quality evaluation
│   ├── merger/              # Merger agent with strategy selection
│   └── worker/              # Worker agents with annotation system
├── coordination/            # Core collaboration framework
│   ├── controller.py        # Main collaboration controller
│   ├── draft.py            # Shared draft system
│   ├── merge_strategy.py   # Adaptive merge strategies
│   └── context_compressor.py # Context management
├── evaluator/              # Benchmark-specific evaluators
├── utils/                  # Utility functions
├── benchmark/              # Benchmark datasets
├── scripts/                # Execution scripts
├── config.py              # System configuration
├── main.py                # Main entry point
└── prompt.py              # Prompt templates
```

## Experimental Results

Results are saved in structured JSON format with detailed metadata:

- **Solution Quality**: Multi-dimensional evaluation scores
- **Processing Efficiency**: Token usage and timing metrics
- **Collaboration Statistics**: Round counts and consensus rates
- **Strategy Performance**: Merge strategy effectiveness

## Reproducibility

All experiments can be reproduced using the provided scripts:

1. **Full Evaluation**: `bash scripts/run_all.sh`
2. **Sample Testing**: `bash scripts/run_sample.sh`
3. **Individual Benchmarks**: Use dataset-specific commands

## Technical Details

### Novel Contributions

1. **Adaptive Collaboration**: Dynamic adjustment of collaboration intensity based on task complexity
2. **Consensus-Based Annotations**: Structured peer review with agreement tracking
3. **Intelligent Merge Strategies**: Automatic strategy selection based on annotation analysis
4. **Context-Aware Compression**: Semantic-preserving context management for long conversations

### Performance Optimizations

- **Token-Efficient Processing**: Intelligent context compression and management
- **Parallel Agent Execution**: Concurrent draft generation and evaluation
- **Caching System**: Lightweight caching for repeated operations
- **Quality-Driven Termination**: Early stopping when quality targets are met

## Contact and Support

For questions about the implementation or experimental setup, please refer to the main paper or contact the authors.

---

*This supplementary material provides complete implementation details and reproducible experimental setup for the DMC framework presented in our AAAI submission.*
