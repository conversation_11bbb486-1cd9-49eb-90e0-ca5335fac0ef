# Experiment Configuration for DMC Framework

## Benchmark Datasets

### Mathematical Reasoning
- **GSM8K**: 1,319 grade school math word problems
- **MATH**: 12,500 competition mathematics problems

### Code Generation  
- **HumanEval**: 164 Python programming problems
- **MBPP**: 974 Python programming problems

### Reading Comprehension
- **HotpotQA**: 7,405 multi-hop reasoning questions
- **StrategyQA**: 2,290 strategy questions requiring reasoning

### Knowledge & Science
- **MMLU**: 15,908 multiple-choice questions across 57 subjects
- **GPQA**: 448 graduate-level science questions

## Model Configuration

### Supported Models
```python
DEFAULT_MODELS = {
    "openai": "gpt-4o-mini",
    "anthropic": "claude-3-5-haiku-20241022", 
    "llama": "llama-3.3-70b-versatile",
    "gemini": "gemini-2.0-flash"
}
```

### API Settings
```python
ADVANCED_CONFIG = {
    "retry_attempts": 6,
    "retry_base_delay": 0.3,
    "timeout": 30,
    "max_tokens": 4096,
    "max_concurrent": 20,
    "temperature": 0.3
}
```

## Task-Specific Parameters

### Token Limits
```python
WORKER_TOKEN_LIMITS = {
    "humaneval": 2048,    # Programming tasks need more tokens
    "mbpp": 2048,         # Programming tasks need more tokens  
    "math": 1536,         # Math reasoning needs moderate tokens
    "gsm8k": 1024,        # Arithmetic problems are simpler
    "hotpotqa": 1536,     # Multi-hop reasoning needs more tokens
    "strategyqa": 1024,   # Strategy reasoning problems
    "gpqa": 1536,         # Scientific reasoning needs more tokens
    "mmlu": 1024,         # Knowledge Q&A problems
    "default": 1024
}

ANNOTATION_TOKEN_LIMITS = {
    "humaneval": 150,     # Programming annotations need more space
    "mbpp": 150,          # Programming annotations need more space
    "math": 120,          # Math annotations need reasoning explanation
    "gsm8k": 100,         # Arithmetic annotations are simpler
    "hotpotqa": 120,      # Multi-hop reasoning annotations
    "strategyqa": 100,    # Strategy reasoning annotations
    "gpqa": 120,          # Scientific annotations need explanation
    "mmlu": 100,          # Knowledge Q&A annotations
    "default": 100
}
```

### Collaboration Settings
```python
COLLABORATION_CONFIG = {
    "max_rounds": 3,
    "quality_threshold": 0.8,
    "max_duration": 180,  # seconds
    "context_max_tokens": 2000,
    "compression_ratio": 0.7
}
```

## Quality Evaluation Criteria

### Multi-Dimensional Scoring
Each task type uses customized evaluation dimensions:

#### GSM8K (Mathematical Reasoning)
- **Accuracy** (35%): Mathematical correctness
- **Completeness** (25%): Solution completeness  
- **Clarity** (25%): Explanation clarity
- **Efficiency** (15%): Solution efficiency

#### HumanEval (Code Generation)
- **Correctness** (40%): Code functionality
- **Completeness** (25%): Implementation completeness
- **Code Quality** (20%): Code style and efficiency
- **Robustness** (15%): Edge case handling

#### HotpotQA (Multi-hop Reasoning)
- **Accuracy** (30%): Answer correctness
- **Reasoning** (30%): Multi-hop reasoning quality
- **Completeness** (25%): Information completeness
- **Clarity** (15%): Explanation clarity

### Quality Thresholds
```python
QUALITY_THRESHOLDS = {
    'gsm8k': 0.95,      # High threshold for math accuracy
    'math': 0.80,       # Moderate threshold for complex math
    'mbpp': 0.75,       # Standard threshold for programming
    'humaneval': 0.65,  # Lower threshold due to complexity
    'hotpotqa': 0.70,   # Moderate threshold for reasoning
    'strategyqa': 0.75, # Standard threshold for strategy
    'gpqa': 0.75,       # Standard threshold for science
    'mmlu': 0.85,       # High threshold for knowledge
}
```

## Experimental Conditions

### Ablation Study Configurations

#### 1. Baseline (Single Agent)
```bash
--baseline-mode --models openai
```

#### 2. Multi-Agent without Merge
```bash
--disable-merge --models openai anthropic
```

#### 3. Multi-Agent without Annotations
```bash
--disable-annotation --models openai anthropic
```

#### 4. Multi-Agent without Leader
```bash
--disable-leader --models openai anthropic
```

#### 5. Full DMC Framework
```bash
--models openai anthropic llama --context-optimization
```

### Collaboration Round Analysis
Test different collaboration intensities:
- 1 round: `--max-collaboration-rounds 1`
- 2 rounds: `--max-collaboration-rounds 2` 
- 3 rounds: `--max-collaboration-rounds 3`
- Adaptive: Default behavior

### Model Combination Studies
- **Homogeneous**: `--models openai openai openai`
- **Heterogeneous**: `--models openai anthropic llama`
- **Pair-wise**: `--models openai anthropic`
- **Single**: `--models openai`

## Merge Strategy Evaluation

### Available Strategies
1. **Semantic Synthesis**: Deep understanding-based merging
2. **Consensus Voting**: Democratic decision making
3. **Authority-Based**: Expertise-weighted decisions
4. **Conflict Resolution**: Structured disagreement handling
5. **Simple Concatenation**: Basic combination
6. **Longest Selection**: Choose longest response

### Strategy Selection Criteria
- Annotation consistency level
- Conflict severity assessment
- Task type requirements
- Historical performance data

## Performance Metrics

### Efficiency Tracking
- **Token Usage**: Input/output token consumption
- **Processing Time**: End-to-end execution time
- **API Calls**: Number of model invocations
- **Collaboration Rounds**: Actual rounds used
- **Context Compression**: Compression ratios achieved

### Quality Metrics
- **Multi-dimensional Scores**: Task-specific evaluation
- **Consensus Rates**: Agreement levels achieved
- **Strategy Effectiveness**: Merge strategy performance
- **Improvement Tracking**: Quality progression over rounds

## Resource Management

### Concurrency Control
```python
CONCURRENCY_CONFIG = {
    "max_concurrent": 20,
    "max_workers": 64,
    "batch_size": 64,
    "rate_limit_buffer": 0.1
}
```

### Memory Management
```python
MEMORY_CONFIG = {
    "max_cache_size": 25000,
    "cache_ttl": 3600 * 24,
    "context_window": 2000,
    "compression_threshold": 0.8
}
```

## Reproducibility Settings

### Random Seed Control
- Fixed temperature: 0.3 for all models
- Deterministic model selection
- Consistent prompt formatting
- Stable evaluation criteria

### Environment Variables
```bash
export OPENAI_API_KEY="your_key"
export ANTHROPIC_API_KEY="your_key"
export GROQ_API_KEY="your_key"
export GEMINI_API_KEY="your_key"
export MAX_CONCURRENT=20
export TIMEOUT=30
```

## Evaluation Protocol

### Problem Sampling
- Random sampling for large datasets
- Stratified sampling for balanced evaluation
- Fixed seed for reproducibility
- Consistent problem ordering

### Answer Extraction
- Task-specific extraction methods
- Format validation and normalization
- Error handling for malformed outputs
- Fallback extraction strategies

### Scoring Methodology
- Exact match for factual answers
- Functional correctness for code
- Semantic similarity for explanations
- Multi-reference evaluation where applicable

---

This configuration ensures consistent, reproducible experiments across all benchmark evaluations while maintaining the flexibility to conduct comprehensive ablation studies.
