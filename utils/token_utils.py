#!/usr/bin/env python3
"""
Token计数和文本压缩工具
用于实现注释长度硬约束和自动摘要功能
"""

import re
import asyncio
from typing import Optional, Dict, Any
from utils.api import async_generate_completion

class TokenCounter:
    """简单的token计数器"""
    
    @staticmethod
    def count_tokens(text: str) -> int:
        """
        简单的token计数方法
        使用启发式方法：大约4个字符=1个token（英文），中文字符约1.5个字符=1个token
        """
        if not text:
            return 0
        
        # 分别计算中英文字符
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        english_chars = len(text) - chinese_chars
        
        # 估算token数量
        estimated_tokens = int(chinese_chars * 0.67 + english_chars * 0.25)
        return max(1, estimated_tokens)
    
    @staticmethod
    def is_over_limit(text: str, max_tokens: int) -> bool:
        """检查文本是否超过token限制"""
        return TokenCounter.count_tokens(text) > max_tokens

class TextCompressor:
    """文本压缩和摘要工具"""
    
    def __init__(self, default_agent: str = "openai"):
        self.default_agent = default_agent
    
    async def compress_annotation(self, annotation_text: str, max_tokens: int, 
                                annotation_type: str = "suggestion", 
                                agent_id: Optional[str] = None) -> Dict[str, Any]:
        """
        压缩注释文本到指定token限制内
        
        Args:
            annotation_text: 原始注释文本
            max_tokens: 最大token数量
            annotation_type: 注释类型
            agent_id: 使用的agent ID
            
        Returns:
            包含压缩结果的字典
        """
        if not agent_id:
            agent_id = self.default_agent
        
        current_tokens = TokenCounter.count_tokens(annotation_text)
        
        if current_tokens <= max_tokens:
            return {
                "compressed_text": annotation_text,
                "original_tokens": current_tokens,
                "compressed_tokens": current_tokens,
                "compression_ratio": 1.0,
                "was_compressed": False
            }
        
        # 构建压缩prompt
        compression_prompt = self._build_compression_prompt(
            annotation_text, max_tokens, annotation_type
        )
        
        try:
            compressed_text = await async_generate_completion(
                agent_id=agent_id,
                prompt=compression_prompt,
                system_prompt="You are a text compression expert. Preserve key information while reducing length.",
                temperature=0.3,
                max_tokens=max_tokens + 20,  # 给一点缓冲
                skip_cache=True
            )
            
            if not compressed_text:
                # 如果压缩失败，使用简单截断
                compressed_text = self._simple_truncate(annotation_text, max_tokens)
            
            compressed_tokens = TokenCounter.count_tokens(compressed_text)
            
            return {
                "compressed_text": compressed_text.strip(),
                "original_tokens": current_tokens,
                "compressed_tokens": compressed_tokens,
                "compression_ratio": compressed_tokens / current_tokens if current_tokens > 0 else 1.0,
                "was_compressed": True
            }
            
        except Exception as e:
            print(f"Warning: Compression failed ({e}), using simple truncation")
            compressed_text = self._simple_truncate(annotation_text, max_tokens)
            compressed_tokens = TokenCounter.count_tokens(compressed_text)
            
            return {
                "compressed_text": compressed_text,
                "original_tokens": current_tokens,
                "compressed_tokens": compressed_tokens,
                "compression_ratio": compressed_tokens / current_tokens if current_tokens > 0 else 1.0,
                "was_compressed": True,
                "compression_method": "truncation"
            }
    
    def _build_compression_prompt(self, text: str, max_tokens: int, annotation_type: str) -> str:
        """构建压缩prompt"""
        
        type_instructions = {
            "suggestion": "Focus on the core suggestion and its rationale.",
            "correction": "Preserve the error identification and the correct solution.",
            "question": "Keep the essential question and context needed to understand it.",
            "enhancement": "Maintain the key improvement idea and its benefits."
        }
        
        instruction = type_instructions.get(annotation_type, "Preserve the most important information.")
        
        return f"""Please compress the following {annotation_type} annotation to approximately {max_tokens} tokens while preserving its essential meaning.

{instruction}

Original annotation:
{text}

Requirements:
- Keep the core message intact
- Remove redundant words and phrases
- Use concise language
- Maintain clarity and actionability
- Target length: ~{max_tokens} tokens

Compressed annotation:"""

    def _simple_truncate(self, text: str, max_tokens: int) -> str:
        """简单截断方法作为备选"""
        if not text:
            return ""
        
        # 估算需要保留的字符数
        target_chars = max_tokens * 4  # 粗略估算
        
        if len(text) <= target_chars:
            return text
        
        # 尝试在句子边界截断
        truncated = text[:target_chars]
        
        # 寻找最后一个句号、问号或感叹号
        last_sentence_end = max(
            truncated.rfind('.'),
            truncated.rfind('!'),
            truncated.rfind('?'),
            truncated.rfind('。'),
            truncated.rfind('！'),
            truncated.rfind('？')
        )
        
        if last_sentence_end > target_chars * 0.7:  # 如果句子边界不太远
            return truncated[:last_sentence_end + 1]
        else:
            # 否则在词边界截断
            last_space = truncated.rfind(' ')
            if last_space > target_chars * 0.8:
                return truncated[:last_space] + "..."
            else:
                return truncated + "..."

class AnnotationLengthController:
    """注释长度控制器"""
    
    def __init__(self, max_tokens: int = 100, compression_agent: str = "openai"):
        self.max_tokens = max_tokens
        self.compressor = TextCompressor(compression_agent)
    
    async def process_annotation(self, annotation_text: str, annotation_type: str = "suggestion",
                               agent_id: str = "system") -> Dict[str, Any]:
        """
        处理注释，确保长度在限制内
        
        Returns:
            处理结果，包含最终文本和统计信息
        """
        if not annotation_text or not annotation_text.strip():
            return {
                "final_text": "",
                "processing_info": {
                    "original_tokens": 0,
                    "final_tokens": 0,
                    "was_processed": False,
                    "processing_method": "empty_input"
                }
            }
        
        original_tokens = TokenCounter.count_tokens(annotation_text)
        
        if original_tokens <= self.max_tokens:
            return {
                "final_text": annotation_text.strip(),
                "processing_info": {
                    "original_tokens": original_tokens,
                    "final_tokens": original_tokens,
                    "was_processed": False,
                    "processing_method": "no_processing_needed"
                }
            }
        
        # 需要压缩
        compression_result = await self.compressor.compress_annotation(
            annotation_text, self.max_tokens, annotation_type, agent_id
        )
        
        return {
            "final_text": compression_result["compressed_text"],
            "processing_info": {
                "original_tokens": compression_result["original_tokens"],
                "final_tokens": compression_result["compressed_tokens"],
                "was_processed": True,
                "processing_method": "llm_compression" if compression_result["was_compressed"] else "no_compression",
                "compression_ratio": compression_result["compression_ratio"]
            }
        }
    
    def validate_length(self, text: str) -> Dict[str, Any]:
        """验证文本长度"""
        tokens = TokenCounter.count_tokens(text)
        return {
            "tokens": tokens,
            "is_valid": tokens <= self.max_tokens,
            "excess_tokens": max(0, tokens - self.max_tokens),
            "utilization": min(1.0, tokens / self.max_tokens)
        }

# 便捷函数
async def ensure_annotation_length(annotation_text: str, max_tokens: int = 100, 
                                 annotation_type: str = "suggestion",
                                 compression_agent: str = "openai") -> str:
    """
    确保注释长度在限制内的便捷函数
    
    Returns:
        处理后的注释文本
    """
    controller = AnnotationLengthController(max_tokens, compression_agent)
    result = await controller.process_annotation(annotation_text, annotation_type)
    return result["final_text"]

def count_text_tokens(text: str) -> int:
    """计算文本token数量的便捷函数"""
    return TokenCounter.count_tokens(text)
