#!/usr/bin/env python3
"""
Token counting and text compression tools
For implementing annotation length constraints and automatic summarization
"""

import re
import asyncio
from typing import Optional, Dict, Any
from utils.api import async_generate_completion

class TokenCounter:
    """Simple token counter"""
    
    @staticmethod
    def count_tokens(text: str) -> int:
        """
        Simple token counting method
        Uses heuristic: approximately 4 chars = 1 token (English), Chinese chars ~1.5 chars = 1 token
        """
        if not text:
            return 0
        
        # Calculate Chinese and English characters separately
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        english_chars = len(text) - chinese_chars
        
        # Estimate token count
        estimated_tokens = int(chinese_chars * 0.67 + english_chars * 0.25)
        return max(1, estimated_tokens)
    
    @staticmethod
    def is_over_limit(text: str, max_tokens: int) -> bool:
        """Check if text exceeds token limit"""
        return TokenCounter.count_tokens(text) > max_tokens

class TextCompressor:
    """Text compression and summarization tool"""
    
    def __init__(self, default_agent: str = "openai"):
        self.default_agent = default_agent
    
    async def compress_annotation(self, annotation_text: str, max_tokens: int, 
                                annotation_type: str = "suggestion", 
                                agent_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Compress annotation text to specified token limit

        Args:
            annotation_text: Original annotation text
            max_tokens: Maximum token count
            annotation_type: Annotation type
            agent_id: Agent ID to use

        Returns:
            Dictionary containing compression results
        """
        if not agent_id:
            agent_id = self.default_agent
        
        current_tokens = TokenCounter.count_tokens(annotation_text)
        
        if current_tokens <= max_tokens:
            return {
                "compressed_text": annotation_text,
                "original_tokens": current_tokens,
                "compressed_tokens": current_tokens,
                "compression_ratio": 1.0,
                "was_compressed": False
            }
        
        # Build compression prompt
        compression_prompt = self._build_compression_prompt(
            annotation_text, max_tokens, annotation_type
        )
        
        try:
            compressed_text = await async_generate_completion(
                agent_id=agent_id,
                prompt=compression_prompt,
                system_prompt="You are a text compression expert. Preserve key information while reducing length.",
                temperature=0.3,
                max_tokens=max_tokens + 20,  # Give some buffer
                skip_cache=True
            )
            
            if not compressed_text:
                # If compression fails, use simple truncation
                compressed_text = self._simple_truncate(annotation_text, max_tokens)
            
            compressed_tokens = TokenCounter.count_tokens(compressed_text)
            
            return {
                "compressed_text": compressed_text.strip(),
                "original_tokens": current_tokens,
                "compressed_tokens": compressed_tokens,
                "compression_ratio": compressed_tokens / current_tokens if current_tokens > 0 else 1.0,
                "was_compressed": True
            }
            
        except Exception as e:
            print(f"Warning: Compression failed ({e}), using simple truncation")
            compressed_text = self._simple_truncate(annotation_text, max_tokens)
            compressed_tokens = TokenCounter.count_tokens(compressed_text)
            
            return {
                "compressed_text": compressed_text,
                "original_tokens": current_tokens,
                "compressed_tokens": compressed_tokens,
                "compression_ratio": compressed_tokens / current_tokens if current_tokens > 0 else 1.0,
                "was_compressed": True,
                "compression_method": "truncation"
            }
    
    def _build_compression_prompt(self, text: str, max_tokens: int, annotation_type: str) -> str:
        """Build compression prompt"""
        
        type_instructions = {
            "suggestion": "Focus on the core suggestion and its rationale.",
            "correction": "Preserve the error identification and the correct solution.",
            "question": "Keep the essential question and context needed to understand it.",
            "enhancement": "Maintain the key improvement idea and its benefits."
        }
        
        instruction = type_instructions.get(annotation_type, "Preserve the most important information.")
        
        return f"""Please compress the following {annotation_type} annotation to approximately {max_tokens} tokens while preserving its essential meaning.

{instruction}

Original annotation:
{text}

Requirements:
- Keep the core message intact
- Remove redundant words and phrases
- Use concise language
- Maintain clarity and actionability
- Target length: ~{max_tokens} tokens

Compressed annotation:"""

    def _simple_truncate(self, text: str, max_tokens: int) -> str:
        """Simple truncation method as fallback"""
        if not text:
            return ""
        
        # Estimate number of characters to keep
        target_chars = max_tokens * 4  # Rough estimation
        
        if len(text) <= target_chars:
            return text
        
        # Try to truncate at sentence boundaries
        truncated = text[:target_chars]
        
        # Find the last period, question mark, or exclamation mark
        last_sentence_end = max(
            truncated.rfind('.'),
            truncated.rfind('!'),
            truncated.rfind('?'),
            truncated.rfind('。'),
            truncated.rfind('！'),
            truncated.rfind('？')
        )
        
        if last_sentence_end > target_chars * 0.7:  # If sentence boundary is not too far
            return truncated[:last_sentence_end + 1]
        else:
            # Otherwise truncate at word boundary
            last_space = truncated.rfind(' ')
            if last_space > target_chars * 0.8:
                return truncated[:last_space] + "..."
            else:
                return truncated + "..."

class AnnotationLengthController:
    """Annotation length controller"""
    
    def __init__(self, max_tokens: int = 100, compression_agent: str = "openai"):
        self.max_tokens = max_tokens
        self.compressor = TextCompressor(compression_agent)
    
    async def process_annotation(self, annotation_text: str, annotation_type: str = "suggestion",
                               agent_id: str = "system") -> Dict[str, Any]:
        """
        Process annotation, ensuring length is within limits

        Returns:
            Processing result, including final text and statistics
        """
        if not annotation_text or not annotation_text.strip():
            return {
                "final_text": "",
                "processing_info": {
                    "original_tokens": 0,
                    "final_tokens": 0,
                    "was_processed": False,
                    "processing_method": "empty_input"
                }
            }
        
        original_tokens = TokenCounter.count_tokens(annotation_text)
        
        if original_tokens <= self.max_tokens:
            return {
                "final_text": annotation_text.strip(),
                "processing_info": {
                    "original_tokens": original_tokens,
                    "final_tokens": original_tokens,
                    "was_processed": False,
                    "processing_method": "no_processing_needed"
                }
            }
        
        # Need compression
        compression_result = await self.compressor.compress_annotation(
            annotation_text, self.max_tokens, annotation_type, agent_id
        )
        
        return {
            "final_text": compression_result["compressed_text"],
            "processing_info": {
                "original_tokens": compression_result["original_tokens"],
                "final_tokens": compression_result["compressed_tokens"],
                "was_processed": True,
                "processing_method": "llm_compression" if compression_result["was_compressed"] else "no_compression",
                "compression_ratio": compression_result["compression_ratio"]
            }
        }
    
    def validate_length(self, text: str) -> Dict[str, Any]:
        """Validate text length"""
        tokens = TokenCounter.count_tokens(text)
        return {
            "tokens": tokens,
            "is_valid": tokens <= self.max_tokens,
            "excess_tokens": max(0, tokens - self.max_tokens),
            "utilization": min(1.0, tokens / self.max_tokens)
        }

# Convenience functions
async def ensure_annotation_length(annotation_text: str, max_tokens: int = 100, 
                                 annotation_type: str = "suggestion",
                                 compression_agent: str = "openai") -> str:
    """
    Convenience function to ensure annotation length is within limits

    Returns:
        Processed annotation text
    """
    controller = AnnotationLengthController(max_tokens, compression_agent)
    result = await controller.process_annotation(annotation_text, annotation_type)
    return result["final_text"]

def count_text_tokens(text: str) -> int:
    """Convenience function to count text tokens"""
    return TokenCounter.count_tokens(text)
