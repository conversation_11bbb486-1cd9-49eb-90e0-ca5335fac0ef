#!/usr/bin/env python3
"""
Simple Cache System
Lightweight cache system, replacing complex memory_system
"""

import time
import threading
from typing import Dict, Any, Optional
from config import get_config


class SimpleCache:
    
    def __init__(self):
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.lock = threading.RLock()
        
        self.max_size = get_config("max_cache_size", 1000)
        self.default_ttl = get_config("cache_ttl", 3600)
    
    def put(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        if ttl is None:
            ttl = self.default_ttl
        
        with self.lock:
            self.cache[key] = {
                'value': value,
                'timestamp': time.time(),
                'ttl': ttl
            }
            
            self._cleanup()
            
            if len(self.cache) > self.max_size:
                self._evict_old()
            
            return True
    
    def get(self, key: str) -> Optional[Any]:
        with self.lock:
            if key not in self.cache:
                return None
            
            item = self.cache[key]
            current_time = time.time()
            
            if current_time - item['timestamp'] > item['ttl']:
                del self.cache[key]
                return None
            
            return item['value']
    
    def clear(self) -> bool:
        with self.lock:
            self.cache.clear()
            return True
    
    def size(self) -> int:
        with self.lock:
            return len(self.cache)
    
    def _cleanup(self):
        current_time = time.time()
        expired_keys = [
            key for key, item in self.cache.items()
            if current_time - item['timestamp'] > item['ttl']
        ]
        
        for key in expired_keys:
            del self.cache[key]
    
    def _evict_old(self):
        sorted_items = sorted(
            self.cache.items(),
            key=lambda x: x[1]['timestamp']
        )
        
        keep_count = int(self.max_size * 0.8)
        for key, _ in sorted_items[:-keep_count]:
            del self.cache[key]


_cache_instance = None

def get_cache() -> SimpleCache:
    global _cache_instance
    if _cache_instance is None:
        _cache_instance = SimpleCache()
    return _cache_instance